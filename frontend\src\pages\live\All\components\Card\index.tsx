/*
 * @Author: spring breeze
 * @Date: 2023-07-05 10:15:58
 * @LastEditTime: 2023-07-05 10:15:58
 * @FilePath: \frontend\src\pages\live\components\Card\index.tsx
 * @Description:
 */
import React, { useMemo } from 'react';
import style from './index.less';
import { Button, Image, Popover, Tooltip } from 'antd';
import { UserOutlined } from '@ant-design/icons';
import defaultImg from '@/images/live/defaultImg.png';
import { ReactComponent as Fire } from '@/images/live/fire.svg';
import { LiveService } from '@/api/live';
import { ClassNames } from '@/utils/utils';
import moment from 'moment';
import Access from '@/components/Access';
import { CardProps } from '../../../components/LegendMode';
import Ellipsis from '@/components/Ellipsis';

const PERMISSION = {
  public: '社会公开',
  school: '校内公开',
  password: '需要密码加入',
  school_custom: '校内自定义范围',
};

const STATUS = {
  1: '未开始',
  2: '直播中',
  3: '已结束',
  4: '已关闭',
  5: '异常',
};
const STATUS_STYLE = {
  1: '',
  2: style.doing,
  3: style.done,
  4: style.done,
  5: style.error,
};
type PERMISSION_KEY = keyof typeof PERMISSION;
type STATUS_KEY = keyof typeof STATUS;
type STATUS_STYLE_KEY = keyof typeof STATUS_STYLE;
const Card: React.FC<CardProps<any>> = ({ buttonsRender, item }) => {
  return (
    <div className={style.card}>
      <div className={style.left}>
        <div className={style.img}>
          <img
            draggable={false}
            src={defaultImg}
            onError={e => {
              e.currentTarget.src = defaultImg;
            }}
          />
          <span
            className={ClassNames(
              style.status,
              STATUS_STYLE[item?.task_status as STATUS_STYLE_KEY],
            )}
          >
            {item?.task_status_msg}
          </span>
          {/* <Access accessible={look_back_rendering}>
            <div className={style.look_back_rendering}>回看生成中</div>
          </Access> */}
        </div>
      </div>
      <div className={style.right}>
        <div className={style.title} role="h1">
          <h3>
            <Ellipsis text={item?.course_name} />
          </h3>
          <div className={style.btns}>{buttonsRender?.(item)}</div>
        </div>
        <div className={style.content}>
          <div className={ClassNames(style.time)}>{item?.semester}</div>
        </div>
        <div className={style.bottom}>
          <span className={style.bottomLeft}>
            <Ellipsis text={item?.teacher_name} />
          </span>
          <span className={style.bottomRight}>
            <Fire width={16} />
            {item?.view_numbers}观看
          </span>
        </div>
      </div>
    </div>
  );
};

export default Card;
Card.displayName = 'Card';
