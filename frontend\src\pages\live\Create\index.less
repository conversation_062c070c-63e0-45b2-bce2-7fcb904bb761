.create {
  padding: 24px;
}
.full {
  width: 100%;
  height: 100%;
}
.upload {
  width: 160px;
  height: 90px;

  .uploadChild {
    .full();
    position: relative;
    img {
      .full();
      position: relative;
    }
    .img {
      &::after {
        content: '';
        color: white;
        transition: all 0.5s ease;
      }
      &:hover {
        &::after {
          // 覆盖的modal
          content: '重新上传';
          color: white;
          text-align: center;
          position: absolute;
          top: 0;
          left: 0;
          width: 160px;
          height: 90px;
          line-height: 90px;
          z-index: 2;
          background-color: rgba(0, 0, 0, 0.3);
        }
      }
    }
    .btn {
      position: absolute;
      bottom: 0;
      // margin right自己本身的宽度
      right: -108px;
    }
  }
}

.rapid {
  padding: 24px;
  box-shadow: 0 2px 8px 2px hsla(0, 0%, 50.6%, 0.16);
  transition: all 0.3s ease;
  border-radius: 3px;
}

.help {
  white-space: nowrap;
  color: #555;
  font-size: 12px;
  &:last-child {
    margin-bottom: 10px;
  }
}

.inModal{
  box-shadow: none;
  padding: 0;
}