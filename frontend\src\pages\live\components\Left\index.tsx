/*
 * @Author: spring breeze
 * @Date: 2023-07-03 16:07:00
 * @LastEditTime: 2023-07-03 16:07:00
 * @FilePath: \frontend\src\pages\live\components\Left\index.tsx
 * @Description:
 */
import React, { useEffect, useMemo, useRef, useState } from 'react';
import style from './index.less';
import { Tree, TreeDataNode, TreeProps } from 'antd';

import './reset.less';
import { useLocation } from 'react-router';
import { history } from 'umi';
import { useIsAdmin } from '@/hooks';
import { useImmer } from 'use-immer';

const { DirectoryTree } = Tree;

const LiveLeft: React.FC<AppProps> = () => {
  const { pathname } = useLocation();
  // const [selectedKeys, setSelectedKeys] = useState<string[]>([
  //   pathname.slice(6),
  // ]);
  const selectedKeys = useMemo(() => {
    return [pathname.slice(6)];
  }, [pathname]);
  const [expandedKeys, setExpandedKeys] = useState<string[]>(selectedKeys);

  const [treeData, setTreeData] = useImmer<TreeDataNode[]>([
    {
      title: '共享直播',
      key: 'share',
      children: [],
      selectable: true, //禁止选中
    },
    {
      title: '我的直播',
      key: 'my',
      children: [
        {
          title: '我申请的',
          key: 'my/apply',
          isLeaf: true,
          icon: <></>,
        },
        {
          title: '我主讲的',
          key: 'my/speaker',
          isLeaf: true,
          icon: <></>,
        },
        {
          title: '我收藏的',
          key: 'my/collect',
          isLeaf: true,
          icon: <></>,
        },
        {
          title: '我分享的',
          key: 'my/share',
          isLeaf: true,
          icon: <></>,
          // 显示箭头
        },
      ],
      selectable: false, //禁止选中
    },
  ]);
  const countRef = useRef(0);
  const isAdmin = useIsAdmin();
  useEffect(() => {
    if (isAdmin && countRef.current === 0) {
      countRef.current++;
      setTreeData(arg => {
        arg.unshift({
          title: '全部直播',
          key: 'all',
          children: [],
          selectable: true, //禁止选中
        });
      });
    }
  }, [isAdmin]);

  const treeConfig: TreeProps = {
    treeData,
    showIcon: true,
    multiple: false,
    className: style.tree,
    onSelect(selectedKeys, info) {
      const path = `/live/${selectedKeys[0]}`;
      if (path !== pathname) {
        history.push(path);
      }
    },
    selectedKeys,
    expandedKeys,
    onExpand(expandedKeys, info) {
      setExpandedKeys(expandedKeys as string[]);
    },
    // 调整size
  };
  return (
    <div id="live-left" className={style.left}>
      <DirectoryTree {...treeConfig} />
    </div>
  );
};

interface AppProps {}
export default LiveLeft;
LiveLeft.displayName = 'LiveLeft';
