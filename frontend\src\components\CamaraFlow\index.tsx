import { FC, useEffect, useState } from 'react';
import { Form, TreeSelect, Checkbox, Select } from 'antd';
import { MinusCircleOutlined } from '@ant-design/icons';
import './index.less';
import activityManagementService from '@/service/activityManagementService';
import config from '@/utils/config';
const { TreeNode } = TreeSelect;

interface IVideoFlowProps {
  maxLength: number;
  defaultLength: number;
  onAddressesChange: (
    name: any,
    addressed: (ActivityType.IFlowOrigin | undefined)[],
  ) => void;
  areaName: string;
  address: (ActivityType.IFlowOrigin | undefined)[];
}

const CamaraFlow: FC<IVideoFlowProps> = ({ onAddressesChange, address, areaName }) => {
  // const [address, setAddress] = useState<ActivityType.IFlowOrigin[]>([])
  const [areaTree, setAreaTree] = useState<any>(null);
  const [changeStatus, setChangeStatus] = useState(true);
  const [classroomInfo, setClassRoomInfo] = useState({
    id: '',
    screenAddr: '',
    guide_url: '',
    student_full_url:'',
    student_full_has_voice:false,
    student_singl_url:'',
    student_singl_has_voice:false,
    student_trace_url:'',
    student_trace_has_voice:false,
    teacher_full_url:'',
    teacher_full_has_voice:false,
    teacher_singl_url:'',
    teacher_singl_has_voice:false,
    teacher_trace_url:'',
    teacher_trace_has_voice:false,
    name: '',
  });


  // student_full_url:'',
  // student_full_url_flv:'',
  // student_full_has_voice:false,
  // student_singl_url:'',
  // student_singl_url_flv:'',
  // student_singl_has_voice:false,
  // student_trace_url:'',
  // student_trace_url_flv:'',
  // student_trace_has_voice:false,
  // teacher_full_url:'',
  // teacher_full_url_flv:'',
  // teacher_full_has_voice:false,
  // teacher_singl_url:'',
  // teacher_singl_url_flv:'',
  // teacher_singl_has_voice:false,
  // teacher_trace_url:'',
  // teacher_trace_url_flv:'',
  // 教室直播测试

  const handleCheckChange = (e: any, mvIdName: string) => {
    const addresses = [...address];
    const findIndex = addresses.findIndex((item: any) => item.screen_flag === mvIdName);
    setChangeStatus(false);
    if (addresses && addresses[findIndex]) {
      addresses[findIndex].is_select = e.target.checked;
      onAddressesChange(areaName, addresses);
    }
  };
  const handleChangeSelectId = (value: number) => {

    // const camaras = [...address];
    setChangeStatus(true);
    const camaras:any= [];
    fetchFlowAddrById(value).then((data:any) => {
      console.log(data)
      let j =0;
      for(let i in data){
        console.log(i,data[i])
        switch(i){
          case 'ai_url_flv':
            if(data[i]){
              camaras[j]={
                  _id: String(value),
                  stream_address: data[i],
                  push_url: data.ai_url,
                  pull_url: data.ai_url,
                  is_mute:true,
                  screen_flag: i,
                  stream_name:'屏幕画面'
              };j++;break;
            }
          case 'guide_url_flv':
            if(data[i]){
              camaras[j]={
                  _id: String(value),
                  stream_address: data[i],
                  pull_url: data.guide_url,
                  push_url: data.guide_url,
                  is_mute:true,
                  screen_flag: i,
                  stream_name:'导切画面'
              };j++;break;
            }
          case 'student_full_url_flv':
           if(data[i]){
              camaras[j]={
                _id: String(value),
                stream_address: data[i],
                pull_url: data.student_full_url,
                push_url: data.student_full_url,
                is_mute:data.student_full_has_voice,
                screen_flag: i,
                stream_name:'学生全景'
            };j++;break;
           }
          case 'student_singl_url_flv':
           if(data[i]){
              camaras[j]={
                _id: String(value),
                stream_address: data[i],
                pull_url: data.student_singl_url,
                push_url: data.student_singl_url,
                is_mute:data.student_singl_has_voice ,
                screen_flag: i,
                stream_name:'学生画面'
            };j++;break;
           }
          case 'student_trace_url_flv':
           if(data[i]){
              camaras[j]={
                _id: String(value),
                stream_address: data[i],
                pull_url: data.student_trace_url,
                push_url: data.student_trace_url,
                is_mute:data.student_trace_has_voice,
                screen_flag: i,
                stream_name:'学生近景'
            };j++;break;
           }
          case 'teacher_full_url_flv':
           if(data[i]){
              camaras[j]={
                _id: String(value),
                stream_address: data[i],
                pull_url: data.teacher_full_url,
                push_url: data.teacher_full_url,
                is_mute:data.teacher_full_has_voice,
                screen_flag: i,
                stream_name:'老师全景'
            };j++;break;
          }
          case 'teacher_singl_url_flv':
           if(data[i]){
              camaras[j]={
                _id: String(value),
                stream_address: data[i],
                pull_url: data.teacher_singl_url,
                push_url: data.teacher_singl_url,
                is_mute:data.teacher_singl_has_voice,
                screen_flag: i,
                stream_name:'老师画面'
            };j++;break;
          }
          case 'teacher_trace_url_flv':
            if(data[i]){
                camaras[j]={
                  _id: String(value),
                  stream_address: data[i],
                  pull_url: data.teacher_trace_url,
                  push_url: data.teacher_trace_url,
                  is_mute:data.teacher_trace_has_voice,
                  screen_flag: i,
                  stream_name:'老师近景'
              };j++;break;
            }
          }
      }
      // camaras[0] = {
      //   _id: String(value),
      //   stream_address: data?.ai_url ?? '',
      //   push_url: '',
      // };
      // camaras[1] = {
      //   _id: String(value),
      //   stream_address: data?.teacher_url ?? '',
      //   push_url: '',
      // };
      console.log(camaras)
      onAddressesChange(data?.name, camaras);
    });
  };

  async function fetchAreas() {
    const res = await activityManagementService.fetchAreaTree();
    if (res.error_code === config.successCodeUniform) {
      setAreaTree(res.extend_message);
    }
  }

  async function fetchFlowAddrById(id: number) {
    const res = await activityManagementService.fetchFlowAddrsByArea(id);
    if (res.error_code === config.successCodeUniform) {
      return res.extend_message;
    }
  }

  useEffect(() => {
    // init
    console.info('address', address, areaName)
    console.info('areaName', !areaName, areaName)
    if (address && address[0] && changeStatus) {
      const id = address[0]._id;
      fetchFlowAddrById(Number(id)).then((data) => {
        onAddressesChange(data?.name, address);
        setClassRoomInfo({
          id: String(data?.id) ?? '',
          screenAddr: data?.ai_url_flv ?? '',
          guide_url: data?.guide_url_flv ?? '',
          name: data?.name ?? '',
          student_full_url:data?.student_full_url_flv ?? '',
          student_full_has_voice:data?.student_full_has_voice ?? false,
          student_singl_url:data?.student_singl_url_flv ?? '',
          student_singl_has_voice:data?.student_singl_has_voice ?? false,
          student_trace_url:data?.student_trace_url_flv ?? '',
          student_trace_has_voice:data?.student_trace_has_voice ?? false,
          teacher_full_url:data?.teacher_full_url_flv ?? '',
          teacher_full_has_voice:data?.teacher_full_has_voice ?? false,
          teacher_singl_url:data?.teacher_singl_url_flv ?? '',
          teacher_singl_has_voice:data?.teacher_singl_has_voice ?? false,
          teacher_trace_url:data?.teacher_trace_url_flv ?? '',
          teacher_trace_has_voice:data?.teacher_trace_has_voice ?? false
        });
      });
    }
  }, [address, areaName]);

  useEffect(() => {
    fetchAreas();
  }, []);

  const getInitChecked = (flag: string) => {
    let checked = false;
    address.forEach((item: any) => {
      if (item.screen_flag === flag) {
        checked = item.is_select;
      }
    })

    return checked;
  }

  return (
    <div className="camara_flowlist_container">
      <Form name="address">
        <Form.Item
          label={`选择教室`}
          rules={[{ required: true, message: '请输入视频流地址' }]}
          style={{marginBottom: 0}}
          name="area_name"
        >
          <div className="camara_flowitem_wrapper">
            <div className="camara_selects">
              <TreeSelect
                value={classroomInfo.name}
                dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
                placeholder="请选择教室"
                onChange={(value) => handleChangeSelectId(Number(value))}
              >
                {areaTree?.map((item: any) => {
                  return (
                    <TreeNode
                      key={item?.id}
                      disabled={true}
                      value={item?.id}
                      title={item?.name}
                    >
                      {item?.children?.map((item: any) => (
                        <TreeNode
                          key={item?.id}
                          disabled={true}
                          value={item?.id}
                          title={item?.name}
                        >
                          {item?.children?.map((item: any) => (
                            <TreeNode
                              key={item?.id}
                              value={item?.id}
                              title={item?.name}
                            />
                          ))}
                        </TreeNode>
                      ))}
                    </TreeNode>
                  );
                })}
              </TreeSelect>
            </div>
          </div>
          {address[0] && (
              <div className="camara_flowitem_info">
                <div className='camara_flowitem_info_content'>
                  <div style={{ whiteSpace: 'nowrap' }}>
                    <Checkbox style={{ height: '32px', lineHeight: '32px', marginRight: 5, visibility: classroomInfo.screenAddr ? 'visible' : 'hidden' }} 
                    checked={getInitChecked('ai_url_flv')}
                    onChange={(e: any) => { handleCheckChange(e, 'ai_url_flv') }} />
                    {`屏幕画面：${
                    classroomInfo.screenAddr ? classroomInfo.screenAddr : '无'
                    }`}</div>
                  <div style={{ whiteSpace: 'nowrap' }}>
                    <Checkbox style={{ height: '32px', lineHeight: '32px', marginRight: 5, visibility: classroomInfo.teacher_full_url ? 'visible' : 'hidden' }} 
                    checked={getInitChecked('teacher_full_url_flv')}
                    onChange={(e: any) => { handleCheckChange(e, 'teacher_full_url_flv') }} />
                    {`教师全景${classroomInfo.teacher_full_url?
                    (classroomInfo.teacher_full_has_voice?'(有声)':'(无声)'):''}：${
                    classroomInfo.teacher_full_url ?classroomInfo.teacher_full_url: '无'
                    }`}</div>
                  <div style={{ whiteSpace: 'nowrap' }}>
                    <Checkbox style={{ height: '32px', lineHeight: '32px', marginRight: 5, visibility: classroomInfo.teacher_singl_url ? 'visible' : 'hidden' }} 
                    checked={getInitChecked('teacher_singl_url_flv')}
                    onChange={(e: any) => { handleCheckChange(e, 'teacher_singl_url_flv') }} />
                    {`教师画面${classroomInfo.teacher_singl_url?
                    (classroomInfo.teacher_singl_has_voice?'(有声)':'(无声)'):''}：${
                    classroomInfo.teacher_singl_url ?classroomInfo.teacher_singl_url : '无'
                    }`}</div>
                  <div style={{ whiteSpace: 'nowrap' }}>
                    <Checkbox style={{ height: '32px', lineHeight: '32px', marginRight: 5, visibility: classroomInfo.teacher_trace_url ? 'visible' : 'hidden' }}
                    checked={getInitChecked('teacher_trace_url_flv')} 
                    onChange={(e: any) => { handleCheckChange(e, 'teacher_trace_url_flv') }} />
                    {`教师近景${classroomInfo.teacher_trace_url?
                    (classroomInfo.teacher_trace_has_voice?'(有声)':'(无声)'):''}：${
                    classroomInfo.teacher_trace_url ?classroomInfo.teacher_trace_url: '无'
                    }`}</div>
                </div>
                <div className='camara_flowitem_info_content'>
                  <div style={{ whiteSpace: 'nowrap' }}>
                    <Checkbox style={{ height: '32px', lineHeight: '32px', marginRight: 5, visibility: classroomInfo.guide_url ? 'visible' : 'hidden' }} 
                    checked={getInitChecked('guide_url_flv')}
                    onChange={(e: any) => { handleCheckChange(e, 'guide_url_flv') }} />
                    {`导切画面：${
                    classroomInfo.guide_url ? classroomInfo.guide_url : '无'
                    }`}</div>
                  <div style={{ whiteSpace: 'nowrap' }}>
                    <Checkbox style={{ height: '32px', lineHeight: '32px', marginRight: 5, visibility: classroomInfo.student_full_url ? 'visible' : 'hidden' }} 
                    checked={getInitChecked('student_full_url_flv')}
                    onChange={(e: any) => { handleCheckChange(e, 'student_full_url_flv') }} />
                    {`学生全景${classroomInfo.student_full_url?
                    (classroomInfo.student_full_has_voice?'(有声)':'(无声)'):''}：${
                    classroomInfo.student_full_url ?classroomInfo.student_full_url: '无'
                    }`}</div>
                  <div style={{ whiteSpace: 'nowrap' }}>
                    <Checkbox style={{ height: '32px', lineHeight: '32px', marginRight: 5, visibility: classroomInfo.student_singl_url ? 'visible' : 'hidden' }} 
                    checked={getInitChecked('student_singl_url_flv')}
                    onChange={(e: any) => { handleCheckChange(e, 'student_singl_url_flv') }} />
                    {`学生画面${classroomInfo.student_singl_url?
                    (classroomInfo.student_singl_has_voice?'(有声)':'(无声)'):''}：${
                    classroomInfo.student_singl_url ?classroomInfo.student_singl_url : '无'
                    }`}</div>
                  <div style={{ whiteSpace: 'nowrap' }}>
                    <Checkbox style={{ height: '32px', lineHeight: '32px', marginRight: 5, visibility: classroomInfo.student_trace_url ? 'visible' : 'hidden' }} 
                    checked={getInitChecked('student_trace_url_flv')} 
                    onChange={(e: any) => { handleCheckChange(e, 'student_trace_url_flv') }} />
                    {`学生近景${classroomInfo.student_trace_url?
                    (classroomInfo.student_trace_has_voice?'(有声)':'(无声)'):''}：${
                    classroomInfo.student_trace_url ?classroomInfo.student_trace_url: '无'
                    }`}</div>
                </div>
              </div>
            )}
        </Form.Item>
      </Form>
    </div>
  );
};

export default CamaraFlow;
