import React, { useState, useEffect } from 'react';
import moment from 'moment';
import {
  Modal,
  Progress,
  Button,
  Spin,
  message,
  Table,
  Form,
  Input,
  Select,
} from 'antd';
import './index.less';
import PlanSearch from '@/components/PlanSearch';
import TaskDetail from '@/components/TaskDetail';
import courseManagementService from '@/service/courseManagementService';
import { Link, history } from 'umi';
import { areaStreamUrls } from '@/service/live';
import { STREAM_NAME_MAP } from '@/constant/Stream';
import { pick, removeEmpty } from '@/utils/utils';
const { Option } = Select;
const TaskList: React.FC<{}> = () => {
  const [searchform] = Form.useForm();
  const [current, setCurrent] = useState<number>(1); //当前页码
  const [totalPage, setTotalPage] = useState<number>(0); //素材总数
  const [selectedRowKeys, setSelectedRowKeys] = useState<Array<any>>([]);
  const [newSelectedRows, setNewSelectedRows] = useState([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [dataSource, setDataSource] = useState<any>([]);
  const [oneOrBatch, setOneOrBatch] = useState<boolean>(true); //批量或单独
  const [operationData, setOperationData] = useState<any>();

  const [isDeleteModalVisible, setIsDeleteModalVisible] = useState(false); //删除modal
  const [deleteLoading, setDeleteLoading] = useState(false);

  const [isStopModalVisible, setIsStopModalVisible] = useState(false);
  const [isDetailModalVisible, setIsDetailModalVisible] = useState(false);

  const [searchData, setSearchData] = useState<any>();

  useEffect(() => {
    getList();
  }, [current, searchData]);
  const getList = () => {
    setLoading(true);
    let param = `page=${current}&size=${30}&schedule_type=${1}&key=${moment().format(
      'x',
    )}`;
    if (searchData) {
      // console.log(searchData)
      const {
        teacher,
        serialNumber,
        course,
        classroom,
        section,
        week,
        weekly,
        broadcast,
        translate,
        voice,
      } = searchData;
      teacher && (param = param + `&teacher_id=${teacher.split(',')[1]}`);
      serialNumber && (param = param + `&course_no=${serialNumber}`);
      classroom &&
        (param =
          param + `&area=${classroom[classroom.length - 1].split(',')[0]}`);
      section && (param = param + `&course_what=${section}`);
      week && (param = param + `&week=${week}`);
      weekly && (param = param + `&course_weekly=${weekly}`);
      broadcast && (param = param + `&task_status=${broadcast}`);
      translate &&
        (param =
          param + `&voice_translation=${translate === '1' ? false : true}`);
      voice &&
        (param = param + `&voice_identify=${voice === '1' ? false : true}`);
      course.coursedetail &&
        (param =
          param +
          `&${
            course.courseType === 'courseNumber' ? 'course_id' : 'course_name'
          }=${course.coursedetail}`);
    }
    courseManagementService.getScheduleTasks(param).then((res: any) => {
      setLoading(false);
      if (res && res.error_msg === 'Success') {
        setDataSource(res.extend_message.results);
        setTotalPage(res.extend_message.total);
      }
    });
  };
  const columns = [
    {
      title: '课程名称',
      dataIndex: 'course_name',
      key: 'course_name',
      render: (text: any, record: any) => {
        return JSON.parse(record.course_metadata)?.course_name;
      },
    },
    {
      title: '课程号',
      dataIndex: 'course_id',
      key: 'course_id',
      render: (text: any, record: any) => {
        return JSON.parse(record.course_metadata)?.course_id;
      },
    },
    {
      title: '课序号',
      dataIndex: 'course_no',
      key: 'course_no',
      render: (text: any, record: any) => {
        return JSON.parse(record.course_metadata)?.course_no;
      },
    },
    {
      title: '周次',
      dataIndex: 'step_weekly',
      key: 'step_weekly',
    },
    {
      title: '星期',
      dataIndex: 'week',
      key: 'week',
      render: (text: any, record: any) => {
        return JSON.parse(record.course_metadata)?.week;
      },
    },
    {
      title: '节次',
      dataIndex: 'course_what',
      key: 'course_what',
      render: (text: any, record: any) => {
        return JSON.parse(record.course_metadata)?.course_section;
      },
    },
    {
      title: '教师',
      dataIndex: 'teacher_name',
      key: 'teacher_name',
      render: (text: any, record: any) => {
        return JSON.parse(record.course_metadata)?.teacher_name;
      },
    },
    {
      title: '教室',
      dataIndex: 'area_name',
      key: 'area_name',
      render: (text: any, record: any) => {
        return JSON.parse(record.course_metadata)?.area_name;
      },
    },
    {
      title: '状态',
      dataIndex: 'task_status_msg',
      key: 'task_status_msg',
    },
    {
      title: '语音识别状态',
      dataIndex: 'voice_identify',
      key: 'voice_identify',
      render: (text: any, record: any) => {
        return text ? (
          <a style={{ color: 'green' }}>开启 </a>
        ) : (
          <a style={{ color: 'red' }}>关闭</a>
        );
      },
    },
    {
      title: '翻译转写状态',
      dataIndex: 'voice_translation',
      key: 'voice_translation',
      render: (text: any, record: any) => {
        return text ? (
          <a style={{ color: 'green' }}>开启 </a>
        ) : (
          <a style={{ color: 'red' }}>关闭</a>
        );
      },
    },
    {
      title: '操作',
      dataIndex: 'operation',
      key: 'operation',
      width: '220px',
      render: (_text: any, record: any) => {
        return (
          <div className="operation">
            <Button
              type="link"
              onClick={() => {
                setOperationData(record);
                setIsDetailModalVisible(true);
              }}
            >
              查看详情
            </Button>
            {record.task_status === 2 && (
              <Button
                type="link"
                onClick={() => {
                  window.open(`/learn/live/course/${record._id}`);
                }}
              >
                预览
              </Button>
            )}
            {record.task_status === 2 ? (
              <Button
                type="link"
                onClick={() => {
                  setOperationData(record);
                  setIsStopModalVisible(true);
                }}
              >
                停止
              </Button>
            ) : (
              <Button
                type="link"
                onClick={() => {
                  setOneOrBatch(true);
                  setOperationData(record);
                  setIsDeleteModalVisible(true);
                }}
              >
                删除
              </Button>
            )}
            {/* <Button
              type="link"
              // disabled={!(record.schedule_status === ActivityConstant.liveStatus.wait)}
            >
              <Link to={`/mg/live?id=${record._id}`}>直播数据</Link>
            </Button> */}
            {false && (
              <Button
                type="link"
                disabled={![2, 3].includes(record.task_status)}
                onClick={async () => {
                  try {
                    const { task_status, course_metadata } = record;
                    const { area, course_name } = JSON.parse(course_metadata);
                    const { extend_message: data } = await areaStreamUrls({
                      area,
                    });
                    // 去空
                    const notNulData = removeEmpty(
                      pick(data, [
                        ...(Object.keys(STREAM_NAME_MAP) as any),
                        ...Object.keys(STREAM_NAME_MAP).map((v) =>
                          v.slice(0, -4),
                        ),
                      ]),
                    );
                    // 整理数据将flv和rtmp放在一起
                    const streams: Record<string, any> = {};
                    for (const key in notNulData) {
                      if (
                        Object.prototype.hasOwnProperty.call(notNulData, key)
                      ) {
                        if (Object.keys(STREAM_NAME_MAP).includes(key)) {
                          streams[key] = {
                            flv: notNulData[key],
                            rtmp: notNulData[key.slice(0, -4)],
                          };
                        }
                      }
                    }
                    // 整理数据
                    const address = Object.keys(streams)
                      .filter((v) => Object.keys(STREAM_NAME_MAP).includes(v))
                      .map((key) => {
                        return {
                          name: STREAM_NAME_MAP[
                            key as keyof typeof STREAM_NAME_MAP
                          ],
                          url: streams[key].flv,
                          rtmp: streams[key].rtmp,
                          definition: key,
                        };
                      })
                      .sort((a, b) => {
                        if (a.definition.includes('teacher')) {
                          return -1;
                        } else {
                          return a.name.localeCompare(b.name);
                        }
                      });
                    if (address?.length === 0) {
                      message.error('直播流地址为空');
                      return;
                    }
                    history.push({
                      pathname: '/mg/live',
                      state: {
                        startTime: record.start_time,
                        endTime: record.finish_time,
                        address: address,
                        name: course_name,
                      },
                    });
                  } catch (error) {
                    message.error('跳转直播数据失败');
                  }
                }}
              >
                直播数据
              </Button>
            )}
          </div>
        );
      },
    },
  ];
  const rowSelection = {
    onChange: (newSelectedRowKeys: Array<any>, newSelectedRows: any) => {
      setSelectedRowKeys(newSelectedRowKeys);
      setNewSelectedRows(newSelectedRows);
    },
    selectedRowKeys: selectedRowKeys,
  };
  const changepage = (page: number) => {
    setCurrent(page);
  };
  const handleDeleteOk = () => {
    setDeleteLoading(true);
    let param = [];
    if (oneOrBatch) {
      param.push(operationData._id);
    } else {
      param = [...selectedRowKeys];
    }
    courseManagementService
      .deleteScheduleTasks({
        _ids: [...param],
      })
      .then((res: any) => {
        setDeleteLoading(false);
        if (res && res.error_msg === 'Success') {
          message.success('删除成功');
        } else {
          message.error(res ? res.extend_message : '删除失败');
        }
        getList();
        setSelectedRowKeys([]);
        setDeleteLoading(false);
        setIsDeleteModalVisible(false);
      });
  };
  const searchList = () => {
    // console.log(searchform.getFieldsValue())
    setCurrent(1);
    setSearchData(searchform.getFieldsValue());
  };
  const resetData = () => {
    searchform.resetFields();
    searchList();
  };
  const handleStopOk = () => {
    courseManagementService
      .stopScheduleTasks({
        _id: operationData._id,
      })
      .then((res: any) => {
        if (res && res.error_msg === 'Success') {
          message.success('直播停止成功');
        } else {
          message.error(res ? res.extend_message : '直播停止失败');
        }
        getList();
        setIsStopModalVisible(false);
      });
  };
  return (
    <div className="overview">
      <PlanSearch
        formdata={searchform}
        task={true}
        searchList={searchList}
        resetList={resetData}
      />
      <div className="buttom-box">
        {/* <div></div> */}
        <div className="batch-operation">
          <Button
            type="primary"
            disabled={!selectedRowKeys.length}
            onClick={() => {
              setOneOrBatch(false);
              setIsDeleteModalVisible(true);
            }}
          >
            批量删除
          </Button>
        </div>
      </div>
      <Table
        loading={loading}
        dataSource={dataSource}
        columns={columns}
        scroll={{ y: 480 }}
        rowKey="_id"
        rowSelection={{
          ...rowSelection,
        }}
        pagination={{
          current: current,
          showSizeChanger: false,
          defaultPageSize: 30,
          total: totalPage,
          onChange: changepage,
        }}
      />
      <Modal
        title="删除"
        visible={isDeleteModalVisible}
        onOk={handleDeleteOk}
        onCancel={() => setIsDeleteModalVisible(false)}
        confirmLoading={deleteLoading}
      >
        确定要删除该任务吗？
      </Modal>
      <Modal
        title="停止"
        visible={isStopModalVisible}
        onOk={handleStopOk}
        onCancel={() => setIsStopModalVisible(false)}
      >
        确定要停止该直播吗？
      </Modal>
      <TaskDetail
        // modalVisible={true}
        modalVisible={isDetailModalVisible}
        modalClose={() => {
          setIsDetailModalVisible(false);
        }}
        operationData={operationData}
      />
    </div>
  );
};

export default TaskList;
