/*
 * @Author: 冉志诚
 * @Date: 2023-08-10 15:21:21
 * @LastEditTime: 2023-08-10 16:04:30
 * @FilePath: \frontend\src\service\CreateService.ts
 * @Description:
 */

import { request } from 'umi';
import { IResponse, RmanResponse, UpFormResponse } from '.';
import RequestConstant from '@/constant/request';

export namespace CreateService {
  export const auditPage = (params: {
    page: number;
    size: number;
    live_name?: string;
  }) => {
    return request<IResponse<List<AuditPage>>>('/v1/general-schedule/audit/', {
      method: 'get',
      params,
      prefix: RequestConstant.PREFIX.LIVE_MANAGE,
    });
  };
  export type List<T> = {
    total: number;
    size: number;
    page: number;
    results: T[];
  };
  export type AuditPage = {
    _id: string;
    origin_start_time: number | null;
    live_now: boolean;
    dynamic_start_time: number | null;
    schedule_status: number;
    schedule_type: number;
    origin_finish_time: number | null;
    live_address: LiveAddress[] | null;
    live_address_type: number | null;
    online_numbers: number;
    add_time: number;
    update_time: number;
    voice_identify: boolean;
    voice_translation: boolean;
    voice_tts: boolean;
    live_name: string;
    live_introduce: string;
    live_cover: string;
    live_speaker: string;
    live_speaker_id: string;
    live_speaker_trait: null;
    room_id: string;
    area_name: string;
    campus: string;
    academic_building: string;
    classroom_number: string;
    view_numbers: number;
    look_back: boolean;
    look_back_day: number | null;
    save_resource: boolean;
    save_folder: null | string;
    save_folder_name: null;
    look_permission: string;
    look_back_status: number | null;
    create_user_code: string;
    look_back_expire_time: null;
    look_password: null | string;
    is_si: boolean;
    is_share: boolean;
    view_object: null;
    is_series: boolean;
    series_lives: any[];
    look_custom_permission: null | string;
    look_custom_roles: null | string;
    look_custom_course_classes: null;
    look_custom_students: null;
    apply_status: string;
    apply_time: number;
    apply_user_code: string;
    apply_user_name: string;
    audit_reason: null;
    audit_user_code: null;
    audit_user_name: null;
    audit_time: null;
    area_id: number | null;
  };
  export interface LiveAddress {
    stream_name: string;
    push_url: string;
    pull_url: string;
    _id: number;
    is_mute: boolean;
    stream_address: string;
    voice_identify: boolean;
  }
  export const fetchFlowAddessByArea = (id: number) => {
    // return request<Request.IResponse<ActivityType.IFlowClassroom>>('/area_urls', {
    return request<IResponse<FlowAddress>>('/area_voice_urls', {
      prefix: RequestConstant.PREFIX.IPINGESTMAN,
      method: 'GET',
      params: {
        area: id,
      },
    });
  };
  export type SHOW_STREAM_NAME_MAP_KEY = keyof typeof STREAM_NAME_MAP;
  export const STREAM_NAME_MAP = {
    screenAddr: '屏幕画面',
    teacher_full_url_flv: '教师全景',
    teacher_singl_url_flv: '教师单目',
    teacher_trace_url_flv: '教师近景',
    guide_url_flv: '导切画面',
    student_full_url_flv: '学生全景',
    student_singl_url_flv: '学生单目',
    student_trace_url_flv: '学生近景',
  };
  export type FlowAddress = Partial<{
    screenAddr: string;
    ai_url: string;
    guide_url: string;
    teacher_full_url: string;
    teacher_singl_url: string;
    teacher_trace_url: string;
    student_full_url: string;
    student_trace_url: string;
    student_singl_url: string;
    teacher_full_has_voice: boolean;
    teacher_trace_has_voice: boolean;
    teacher_singl_has_voice: boolean;
    student_full_has_voice: boolean;
    student_trace_has_voice: boolean;
    student_singl_has_voice: boolean;
    name: string;
    ai_url_flv: string;
    guide_url_flv: string;
    teacher_full_url_flv: string;
    teacher_trace_url_flv: string;
    teacher_singl_url_flv: string;
    student_full_url_flv: string;
    student_trace_url_flv: string;
    student_singl_url_flv: string;
  }>;
  export interface ApplyDetail {
    _id: string;
    origin_start_time: number;
    live_now: boolean;
    dynamic_start_time: number;
    schedule_status: number;
    schedule_type: number;
    origin_finish_time: number;
    live_address: LiveAddress[];
    live_address_type: number;
    online_numbers: number;
    add_time: number;
    update_time: number;
    voice_identify: boolean;
    voice_translation: boolean;
    voice_tts: boolean;
    live_name: string;
    live_introduce: string;
    series_lives?: {
      live_address: LiveAddress[];
      area_id: number;
      area_name: string;
      start_time: number;
      end_time: number;
      live_address_type: number;
    }[];
    live_cover: string;
    live_speaker: string;
    live_speaker_id: string;
    live_speaker_trait: null;
    room_id: string;
    area_name: string;
    campus: string;
    academic_building: string;
    classroom_number: string;
    view_numbers: number;
    look_back: boolean;
    look_back_day: number;
    save_resource: boolean;
    save_folder: string;
    save_folder_name: null;
    look_permission: string;
    look_back_status: null;
    create_user_code: string;
    look_back_expire_time: null;
    look_password: null;
    is_si: boolean;
    is_share: boolean;
    view_object: null;
    is_series: boolean;
    look_custom_permission: string;
    look_custom_roles: string;
    look_custom_course_classes: null;
    look_custom_students: null;
    apply_status: string;
    apply_time: number;
    apply_user_code: string;
    apply_user_name: string;
    audit_reason: null;
    audit_user_code: null;
    audit_user_name: null;
    audit_time: null;
    area_id: null;
  }
  export const apply = (data: any) => {
    return request<IResponse<null>>('/v1/general-schedule/apply/', {
      method: 'POST',
      data,
      prefix: RequestConstant.PREFIX.LIVE_MANAGE,
    });
  };
  export const patch = (data: any) => {
    return request<IResponse<null>>('/v1/general-schedule/man/patch', {
      method: 'POST',
      data,
      prefix: RequestConstant.PREFIX.LIVE_MANAGE,
    });
  };
  export const getDetail = (params: { _id: string }) => {
    return request<IResponse<ApplyDetail>>('/v1/general-schedule/man/detail/', {
      method: 'get',
      params,
      prefix: RequestConstant.PREFIX.LIVE_MANAGE,
    });
  };
  export const getAreaLive = (params: {
    start_time: number;
    end_time: number;
  }) => {
    return request<IResponse<AreaLiveTreeNode[]>>(
      '/v1/general-schedule/area/live/',
      {
        method: 'get',
        params,
        prefix: RequestConstant.PREFIX.LIVE_MANAGE,
      },
    );
  };
  export type AreaLiveTreeNode = {
    id: number;
    type: string;
    code: string;
    name: string;
    super_area: null;
    total: number;
    children: AreaLiveTreeNode[];
    area_fullname: string;
    is_occupy?: boolean;
  };
  export const getTree = (level: number = 2) => {
    return request<RmanResponse<TreeNode[]>>(`/v1/folder/all/tree`, {
      method: 'GET',
      prefix: RequestConstant.PREFIX.RMAN,
      params: {
        level,
      },
    });
  };
  export interface TreeNode {
    id: string;
    code: null;
    name: string;
    value: string;
    path: string;
    parentId: null;
    layer: number;
    childCount?: number;
    children?: TreeNode[];
  }
  export const uploadCover = (img: string) => {
    return request<IResponse<{ url: string }>>(
      '/v1/general-schedule/man/cover/',
      {
        method: 'POST',
        data: {
          image_base64: img,
        },
        prefix: RequestConstant.PREFIX.LIVE_MANAGE,
      },
    );
  };
  export const getPagingData = (params: {
    school: string;
    pageIndex: number;
    pageSize: number;
    keyword?: string;
    sourceType: 0;
  }) => {
    //获取分页元数据
    return request<UpFormResponse<PagingDataRes>>(
      `/v1/base/data/database/source/data`,
      {
        method: 'GET',
        params,
        prefix: RequestConstant.PREFIX.UP_FORM,
      },
    );
  };
  export interface PagingDataRes {
    pageIndex: number;
    pageTotal: number;
    pageSize: number;
    recordTotal: number;
    results: {
      fieldCode: string;
      fieldValue: string;
      extendedValue: string;
    }[];
  }
  export const onloadTreeLeaf = (params: {
    folderPath: string;
    isOwner?: boolean;
    isChildCount?: boolean;
  }) => {
    return request<RmanResponse<LeafNode[]>>(`/v1/folder/children`, {
      method: 'GET',
      prefix: RequestConstant.PREFIX.RMAN,
      params: {
        isOwner: true,
        isChildCount: true,
        ...params,
      },
    });
  };
  export interface LeafNode {
    showName: string;
    name: string;
    path: string;
    contentId: string;
    parentPath: string;
    childCount: number;
  }
  /**
   * 查询区域-树结构
   */
  export const fetchAreaTree = () => {
    return request<IResponse<AreaTreeNode[]>>('/area_tree/', {
      prefix: RequestConstant.PREFIX.IPINGESTMAN,
      method: 'GET',
    });
  };
  export type AreaTreeNode = {
    id: number;
    type: string;
    code: string;
    name: string;
    super_area: null;
    total: number;
    children: AreaTreeNode[];
    area_fullname: string;
    is_occupy?: boolean;
  };
  /**
   * 查询流地址列表
   */
  export const fetchFlows = function (
    page: number,
    size: number,
    keyword: string,
  ) {
    return request<
      IResponse<{
        results: FlowType[];
        page: number;
        size: number;
        total: number;
      }>
    >(`/v1/stream/man/`, {
      params: {
        str: new Date().getTime(),
        stream_name: keyword,
        page,
        size,
      },
      prefix: RequestConstant.PREFIX.LIVE_MANAGE,
    });
  };
  export type FlowType = {
    _id: string;
    stream_name: string;
    stream_address: string;
    push_url: string;
    pull_url: string;
    stream_status: boolean;
    is_used: boolean;
    create_time: number;
    is_mute: boolean;
  };
  /** 获取某教师在某学期某一周的直播情况 */
  export const userLive = (week: number) => {
    return request<IResponse<UserLiveResponseType[]>>(
      `/v1/general-schedule/user/live/`,
      {
        method: 'GET',
        prefix: RequestConstant.PREFIX.LIVE_MANAGE,
        params: {
          week,
        },
      },
    );
  };

  export interface UserLiveResponseType {
    date: string;
    lives: UserLiveType[];
    week_of_day: number;
  }

  export interface UserLiveType {
    area_id: number;
    area_name: string;
    arrange_type: string;
    end_time: string;
    live_name: string;
    live_no: number;
    start_time: string;
    // 索引位置
    index?: number;
  }
  /**获取当前学期的周次列表 */
  export const semesterWeek = () => {
    return request<IResponse<SemesterWeekType>>(
      `/v1/general-schedule/semester/week/`,
      {
        method: 'GET',
        prefix: RequestConstant.PREFIX.LIVE_MANAGE,
      },
    );
  };

  export type SemesterWeekType = {
    this_week: number;
    weeks: number[];
  };
}
