namespace ActivityConstant {
  export enum liveStatus {
    all = 0,
    wait = 1,
    on = 2,
    end = 3,
    close = 4,
    error = 5
  }
  export enum flowModelType {
    update = 'update',
    add = 'add'
  }
  export enum SpeakerOption {
    choose = 'choose', // 选择用户
    write = 'write'   // 自定义人员
  }

  export enum flowRadioOption {
    classroomFlow = 1, // 教室相机直播流
    customFlow = 2  // 自定义直播流
  }
}
export default ActivityConstant