#live-left {
  .ant-tree {
    font-size: 16px;
    .ant-tree-node-selected {
      color: var(--primary-color);
      background: rgb(240, 249, 255);
      &::before {
        background: rgb(240, 249, 255);
      }
    }
    .ant-tree-treenode-selected {
      &::before {
        background: rgb(240, 249, 255);
      }
    }
    .ant-tree-treenode {
      width: 100%;
      height: 41px;
      line-height: 41px;
      position: relative;
      display: flex;
      align-items: center;
      padding: 3px 0;

      &:hover {
        background: #f5f5f5;
      }
      &::before {
        height: 100%;
      }
      > * {
        height: 100%;
      }
      // // 屏蔽箭头
      // .ant-tree-indent {
      //   display: none !important;
      // }
      // .ant-tree-switcher {
      //   // display: none !important;
      // }

      .ant-tree-node-content-wrapper {
        height: 100%;
        display: flex;
        align-items: center;
        text-align: center;
      }
      .ant-tree-switcher {
        display: flex;
        justify-content: flex-end;
        align-items: center;
      }
    }
  }
}
.reset-npu {
  #live-left {
    transition: none;
    left: 0;
    top: 70px;
  }
}
