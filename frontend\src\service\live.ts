/*
 * @Author: spring breeze
 * @Date: 2023-06-15 16:03:44
 * @LastEditTime: 2023-07-24 16:10:56
 * @FilePath: \frontend\src\service\live.ts
 * @Description:
 */
import RequestConstant from '@/constant/request';
import { request } from 'umi';

export const fetchStatistic = (params: {
  page: number;
  size: number;
  schedule_status?: string;
  live_name?: string;
}) =>
  request(`/v1/general-schedule/man`, {
    params,
  });

export function livestreamStatistic(params: {
  originUrl: string;
  timeType: 'day' | 'hour' | 'minute';
  startCreateTime?: string;
  endCreateTime?: string;
}) {
  return request(`/v1/api/livestream/stream/statistics`, {
    method: 'get',
    params,
    prefix: RequestConstant.module.SOMEDIASERVER,
  }) as Promise<MediaServerResponseContext<StreamStatistic[]>>;
}

export type MediaServerResponseContext<T> = {
  data: T;
  status: number;
  errorCode: null;
  errorMessage?: string;
};
export interface StreamStatistic {
  originUrl: string;
  mediaServerId: string;
  vhost: string;
  app: string;
  stream: string;
  schema: string;
  time: string;
  videoFrameRate: number;
  bytesSpeed: number;
  readerCount: number;
  totalReaderCount: number;
}

export function livestreamInfo(params: {
  originUrl: string;
  timeType: 'day' | 'hour' | 'minute';
  /**是否包含统计信息 */
  containStatistics: boolean;
  /**是否包含异常提醒信息 */
  containAbnormalRemind: boolean;
}) {
  return request(`/v1/api/livestream/stream`, {
    method: 'get',
    params,
    skipErrorHandler: true,
    prefix: RequestConstant.module.SOMEDIASERVER,
  }) as Promise<MediaServerResponseContext<LivestreamInfo>>;
}

export function areaStreamUrls(
  params: Partial<{
    area: number;
    semester: string;
    course_id: string;
    course_no: string;
    course_section: string;
    week: string;
    class_weekly: string[];
  }>,
) {
  return request(`/area_voice_urls`, {
    method: 'get',
    params,
    prefix: RequestConstant.module.ipingestman,
  }) as Promise<IpingestmanResponseContext<AreaStreamUrlsType>>;
}
export function setAreaStreamUrls(
  data: Partial<{
    area_id: number;
    semester: string;
    course_id: string;
    course_no: string;
    course_section: string;
    week: string;
    class_weekly: string[];
    voice_identify_stream: {
      stream_name: string;
      stream_url: string;
    }[];
  }>,
) {
  return request(`/schedule/course_loop/streamset`, {
    method: 'post',
    data,
    prefix: RequestConstant.module.ipingestman,
  }) as Promise<IpingestmanResponseContext<boolean>>;
}

export type AreaStreamUrlsType = Partial<{
  ai_url: string;
  guide_url: string;
  teacher_full_url: string;
  teacher_trace_url: string;
  student_full_url: string;
  student_trace_url: string;
  teacher_full_has_voice: boolean;
  teacher_trace_has_voice: boolean;
  student_full_has_voice: string;
  student_trace_has_voice: string;
  name: string;
  ai_url_flv: string;
  guide_url_flv: string;
  teacher_full_url_flv: string;
  teacher_trace_url_flv: string;
  student_full_url_flv: string;
  student_trace_url_flv: string;
}>;
export type IpingestmanResponseContext<T> = {
  error_code: string;
  error_msg: string;
  extend_message: T;
};
export type LivestreamInfo = Partial<{
  mediaServerId: string;
  vhost: string;
  app: string;
  schema: string;
  stream: string;
  regist: boolean;
  aliveSecond: number;
  bytesSpeed: number;
  createStamp: number;
  originType: string;
  originTypeStr: string;
  originUrl: string;
  readerCount: number;
  totalReaderCount: number;
  isRecordingHLS: boolean;
  isRecordingMP4: boolean;
  videoCodecId: number;
  videoCodecName: string;
  videoFrameRate: number;
  videoHeight: number;
  videoWidth: number;
  audioCodecId: number;
  audioCodecName: string;
  audioSampleRate: number;
  audioSampleBit: number;
  audioTrackNo: number;
  description: null;
  statistics: StreamStatistic[];
  id: number;
  createTime: string;
  createByUser: number;
  state: number;
  updateTime: string;
  updateByUser: number;
}>;
