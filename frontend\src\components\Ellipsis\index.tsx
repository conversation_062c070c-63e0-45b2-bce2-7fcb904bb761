import { Tooltip } from 'antd';
import React from 'react';

/** 返回一个根据省略长度自动截取字符串的tooltip */
const Ellipsis: React.FC<EllipsisProps> = ({ text, omitLen = 5 }) => {
  if (!text) {
    return <>{text}</>;
  }
  return (text?.length ?? 0) > omitLen ? (
    <Tooltip title={text}>{text?.slice(0, omitLen)}...</Tooltip>
  ) : (
    <>{text}</>
  );
};

interface EllipsisProps {
  text?: string;
  omitLen?: number;
}
export default Ellipsis;
Ellipsis.displayName = 'Ellipsis';
