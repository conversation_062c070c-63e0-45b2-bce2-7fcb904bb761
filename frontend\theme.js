/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2021/3/11.
 */
/* jshint esversion: 6 */
const path = require('path');
const { generateTheme } = require('antd-theme-generator');

const options = {
  stylesDir: path.join(__dirname, './src/themes'),
  antDir: path.join(__dirname, './node_modules/antd'),
  varFile: path.join(__dirname, './src/themes/variables.less'),
  mainLessFile: path.join(__dirname, './src/themes/variables.less'),
  themeVariables: [
    //需要动态切换的主题变量
    '@primary-color',
  ],
  indexFileName: 'index.html',
  outputFilePath: path.join(__dirname, './public/theme.less'), //页面引入的主题变量文件
};

generateTheme(options)
  .then(less => {
    console.log('Theme generated successfully');
  })
  .catch(error => {
    console.log('Error', error);
  });
