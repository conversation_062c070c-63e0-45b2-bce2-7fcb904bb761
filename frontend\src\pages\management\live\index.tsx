/*
 * @Author: spring breeze
 * @Date: 2023-06-09 11:11:37
 * @LastEditTime: 2023-06-09 11:11:37
 * @FilePath: \frontend\src\pages\cockpit\Screen\Monitoring\index.tsx
 * @Description:
 */
import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import style from './index.less';
import { Empty, Input, Select, message } from 'antd';
import Charts from '@/components/Echarts';

import dayjs from 'dayjs';
import {
  ClassNames,
  getNoCommonDivisor,
  numToWeek,
  pick,
  removeEmpty,
} from '@/utils/utils';
import { history, useHistory, useLocation } from 'umi';
import { useImmer } from 'use-immer';
import Player, { PlayerRef } from '@/components/Player';
import {
  fetchStatistic,
  livestreamInfo,
  livestreamStatistic,
} from '@/service/live';
import { LeftOutlined } from '@ant-design/icons';
import XGPlayer from 'xgplayer';
import { PlayerUIPlugin, PlayerUIPluginEvents } from '@/plugins/PlayerUIPlugin';
import live from '@/images/live.png';
import video from '@/images/video.png';

const Monitoring: React.FC<AppProps> = () => {
  const [loading, setLoading] = useImmer({
    chart: false,
  });

  const [dataset, setDataset] = useImmer<any>({}); // 数据集
  const [player, setPlayer] = useState<XGPlayer | null>(null);

  const location = useLocation() as unknown as {
    query: {
      startTime: string;
      endTime: string;
      name: string;
      address: string;
    };
  };
  useEffect(() => {
    if (!location.query) {
      message.error('请先选择直播');
      history.push('/mg/activity');
    }
  }, [location.query]);
  useEffect(() => {
    const dom = document.querySelector(
      '.management-layout-wrapper',
    ) as HTMLDivElement;
    if (dom) {
      dom.style.backgroundColor = '#f7f9fa';
    }
    return () => {
      const dom = document.querySelector(
        '.management-layout-wrapper',
      ) as HTMLDivElement;
      if (dom) {
        dom.style.backgroundColor = '';
      }
    };
  });
  const address = JSON.parse(location?.query?.address ?? []) as {
    name: string;
    url: string;
    rtmp: string;
  }[];
  const [streamUrl, setStreamUrl] = useImmer({
    rtmp: address?.[0]?.rtmp ?? '',
    // rtmp: 'rtmp://**************/live/B101',
    hls: address?.[0]?.url ?? '',
  }); // 流地址
  const [totalReaderCount, setTotalReaderCount] = useState(0); // 总观看人数

  const origins = useMemo(() => {
    return address?.map((v) => {
      return {
        name: v.name,
        url: v.url,
        definition: v.name,
        rtmp: v.rtmp,
      };
    });
  }, [location]);

  const [mediaInfo, setMediaInfo] = useImmer<Record<string, any>>({});

  const info = useMemo(() => {
    try {
      const query = location?.query;
      const { name } = query;
      const startTime = dayjs(Number(query?.startTime) * 1000);
      const endTime = dayjs(
        query?.endTime ? Number(query?.endTime) * 1000 : Date.now(),
      );
      let unit = '分钟';
      let duration = endTime.diff(startTime, 'minute');
      if (duration > 1000) {
        unit = '小时';
        duration = endTime.diff(startTime, 'hour');
      }
      console.log(
        '%c [ duration ]-93',
        'font-size:13px; background:pink; color:#bf2c9f;',
        duration,
        location.query,
        Date.now(),
      );
      const endTimeFormatStr =
        startTime.format('YYYY-MM-DD') === endTime.format('YYYY-MM-DD')
          ? 'HH:mm:ss'
          : 'YYYY-MM-DD HH:mm:ss';
      return {
        直播名称: name,
        直播时间: `${startTime.format(
          'YYYY-MM-DD HH:mm:ss',
        )} ~ ${endTime.format(endTimeFormatStr)}`,
        直播时长: `${duration}${unit}`,
        流地址: streamUrl.rtmp,
        ...mediaInfo,
      };
    } catch (error) {
      return {};
    }
  }, [location.query, mediaInfo, streamUrl]);
  const getStreamStatistics = useCallback(async () => {
    if (!streamUrl.rtmp) return;
    setLoading((d) => {
      d.chart = true;
    });
    try {
      //
      const { data } = await livestreamStatistic({
        timeType: 'minute',
        originUrl: streamUrl.rtmp,
        // startCreateTime: dayjs(Number(startTime) * 1000).format(
        //   'YYYY.MM.DD HH:mm:ss',
        // ),
        // endCreateTime: dayjs(Number(endTime) * 1000).format(
        //   'YYYY.MM.DD HH:mm:ss',
        // ),
        startCreateTime: dayjs()
          .subtract(30, 'minute')
          .format('YYYY-MM-DD HH:mm:ss'),
        endCreateTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
      });
      const dataset: Record<string, Record<string, number>> = {};
      // 最多显示10条
      data?.forEach((item) => {
        const time = dayjs(item.time).format('YYYY-MM-DD HH:mm:ss');
        // 初始化
        if (!dataset['观看人数']) {
          dataset['观看人数'] = {};
        }
        dataset['观看人数'][time] = item.totalReaderCount;
      });
      console.log(dataset);
      setDataset(dataset);
    } catch (error) {
      // message.error('获取流统计信息失败');
    }
    setLoading((d) => {
      d.chart = false;
    });
  }, [location.query, streamUrl.rtmp]);

  const getInfo = useCallback(async () => {
    if (!streamUrl.rtmp) return;
    try {
      const { data, errorMessage } = await livestreamInfo({
        timeType: 'minute',
        originUrl: streamUrl.rtmp,
        containAbnormalRemind: false,
        containStatistics: false,
      });
      if (!data) {
        throw new Error(errorMessage);
      }
      const map = {
        state: '流状态',
        totalReaderCount: '累计观看人数',
      };
      setTotalReaderCount(data.totalReaderCount ?? 0);
      setMediaInfo((draft: any) => {
        Object.keys(map).forEach((key) => {
          const v = data[key as keyof typeof data];
          type MapKey = keyof typeof map;
          if (key === 'state') {
            draft[map.state] = v ? (
              <span style={{ color: 'green' }}>正常</span>
            ) : (
              <span style={{ color: 'red' }}>异常</span>
            );
          } else draft[map[key as MapKey]] = v;
        });
      });
    } catch (error: any) {
      // console.log(
      //   '%c [ error ]-167',
      //   'font-size:13px; background:pink; color:#bf2c9f;',
      //   error,
      // );
      // message.error(error.message ?? '获取流信息失败');
    }
  }, [location.query, streamUrl.rtmp]);

  useEffect(() => {
    getStreamStatistics();
    getInfo();
  }, [location.query, streamUrl.rtmp]);
  useEffect(() => {
    player?.emit(PlayerUIPluginEvents.UI_TEXT_CHANGE, {
      text: `${totalReaderCount}人观看`,
    });
  }, [totalReaderCount]);
  return (
    <div className={style.monitoring}>
      <header
        onClick={() => {
          history.push('/mg/activity');
        }}
      >
        <LeftOutlined />
        <h1>直播数据详情</h1>
      </header>
      <div className={style.info}>
        <div className={style.media}>
          <h2 className={ClassNames(style.title, style.line)}>
            <img src={video} />
            基本信息
          </h2>
          {Object.keys(info).map((key) => {
            return (
              <div key={key} className={style.infoLine}>
                <div className={style.label}>{key}</div>
                <div className={style.value}>
                  {info[key as keyof typeof info]}
                </div>
              </div>
            );
          })}
        </div>
        <div className={style.video}>
          {origins?.length > 0 ? (
            <Player
              origins={origins}
              onDefinitionChange={(data) => {
                const { index } = data?.to ?? {};
                const { url, definition, rtmp } = origins[index ?? 0];
                setStreamUrl({
                  rtmp: rtmp,
                  hls: url,
                });
              }}
              afterPlayerInit={(player) => {
                setPlayer(player);
              }}
              extraPlugins={[
                {
                  plugin: PlayerUIPlugin,
                  options: {
                    root: document.getElementById('video'),
                  },
                },
              ]}
              option={{
                isLive: true,
              }}
            />
          ) : (
            <Empty
              style={{
                width: '100%',
                height: '100%',
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                flexDirection: 'column',
              }}
              description="暂无数据,该教室未推流"
            />
          )}
        </div>
      </div>
      <div className={style.chart}>
        <h2 className={style.title}>
          <img src={live} />
          直播数据
        </h2>
        <div className={style.chartContent}>
          {Object.keys(dataset ?? {}).length > 0 ? (
            <Charts
              type="line"
              repaint
              data={dataset}
              loading={loading.chart}
              mergeOptions={(o, data) => {
                const defaultSeries = o.series as any[];
                return {
                  ...o,
                  yAxis: [
                    {
                      name: '观看人数',
                      axisLabel: {
                        formatter: '{value} 人',
                      },
                      type: 'value',
                      minInterval: 1, // 只显示整数
                    },
                  ],
                  series: defaultSeries.map((item, index) => {
                    return {
                      ...item,
                      yAxisIndex: index,
                    };
                  }),
                };
              }}
            />
          ) : (
            <Empty />
          )}
        </div>
      </div>
    </div>
  );
};

interface AppProps {}
export default Monitoring;
Monitoring.displayName = 'Monitoring';
