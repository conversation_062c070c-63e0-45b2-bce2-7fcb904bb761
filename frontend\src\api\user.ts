/*
 * @Author: 冉志诚
 * @Date: 2023-08-23 10:16:54
 * @LastEditTime: 2023-08-23 11:43:17
 * @FilePath: \frontend\src\api\user.ts
 * @Description:
 */
import RequestConstant from '@/constant/request';
import request from './request';

async function login(params: any) {
  return request(`/v1/user/login`, {
    method: 'post',
    data: JSON.stringify(params),
    prefix: RequestConstant.PREFIX.UP_FORM,
  });
}

// async function logout() {
//   return request(`/v1/user/loginout`,{
// }
// 统一退出登录
async function logout() {
  return request(`/v1/loginmanage/loginout`, {
    prefix: '/unifiedlogin',
  });
}

/**
 * 获取用户信息
 */
async function info() {
  return request(`/v1/user/current`, {
    prefix: RequestConstant.PREFIX.UP_FORM,
  });
}

async function getAllUser(data: any) {
  return request(`/v1/user/list`, {
    method: 'post',
    data,
    prefix: RequestConstant.PREFIX.UP_FORM,
  });
}

async function getAllUserByOrg(data: any) {
  return request(`/v1/organization/origina/users`, {
    method: 'post',
    data,
    prefix: RequestConstant.PREFIX.UP_FORM,
  });
}
//批量删除检索结果
async function deleteAllUserByOrg(data: any) {
  return request(`/v1/organization/batchdeleteuser`, {
    method: 'post',
    data,
    prefix: RequestConstant.PREFIX.UP_FORM,
  });
}
//查询进度
async function deletePercentage(params: any) {
  return request(`/v1/organization/batchdeleteuser/percentage`, {
    method: 'get',
    params,
    prefix: RequestConstant.PREFIX.UP_FORM,
  });
}
//删除信息下载
async function deleteInfoDownload(params: any) {
  return request(`/v1/organization/batchdeleteuser/download`, {
    method: 'get',
    params,
    prefix: RequestConstant.PREFIX.UP_FORM,
  });
}

async function createUser(data: any) {
  return request(`/v1/user`, {
    method: 'post',
    data,
    prefix: RequestConstant.PREFIX.UP_FORM,
  });
}

async function createUserOriginal(data: any, num: string) {
  return request(`/v1/user/original${num}`, {
    method: 'post',
    data,
    prefix: RequestConstant.PREFIX.UP_FORM,
  });
}

async function updateUserOriginal(data: any, params: any) {
  return request('/unifiedplatform/v1/user/put', {
    method: 'post',
    data,
    params,
  });
}

async function updateUser(data: any) {
  return request(`/v1/user`, {
    method: 'patch',
    data,
    prefix: RequestConstant.PREFIX.UP_FORM,
  });
}

async function deleteUser(userId: number, requestSourceLogger: number | null) {
  return request(`/v1/user/${userId}`, {
    method: 'delete',
    params: { requestSourceLogger },
    prefix: RequestConstant.PREFIX.UP_FORM,
  });
}

async function batchDeleteUser(
  data: string[],
  requestSourceLogger: number | null,
) {
  return request(`/v1/user/batch/delete`, {
    method: 'POST',
    data,
    params: {
      requestSourceLogger,
    },
    prefix: RequestConstant.PREFIX.UP_FORM,
  });
}

async function userMenus() {
  return request(`/v1/user/rolemenu`, {
    prefix: RequestConstant.PREFIX.UP_FORM,
  });
}

async function userDisable(data: Array<string>, disable: boolean) {
  return request(`/v1/user/disable?disable=${disable}`, {
    method: 'post',
    data,
    prefix: RequestConstant.PREFIX.UP_FORM,
  });
}

// 管理员重置密码
async function resetPassword(data: any) {
  return request(`/v1/user/admin/reset/password`, {
    method: 'PUT',
    data,
    prefix: RequestConstant.PREFIX.UP_FORM,
  });
}

async function getfields() {
  return request(`/rman/v1/metadata/resource/basic/data`, {
    method: 'GET',
  });
}
// 用精确到专业的接口
async function getMajorFields() {
  return request(
    `/rman/v1/metadata/config/fields?EntityType=biz_sobey_course&Type=basic&ResourceType=model_sobey_object_entity`,
    {
      method: 'GET',
    },
  );
}

/**
 * 获取用户详细信息
 * @param userCode
 */
async function fetchUserDetail(userCode?: string) {
  return request(`/v1/user/current`, {
    params: {
      userCode,
    },
    prefix: RequestConstant.PREFIX.UP_FORM,
  });
}

/**
 * 更新头像
 * @param url
 */
async function updateUserAvatar(url?: string) {
  return request(`/v1/user/manage/update/avatar?avatarurl=${url}`, {
    prefix: RequestConstant.PREFIX.LEARN,
    method: 'GET',
  });
}
/**
 * 查询导入锁定
 * @param url
 */
async function checkImport(url: string) {
  return request(`/v1/import/lock?type=${url}`, {
    method: 'GET',
    prefix: RequestConstant.PREFIX.UP_FORM,
  });
}
/**
 * 查询导入进度
 * @param url
 */
async function checkImportProgess(url: string) {
  return request(
    `/v1/import/percentage?type=${url}`,

    {
      method: 'GET',
      prefix: RequestConstant.PREFIX.UP_FORM,
    },
  );
}

/**
 * 修改自己密码
 * @param params
 */
async function updatePassword(params: {
  oldPassword: string;
  password: string;
  confirmPassword: string;
}) {
  return request(`/v1/user/manage/update/password`, {
    prefix: RequestConstant.PREFIX.LEARN,

    method: 'POST',
    body: JSON.stringify(params),
  });
}

// export function getfields() {
//     //获取元数据
//     return HTTP.get(`/rman/v1/metadata/resource/basic/data`).then(res => {
//         if (res.status === 200) {
//             return res.data
//         }
//     }).catch(error => {
//         console.error(error)
//     })
// }

/**
 * 批量更新用户组织
 * @param url
 */
async function upDateUserorg(data: any) {
  return request(
    `/v1/user/batch/update/org`,

    {
      method: 'post',
      data,
      prefix: RequestConstant.PREFIX.UP_FORM,
    },
  );
}

/**
 * 批量更新用户角色
 * @param url
 */
async function upDateUserrole(data: any) {
  return request(
    `/v1/user/updates/roles`,

    {
      method: 'post',
      data,
      prefix: RequestConstant.PREFIX.UP_FORM,
    },
  );
}

/**
 * 查询移动进度
 * @param url
 */
async function checkUpdate(taskId: string) {
  return request(
    `/v1/user/task/info?taskId=${taskId}`,

    {
      method: 'get',
      prefix: RequestConstant.PREFIX.UP_FORM,
    },
  );
}

//教室主页相关
/**
 * 添加关注
 * @param fansCode
 * @param teacherCode
 */
async function reqConcernAdd(data: any) {
  return request(`/v1/teacher/homepage/add/fans`, {
    prefix: RequestConstant.PREFIX.LEARN,

    method: 'post',
    data,
  });
}

/**
 * 取消关注
 * @param fansCode
 * @param teacherCode
 */
async function reqConcernCancel(data: any) {
  return request(`/v1/teacher/homepage/delete/fans`, {
    prefix: RequestConstant.PREFIX.LEARN,

    method: 'post',
    data,
  });
}

/**
 * 获取教师主页信息
 * @param teacherCode
 */
async function reqTeacherInfo(data: any) {
  return request(
    `/learn/v1/teacher/homepage/info?teacherCode=${data.teacherCode}`,
    {
      method: 'post',
    },
  );
}

/**
 * 保存教师主页信息
 * @param teacherCode
 */
async function reqSaveTeacherInfo(data: any) {
  return request(`/v1/teacher/homepage/save`, {
    prefix: RequestConstant.PREFIX.LEARN,

    method: 'post',
    data,
  });
}

/**
 * 修改教师主页相关相关信息
 * @param teacherCode
 */
async function reqUpdateTeacherInfo(data: any) {
  return request(`/v1/teacher/homepage/update`, {
    prefix: RequestConstant.PREFIX.LEARN,

    method: 'post',
    data,
  });
}

async function reqMessageList(data: any) {
  return request(
    `/v1/message/notify/self/list`,

    {
      method: 'GET',
      params: data,
      prefix: RequestConstant.PREFIX.UP_FORM,
    },
  );
}

async function reqUnreadMessageCount() {
  return request(
    `/v1/message/notify/unread/count`,

    {
      method: 'GET',
      prefix: RequestConstant.PREFIX.UP_FORM,
    },
  );
}

async function updateMessageRead(data: any) {
  return request(
    `/v1/message/read`,

    {
      method: 'GET',
      params: data,
      prefix: RequestConstant.PREFIX.UP_FORM,
    },
  );
}

async function updateMessagesRead(data: any) {
  return request(
    `/v1/message/read`,

    {
      method: 'POST',
      data,
      prefix: RequestConstant.PREFIX.UP_FORM,
    },
  );
}
async function updateMessagesDelete(data: any) {
  return request(
    `/v1/message/delete`,

    {
      method: 'POST',
      data,
      prefix: RequestConstant.PREFIX.UP_FORM,
    },
  );
}

async function searchOrgList(params: any) {
  return request(
    `/v1/organization/list`,

    {
      method: 'GET',
      params,
      prefix: RequestConstant.PREFIX.UP_FORM,
    },
  );
}

// 查询用户密码配置
async function Getpasswordrule() {
  return request(
    `/v1/setting/password/rule`,

    {
      method: 'GET',
      prefix: RequestConstant.PREFIX.UP_FORM,
    },
  );
}

// 修改密码配置
async function Setpasswordrule(data: any) {
  return request(
    `/v1/setting/password/rule`,

    {
      method: 'post',
      data,
      prefix: RequestConstant.PREFIX.UP_FORM,
    },
  );
}
// 用户解锁
async function unlockUser(data: any) {
  return request(
    `/v1/user/admin/unlock/user`,

    {
      method: 'post',
      data,
      prefix: RequestConstant.PREFIX.UP_FORM,
    },
  );
}
/**
 * 获取 rman全局参数
 *
 */
const reqAccountConfig = () =>
  request(
    `/v1/app/app/module/parameter/list?moduleCode=personal_account_management`,
    {
      prefix: RequestConstant.PREFIX.UP_FORM,
    },
  );

/**
 * 获取用户消息配置
 */
const reqMessageConfig = () =>
  request(`/v1/message/user/message_type`, {
    prefix: RequestConstant.PREFIX.UP_FORM,
  });
/**
 * 设置用户消息配置
 */

const updateMessageConfig = (data: any) => {
  return request(
    `/v1/message/user/message_type`,

    {
      method: 'post',
      data,
      prefix: RequestConstant.PREFIX.UP_FORM,
    },
  );
};

let user = {
  login,
  logout,
  info,
  getAllUser,
  updateUser,
  createUser,
  deleteUser,
  userMenus,
  userDisable,
  getAllUserByOrg,
  deleteAllUserByOrg,
  deletePercentage,
  deleteInfoDownload,
  createUserOriginal,
  batchDeleteUser,
  updateUserOriginal,
  resetPassword,
  getfields,
  getMajorFields,
  fetchUserDetail,
  updateUserAvatar,
  updatePassword,
  checkImport,
  checkImportProgess,
  upDateUserorg,
  checkUpdate,
  upDateUserrole,
  reqConcernAdd,
  reqConcernCancel,
  reqTeacherInfo,
  reqSaveTeacherInfo,
  reqUpdateTeacherInfo,
  reqMessageList,
  reqUnreadMessageCount,
  searchOrgList,
  updateMessageRead,
  Getpasswordrule,
  unlockUser,
  Setpasswordrule,
  updateMessagesRead,
  updateMessagesDelete,
  reqAccountConfig,
  updateMessageConfig,
  reqMessageConfig,
};

export default user;
