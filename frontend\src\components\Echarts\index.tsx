import React, { useEffect, useMemo, useState } from 'react';
import * as echarts from 'echarts';
import { randomColor } from '@/utils/utils';

export type ChartsType = 'line' | 'bar' | 'pie';
const Charts: React.FC<ChartsProps> = React.memo(
  ({ title, type, data, mergeOptions, repaint = false, loading = false }) => {
    const [instance, setInstance] = useState<echarts.ECharts | null>(null);
    const option: echarts.EChartsCoreOption = useMemo(() => {
      const options: Record<ChartsType, echarts.EChartsCoreOption> = new Proxy(
        {
          line: {
            xAxis: {
              type: 'category',
              data: Object.keys(data[Object.keys(data)?.[0] ?? {}] ?? {}),
            },
            yAxis: {
              type: 'value',
            },
            tooltip: {
              trigger: 'axis',
            },
            legend: {
              data: Object.keys(data),
              // top: -10,
              // textStyle: {
              //   fontSize: 10,
              // },
            },
            series: Object.keys(data).map((item) => {
              const color = randomColor();
              return {
                data: Object.values(data[item]),
                type: 'line',
                name: item,
                lineStyle: {
                  shadowOffsetX: 0,
                  shadowOffsetY: 9,
                  shadowBlur: 8,
                  shadowColor: color,
                },
                itemStyle: {
                  color,
                },
              };
            }),
          },
          bar: {},
          pie: {},
        },
        {
          get(target, p, receiver) {
            return (target as any)[p] ?? {};
          },
        },
      );
      return {
        title: {
          text: title,
        },
        grid: {
          left: '3%',
          right: '1%',
          bottom: '10%',
          top: '30px',
          containLabel: true,
        },
        ...options[type],
      };
    }, [title, type, data]);
    useEffect(() => {
      setInstance(
        echarts.init(document.getElementById('content') as HTMLElement),
      );

      return () => {
        // 销毁
        instance?.dispose();
      };
    }, []);
    useEffect(() => {
      const resizeFn = () => {
        setTimeout(() => {
          if (repaint) {
            instance?.resize();
          }
        }, 300);
      };
      window.addEventListener('resize', resizeFn);
      return () => {
        // 回收事件
        window.removeEventListener('resize', resizeFn);
      };
    }, [instance]);
    useEffect(() => {
      if (loading) {
        instance?.showLoading();
      } else {
        instance?.hideLoading();
      }
    }, [loading, instance]);
    useEffect(() => {
      const o = mergeOptions?.(option, data) ?? option;
      instance?.setOption(o);
    }, [option, instance]);

    return (
      <div
        id="content"
        style={{
          width: '100%',
          height: '100%',
        }}
      ></div>
    );
  },
);
/** 折线图类型的数据集 */
type LineData = {
  [x: string]: Record<string, number>;
};
type AllDataStruct = LineData;
interface ChartsProps {
  /** 标题 */
  title?: string;
  /** 图标类型 */
  type: ChartsType;
  /**数据格式 */
  data: AllDataStruct;
  /** 覆盖的配置 */
  mergeOptions?: (
    option: echarts.EChartsCoreOption,
    data: AllDataStruct,
  ) => echarts.EChartsCoreOption;
  /** 当resize是否重绘 */
  repaint?: boolean;
  /** 是否loading */
  loading?: boolean;
}
export default Charts;
Charts.displayName = 'Charts';
