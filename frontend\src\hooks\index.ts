import { PaginationProps } from 'antd';
import { useEffect, useMemo, useState } from 'react';
import { useHistory, useLocation } from 'react-router';
import { IGlobal, useSelector } from 'umi';
import { useImmer } from 'use-immer';

/**
 * @description 自适应rem
 * @param standard 基准比率  以1920为基准分辨率,计算出的rem根值为16px
 */
export const useSelfAdaptation = (standard = 120) => {
  // ...
  useEffect(() => {
    const setRem = () => {
      const htmlWidth =
        document.documentElement.clientWidth || document.body.clientWidth;
      document.documentElement.style.fontSize = `${htmlWidth / standard}px`;
    };
    setRem();
    window.addEventListener('resize', setRem);
    return () => {
      document.documentElement.style.fontSize = '';
      window.removeEventListener('resize', setRem);
    };
  });
};

/**
 * @description 上拉加载
 */
export const usePullUp = (observer: any, callback: Function) => {
  const [isFetching, setIsFetching] = useState(false);

  useEffect(() => {
    observer.current = new MutationObserver((entries) => {
      const target = entries[0].target as HTMLElement;
      if (target.scrollTop + target.clientHeight >= target.scrollHeight) {
        setIsFetching(true);
        callback().then(() => setIsFetching(false));
      }
    });

    observer.current.observe(document.documentElement, {
      childList: true,
      subtree: true,
    });

    return () => observer.current.disconnect();
  }, [callback]);

  return [isFetching];
};

/**
 * @description 修改query的形式导航
 */
export function useQuery<T extends Record<string, any>>() {
  const history = useHistory();
  const location = history.location;

  // 获取查询参数
  const queryParams = useMemo(() => {
    return new URLSearchParams(location.search);
  }, [location.search]);
  const query: T = useMemo(() => {
    const params: any = {};
    for (let param of queryParams.keys()) {
      let value = queryParams.get(param) ?? '';
      try {
        value = JSON.parse(value);
      } catch (error) {}
      params[param as keyof typeof params] = value;
    }
    return params;
  }, [queryParams]);

  // 设置查询参数
  const setQuery = (value: T) => {
    // 解析value设置所有query
    for (let key in value) {
      queryParams.set(key, JSON.stringify(value[key as keyof typeof value]));
    }

    history.replace({
      search: queryParams.toString(),
    });
  };

  return { query, setQuery };
}

/**
 * @description 用于分页的hook
 * @param rest 需要放置在query中的数据
 */
export const useQueryPagination = <T extends Record<string, any>>(
  defaultPagination?: PaginationProps,
  rest?: T,
) => {
  const { query, setQuery } = useQuery<
    T & {
      page?: number;
      pageSize?: number;
    }
  >();

  const pagination = useMemo<PaginationProps>(() => {
    return {
      className: 'hook-pagination',
      pageSize: Number(query.pageSize ?? 10),
      current: Number(query.page ?? 1),
      showTotal: (total) => `共${total}条`,
      // hideOnSinglePage: true,
      onChange(page, pageSize) {
        setQuery({
          ...query,
          page,
          pageSize,
          ...rest,
        });
      },
      ...defaultPagination,
    };
  }, [query, rest]);

  return {
    pagination,
    query,
    setQuery,
  };
};

/** voltanode版本控制 判断当前用户是否是管理员 */
export const useIsAdmin = () => {
  const { userInfo } = useSelector<any, IGlobal>((state) => state.global);

  const managerCode = ['r_sys_manager', 'admin_S1'];
  return userInfo?.roles?.some((item: any) => {
    return managerCode.includes(item.roleCode);
  });
};
