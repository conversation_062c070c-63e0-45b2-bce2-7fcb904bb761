.apply {
  padding: 10px 26px;
  height: 100%;
}
.operation {
  gap: 10px;
  display: flex;
  > * {
    padding-left: 0;
    padding-right: 0;
  }
}
.headerBtns {
  display: flex;
  gap: 20px;
}

.condition {
  display: flex;
  gap: 10px;
  align-items: center;
  > div {
    font-size: 16px;
    cursor: pointer;
    &:hover {
      color: var(--primary-color);
    }
    &.active {
      color: var(--primary-color);
    }
  }
  .item {
    font-size: 14px;
  }
}
