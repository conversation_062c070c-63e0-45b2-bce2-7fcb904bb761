import React, { useEffect, useRef, useState, useMemo } from 'react';
import style from './index.less';
import './reset.less';
import {
  ProForm,
  ProFormCheckbox,
  ProFormDatePicker,
  ProFormDateRangePicker,
  ProFormDependency,
  ProFormInstance,
  ProFormItem,
  ProFormList,
  ProFormProps,
  ProFormRadio,
  ProFormSelect,
  ProFormSwitch,
  ProFormText,
  ProFormTextArea,
  ProFormTreeSelect,
  ProFormUploadButton,
} from '@ant-design/pro-components';
import Header from '../components/Header';
import { Button, Col, Form, Row, Upload, message } from 'antd';
import Crop from 'antd-img-crop';
import defaultImg from '@/images/live/defaultImg.png';
import {
  ClassNames,
  clearAryEmpty,
  ensure,
  fileToBase64,
  omit,
  removeEmpty,
} from '@/utils/utils';
import { LiveService } from '@/api/live';
import { useImmer } from 'use-immer';
import { DataNode } from 'antd/lib/tree';
import ProFormPageSelect from '@/components/ProFormPageSelect';
import StudentModal, { StudentRefType } from './components/StudentModal';
import { useQuery } from '@/hooks';
import Access from '@/components/Access';
import { CalendarOutlined } from '@ant-design/icons';
import CalendarModal, { CalendarRefType } from './components/CalendarModal';
import moment from 'moment';
import { CreateService } from '@/api/CreateService';
import {
  FormContext,
  LIVE_ADDRESS_TYPE,
  generateCustomAddressByLiveAddress,
  getNodeByLabel,
} from './common';
import ActivityConstant from '@/constant/activity';

/** 低版本类型错误  */
const ImgCrop: any = Crop;
const Provider = FormContext.Provider;

const defaultImgUrl = '/learn/workbench/img/v2/live_default_cover.png';

/** 表单布局内容wrapper  */
const FORM_COL = 14;

const Rapid: React.FC<RapidProps> = ({
  inModal,
  readonly: propReadonly,
  id: propId,
  type: propType,
  hideFooter,
}) => {
  const formRef = useRef<ProFormInstance>(null);
  const [coverUrl, setCoverUrl] = useState<string>(defaultImg);
  const [flowAddress, setFlowAddress] = useImmer<LiveService.FlowAddress>({});
  const [treeData, setTreeData] = useImmer<{
    saveFolder: any[];
  }>({
    saveFolder: [],
  });
  const [form] = Form.useForm<
    CreateService.ApplyDetail & Record<string, any>
  >();
  const area_name = Form.useWatch('area_name', form);
  const live_address_type = Form.useWatch('live_address_type', form);
  const custom_live_address = Form.useWatch('custom_live_address', form);
  const series_lives = Form.useWatch('series_lives', { form, preserve: true });

  const readonly = useMemo(() => {
    return propReadonly;
  }, [propReadonly]);
  const [areaTree, setAreaTree] = useState<DataNode[]>([]);
  const { query } = useQuery<{
    type: 'series' | 'rapid';
    id?: string;
  }>();
  const [submitting, setSubmitting] = useState(false);
  const type = useMemo(() => propType || query.type, [query.type, propType]);
  const isRapid = useMemo(() => type === 'rapid', [type]);
  const studentModalRef = useRef<StudentRefType>(null);
  const calendarModalRef = useRef<CalendarRefType>(null);
  const stream_address: CreateService.LiveAddress[] = useMemo(() => {
    if (isRapid) {
      if (live_address_type === LIVE_ADDRESS_TYPE.CLASSROOM) {
        if (Object.keys(flowAddress).length === 0) {
          return [];
        }
        return clearAryEmpty(
          Object.keys(LiveService.STREAM_NAME_MAP).map((key) => {
            const stream_name =
              LiveService.STREAM_NAME_MAP[
                key as LiveService.SHOW_STREAM_NAME_MAP_KEY
              ];
            const url =
              flowAddress[
                key.slice(
                  0,
                  key.length - 4,
                ) as LiveService.SHOW_STREAM_NAME_MAP_KEY
              ];
            const stream_address =
              flowAddress[key as LiveService.SHOW_STREAM_NAME_MAP_KEY];
            const has_voice_key = `${key.slice(
              0,
              key.length - 7,
            )}has_voice` as LiveService.SHOW_STREAM_NAME_MAP_KEY;
            const is_mute = flowAddress[has_voice_key];

            if (!url) {
              return null;
            }
            return {
              stream_name,
              push_url: url,
              pull_url: url,
              //@ts-ignore labelInValue
              _id: area_name?.value,
              is_mute,
              stream_address,
            };
          }),
        );
      } else {
        if (!custom_live_address) return [];
        return clearAryEmpty(custom_live_address).map((item: any) => {
          if (Object.keys(removeEmpty(item)).length === 0) return null;
          const { name, flow } = item;
          return {
            ...flow.item,
            stream_name: name,
            flow_name: flow.item.stream_name,
          };
        });
      }
    } else {
      return series_lives?.at(-1)?.live_address ?? [];
    }
  }, [
    custom_live_address,
    live_address_type,
    area_name,
    flowAddress,
    isRapid,
    series_lives,
  ]);
  const id = useMemo(() => {
    return propId || query.id;
  }, [query.id, propId]);

  const [info, setInfo] = useImmer<Partial<CreateService.ApplyDetail>>({});
  const isSave = useRef(false);
  const proformConfig: ProFormProps = {
    layout: 'horizontal',
    labelCol: { span: 6 },
    wrapperCol: { span: FORM_COL },
    formRef,
    grid: true,
    disabled: readonly,
    // size: 'middle',
    async onFinish(formData) {
      console.log(
        '%c [ formData ]-52',
        'font-size:13px; background:pink; color:#bf2c9f;',
        formData,
      );
      setSubmitting(true);
      try {
        const {
          live_cover,
          live_introduce,
          live_name,
          look_back,
          look_back_day,
          save_resource,
          look_custom_roles,
          look_custom_permission,
          look_permission,
          live_address_type,
          live_speaker,
          speaker,
          area_name,
          is_si,
          voice_identify,
          voice_translation,
          voice_identify_stream,
          look_password,
          is_share,
        } = formData;
        let { origin_start_time, origin_finish_time } = formData;
        const live_address = stream_address;
        if (!live_address || live_address.length === 0) {
          throw new Error('请等待流地址加载完毕');
        }
        if (isRapid) {
          origin_start_time = moment(origin_start_time).valueOf() / 1000;
          if (origin_finish_time) {
            origin_finish_time = moment(origin_finish_time).valueOf() / 1000;
          }
        }

        const computed_speaker = {
          live_speaker: '',
          live_speaker_id: '',
        };
        if (speaker === 'user') {
          const { label, value } = live_speaker;
          computed_speaker.live_speaker = label;
          computed_speaker.live_speaker_id = value;
        } else {
          computed_speaker.live_speaker = live_speaker;
        }
        if (voice_identify) {
          voice_identify_stream?.map((name: string) => {
            const item = live_address.find((item: any) => {
              if (live_address_type === LIVE_ADDRESS_TYPE.CLASSROOM) {
                return item.stream_name === name;
              }
              return item._id === name;
            });
            if (item) {
              item.voice_identify = true;
            }
          });
        }
        let save_folder: string | undefined = undefined;
        if (save_resource) {
          save_folder = formData.save_folder.value;
        }
        // 快速模式的body
        const body: Record<string, any> = removeEmpty({
          origin_start_time,
          origin_finish_time,
          live_cover: live_cover ?? defaultImgUrl,
          live_introduce,
          live_name,
          look_back: !!look_back,
          look_back_day,
          save_resource: !!save_resource,
          save_folder,
          look_custom_roles,
          look_custom_permission,
          look_permission,
          live_address_type,
          live_address,
          ...computed_speaker,
          area_name: area_name?.label,
          is_si: !!is_si,
          voice_identify: !!voice_identify,
          voice_translation: !!voice_translation,
          is_series: !isRapid,
          look_password,
          area_id: area_name?.value,
          is_share,
        });
        if (!isRapid) {
          body.live_address = [];
          body.origin_start_time = moment(origin_start_time).valueOf() / 1000;
          body.series_lives = series_lives;
        }
        console.log(
          '%c [ body ]-78',
          'font-size:13px; background:pink; color:#bf2c9f;',
          body,
        );
        let res;
        // 修改
        if (id) {
          res = await CreateService.patch({
            ...body,
            _id: id,
          });
        } else {
          if (isSave.current) {
            res = await CreateService.save([body]);
          } else {
            res = await CreateService.apply([body]);
          }
        }
        const { error_code, error_msg } = res;
        if (error_code === 'livemanage.0000.0000') {
          const prefix = isSave.current ? '保存' : id ? '修改' : '创建';
          message.success(`${prefix}成功`);
        } else {
          throw new Error(error_msg ?? '创建失败');
        }
      } catch (error) {
        console.error(error);
        message.error((error as any).message ?? '创建失败');
      }
      setSubmitting(false);
    },
    form,
    // 将拿到的数据翻译成form表单需要的数据
    request: async () => {
      const defaultSetting = {
        live_address_type: LIVE_ADDRESS_TYPE.CLASSROOM,
        custom_live_address: [{}],
        speaker: 'user',
        look_permission: 'public',
      };
      if (id) {
        try {
          const { extend_message } = await CreateService.getDetail({
            _id: id,
          });
          const {
            live_speaker_id: unless_speaker_id,
            live_speaker: unless_speaker,
            origin_start_time: unless_origin_start_time,
            origin_finish_time: unless_origin_finish_time,
            live_address,
            live_address_type,
            series_lives,
          } = extend_message;
          setInfo(extend_message);
          // 没时间优化了
          let origin_start_time = unless_origin_start_time
            ? moment(unless_origin_start_time * 1000)
            : undefined;
          const origin_finish_time = unless_origin_finish_time
            ? moment(unless_origin_finish_time * 1000)
            : undefined;
          const speaker = unless_speaker_id ? 'user' : 'custom';
          const live_speaker =
            speaker === 'user'
              ? {
                  label: unless_speaker,
                  value: unless_speaker_id,
                }
              : unless_speaker;
          // 语音识别流的数据回填
          const voice_identify_stream: string[] = [];
          live_address?.forEach((item) => {
            if (item.voice_identify) {
              voice_identify_stream.push(
                live_address_type === LIVE_ADDRESS_TYPE.CLASSROOM
                  ? item.stream_name
                  : item._id.toString(),
              );
            }
          });
          console.log(
            '%c [ voice_identify_stream ]-349',
            'font-size:13px; background:pink; color:#bf2c9f;',
            voice_identify_stream,
          );

          let custom_live_address = undefined;

          if (isRapid) {
            if (live_address_type === LIVE_ADDRESS_TYPE.CUSTOM) {
              custom_live_address =
                generateCustomAddressByLiveAddress(live_address);
            }
          } else {
            // 系列
            const time = series_lives?.at(-1)?.start_time;
            //@ts-ignore 在系列直播类型为string
            origin_start_time = time
              ? moment(time * 1000)?.format('YYYY-MM-DD HH:mm:ss')
              : undefined;
          }
          return {
            ...defaultSetting,
            ...omit(extend_message, [
              'area_name',
              //FIXME 后面修复
              // 'save_folder'
            ]),
            origin_start_time,
            origin_finish_time,
            live_speaker,
            voice_identify_stream,
            custom_live_address,
            speaker,
            series_lives,
          };
        } catch (error) {
          console.error(error);
          return defaultSetting;
        }
      } else {
        return defaultSetting;
      }
    },
    submitter:
      inModal && hideFooter
        ? false
        : {
            render(props, dom) {
              return (
                <div
                  key="footer"
                  style={{
                    display: 'flex',
                    width: '100%',
                    justifyContent: 'center',
                    gap: '10px',
                  }}
                >
                  <Button
                    onClick={() => {
                      formRef.current?.resetFields();
                    }}
                  >
                    重置
                  </Button>
                  <Access accessible>
                    <Button
                      loading={submitting}
                      onClick={() => {
                        isSave.current = true;
                        formRef.current?.submit();
                      }}
                    >
                      保存
                    </Button>
                  </Access>
                  <Button
                    loading={submitting}
                    type="primary"
                    onClick={() => {
                      isSave.current = false;
                      formRef.current?.submit();
                    }}
                  >
                    提交
                  </Button>
                </div>
              );
            },
          },
  };

  const classRoomSelectHelp = useMemo(() => {
    if (Object.keys(flowAddress).length === 0) return '';
    return Object.keys(LiveService.STREAM_NAME_MAP).map((key) => {
      const title =
        LiveService.STREAM_NAME_MAP[
          key as LiveService.SHOW_STREAM_NAME_MAP_KEY
        ];
      const value = flowAddress[key as LiveService.SHOW_STREAM_NAME_MAP_KEY];
      return (
        <p className={style.help} key={key}>
          {title}:{value ?? '无'} <br />
        </p>
      );
    });
  }, [flowAddress]);

  useEffect(() => {
    getSaveFolderTreeData();
  }, []);
  // 处理选择教室直播流情况下 area_name更新触发的事件
  useEffect(() => {
    areaNameOnChange(area_name);
  }, [area_name]);

  useEffect(() => {
    // 在直播源类型为自定义时，清除选中的已经不存在stream_address的地址
    if (live_address_type === LIVE_ADDRESS_TYPE.CUSTOM) {
      // 清除选中的已经不存在stream_address的地址
      const voice_identify_stream: string[] =
        formRef?.current?.getFieldValue?.('voice_identify_stream') ?? [];

      formRef?.current?.setFieldValue?.(
        'voice_identify_stream',
        voice_identify_stream.filter((item) => {
          return (
            stream_address.findIndex((stream) => {
              // if(live_address_type === LIVE_ADDRESS_TYPE.CLASSROOM){
              //   return stream.stream_name === item
              // }
              return stream._id.toString() === item.toString();
            }) > -1
          );
        }),
      );
    }
  }, [stream_address, live_address_type]);
  useEffect(() => {
    // 兼容老代码..... 老的数据没有area_id 为了补齐数据需手动查询
    if (areaTree?.length > 0 && Object.keys(info ?? {}).length > 0 && id) {
      const { area_name } = info;
      if (!area_name) {
        return;
      }
      const node = getNodeByLabel(areaTree, area_name!);
      formRef.current?.setFieldValue('area_name', {
        label: area_name,
        value: node?.key,
      });
    }
  }, [areaTree, info]);
  async function getSaveFolderTreeData() {
    try {
      const { data } = await LiveService.getTree();
      const personal = data?.[0]?.children;
      setTreeData((arg) => {
        arg.saveFolder =
          personal?.map((item) => ({
            key: item.path,
            value: item.path,
            title: item.name,
            id: item.id,
            label: `个人资源/${item.name}`,
            isLeaf: item.childCount === 0,
          })) ?? [];
      });
    } catch (error) {
      message.error('获取目录失败');
      return [];
    }
  }

  async function areaNameOnChange(value: any) {
    if (!value) return;
    const { value: id, label } = value;
    try {
      const { extend_message } = await LiveService.fetchFlowAddessByArea(id);
      setFlowAddress({
        ...extend_message,
      });
      // LiveService.STREAM_NAME_MAP
    } catch (error) {
      message.error('获取教师相机直播流失败');
    }
  }
  return (
    <Provider value={form}>
      <div className={style.create}>
        <div
          id="rapid"
          className={ClassNames(style.rapid, inModal ? style.inModal : '')}
        >
          <Access accessible={!inModal}>
            <Header title={`创建${isRapid ? '快速' : '系列'}直播`} />
          </Access>
          <ProForm {...proformConfig}>
            <Col
              xs={{
                span: 12,
              }}
              offset={3}
            >
              <ProFormItem
                label="直播封面"
                wrapperCol={{ span: 6 }}
                labelCol={{ span: 6 }}
                name="live_cover"
                valuePropName="file"
                hidden
              >
                <ImgCrop rotate aspect={16 / 9}>
                  <Upload
                    showUploadList={false}
                    maxCount={1}
                    name="avatar"
                    accept="image/*"
                    className={style.upload}
                    listType="picture-card"
                    beforeUpload={beforeUpload}
                    disabled={readonly}
                    customRequest={async (e) => {
                      try {
                        const data = await fileToBase64(e.file as File);
                        const {
                          extend_message: { url },
                        } = await LiveService.uploadCover(data as string);
                        setCoverUrl(url);
                      } catch (error) {
                        message.error('上传失败');
                      }
                    }}
                  >
                    <div className={style.uploadChild}>
                      <div className={style.img}>
                        <img src={coverUrl} alt="" />
                      </div>
                      <Access accessible={!readonly}>
                        <Button
                          onClick={(e) => {
                            e.stopPropagation();
                            setCoverUrl(defaultImg);
                          }}
                          className={style.btn}
                          type="primary"
                        >
                          恢复默认
                        </Button>
                      </Access>
                    </div>
                  </Upload>
                </ImgCrop>
              </ProFormItem>
            </Col>

            <ProFormText
              name="live_name"
              label="直播名称"
              placeholder="请输入直播名称"
              rules={[
                {
                  required: true,
                  message: '请输入直播名称',
                },
              ]}
              wrapperCol={{ span: FORM_COL / 2 }}
            />

            <Col
              xs={{
                span: 12,
              }}
              offset={3}
            >
              <ProFormItem label="主讲人">
                <Row>
                  <Col
                    xs={{
                      span: 12,
                    }}
                  >
                    <ProFormSelect
                      name="speaker"
                      width="md"
                      noStyle
                      label="主讲人"
                      fieldProps={{
                        onChange(value, option) {
                          formRef.current?.setFieldValue(
                            'live_speaker',
                            undefined,
                          );
                        },
                      }}
                      options={[
                        {
                          label: '选择用户',
                          value: 'user',
                        },
                        {
                          label: '自定义人员',
                          value: 'custom',
                        },
                      ]}
                    />
                  </Col>
                  <Col
                    xs={{
                      span: 12,
                    }}
                  >
                    <ProFormDependency name={['speaker']}>
                      {({ speaker }) => {
                        return speaker === 'user' ? (
                          <ProFormPageSelect
                            name="live_speaker"
                            noStyle
                            showSearch
                            placeholder="请输入主讲人"
                            width="md"
                            pageSize={100}
                            request={async ({
                              keyWords,
                              current,
                              pageSize,
                            }) => {
                              try {
                                const {
                                  extendMessage: { results, recordTotal },
                                } = await LiveService.getPagingData({
                                  sourceType: 0,
                                  pageSize,
                                  school: 'SCU',
                                  pageIndex: current,
                                  keyword: keyWords,
                                });
                                return {
                                  data: results.map((item) => {
                                    return {
                                      label: item.fieldValue,
                                      value: item.fieldCode,
                                    };
                                  }),
                                  total: recordTotal,
                                };
                              } catch (error) {
                                message.error('获取主讲人失败');
                                return {
                                  data: [],
                                  total: 0,
                                };
                              }
                            }}
                            fieldProps={{
                              labelInValue: true,
                            }}
                            rules={[
                              {
                                required: true,
                                message: '请输入主讲人',
                              },
                            ]}
                          />
                        ) : (
                          <ProFormText
                            width="md"
                            name="live_speaker"
                            noStyle
                            placeholder="请输入主讲人"
                            rules={[
                              {
                                required: true,
                                message: '请输入主讲人',
                              },
                            ]}
                          />
                        );
                      }}
                    </ProFormDependency>
                  </Col>
                </Row>
              </ProFormItem>
            </Col>
            <Access accessible={isRapid}>
              <ProFormDatePicker
                name="origin_start_time"
                label="直播开始时间"
                wrapperCol={{ span: FORM_COL / 2 }}
                placeholder="请输入直播开始时间"
                fieldProps={{
                  format: 'YYYY-MM-DD HH:mm',
                  showTime: { format: 'HH:mm' },
                  disabledDate: (current) => {
                    // 禁止选择现在分之前的日期
                    return current && current < moment().subtract(1, 'minutes');
                  },
                }}
                rules={[
                  {
                    required: true,
                    message: '这是必填项',
                  },
                ]}
              />
              <ProFormDatePicker
                name="origin_finish_time"
                label="直播结束时间"
                wrapperCol={{ span: FORM_COL / 2 }}
                placeholder="请输入直播结束时间"
                // help="请先选择开始时间"
                fieldProps={{
                  format: 'YYYY-MM-DD HH:mm',
                  showTime: { format: 'HH:mm' },
                  disabledDate: (current) => {
                    // 禁止选择现在分之前的日期
                    return current && current < moment().subtract(1, 'minutes');
                  },
                }}
                rules={[
                  {
                    required: true,
                    message: '这是必填项',
                  },
                  {
                    async validator(_, value) {
                      if (!value) return Promise.resolve();
                      const start =
                        formRef.current?.getFieldValue('origin_start_time');
                      if (!start) return Promise.resolve();
                      if (value.isBefore(start)) {
                        return Promise.reject('结束时间要大于开始时间');
                      }
                    },
                  },
                ]}
              />
            </Access>
            <Access accessible={!isRapid}>
              <ProFormText
                name="origin_start_time"
                label="开始时间"
                wrapperCol={{ span: FORM_COL / 2 }}
                placeholder="请输入直播开始时间"
                fieldProps={{
                  readOnly: true,
                  suffix: <CalendarOutlined />,
                  onClick: () => {
                    calendarModalRef.current?.setOpen(true);
                  },
                }}
                rules={[
                  {
                    required: true,
                    message: '请输入直播开始时间',
                  },
                ]}
              />
              <CalendarModal
                actionRef={calendarModalRef}
                onSuccess={(value) => {
                  formRef.current?.setFieldValue(
                    'origin_start_time',
                    moment(value.start_time * 1000).format('YYYY-MM-DD HH:mm'),
                  );
                  formRef.current?.setFieldValue('series_lives', [value]);
                }}
              />
            </Access>

            <ProFormRadio.Group
              label="观看权限"
              name="look_permission"
              wrapperCol={{ span: 12 }}
              // rules={[
              //   {
              //     required: true,
              //     message: '请选择观看权限',
              //   },
              // ]}
              fieldProps={{
                onChange(e) {
                  formRef.current?.setFieldValue('look_password', undefined);
                  formRef.current?.setFieldValue(
                    'look_custom_permission',
                    undefined,
                  );
                },
              }}
              options={[
                {
                  label: '社会公开',
                  value: 'public',
                },
                {
                  label: '校内公开',
                  value: 'school',
                },
                {
                  label: '需要密码加入',
                  value: 'password',
                },
                {
                  label: '校内自定义范围',
                  value: 'school_custom',
                },
              ]}
            />

            <ProFormDependency name={['look_permission']}>
              {({ look_permission }) => {
                if (look_permission === 'password') {
                  return (
                    <ProFormText.Password
                      label="密码"
                      wrapperCol={{ span: FORM_COL / 2 }}
                      name="look_password"
                      placeholder="请输入直播观看密码"
                      rules={[
                        {
                          required: true,
                          message: '请输入直播观看密码',
                        },
                      ]}
                    />
                  );
                }
                if (look_permission === 'school_custom') {
                  return (
                    <ProFormRadio.Group
                      label="自定义范围"
                      name="look_custom_permission"
                      wrapperCol={{ span: FORM_COL }}
                      rules={[
                        {
                          required: true,
                          message: '请选择观看权限',
                        },
                      ]}
                      fieldProps={{
                        onChange(e) {
                          formRef.current?.setFieldValue(
                            'look_custom_roles',
                            undefined,
                          );
                          formRef.current?.setFieldValue(
                            'view_object',
                            undefined,
                          );
                        },
                      }}
                      options={[
                        {
                          label: '按用户角色定义范围',
                          value: 'role',
                        },
                        // {
                        //   label: '按课程班级定义范围',
                        //   value: 'course_class',
                        // },
                        // {
                        //   label: '按学生维度定义范围',
                        //   value: 'student_person',
                        // },
                      ]}
                    />
                  );
                }
                return null;
              }}
            </ProFormDependency>
            <ProFormDependency name={['look_custom_permission']}>
              {({ look_custom_permission: look_permission_custom }) => {
                if (look_permission_custom === 'role') {
                  return (
                    <ProFormRadio.Group
                      label="用户角色"
                      name="look_custom_roles"
                      wrapperCol={{ span: FORM_COL / 2 }}
                      rules={[
                        {
                          required: true,
                          message: '请选择用户角色',
                        },
                      ]}
                      options={[
                        {
                          label: '所有学生可见',
                          value: 'r_student',
                        },
                        {
                          label: '所有老师可见',
                          value: 'r_teacher',
                        },
                      ]}
                    />
                  );
                }
                if (!!look_permission_custom) {
                  return (
                    <>
                      <ProFormText
                        label="观看对象名称"
                        name="view_object"
                        wrapperCol={{ span: FORM_COL / 2 }}
                        placeholder="请输入观看对象名称"
                        fieldProps={{
                          value: '',
                          readOnly: true,
                          onClick: () => {
                            studentModalRef.current?.setOpen(true);
                          },
                        }}
                        rules={[
                          {
                            required: true,
                            async validator(rule, value) {
                              if (!value) {
                                return Promise.reject('请输入观看对象名称');
                              }
                              return Promise.resolve();
                            },
                          },
                        ]}
                      />
                      <StudentModal
                        actionRef={studentModalRef}
                        type={look_permission_custom}
                      />
                    </>
                  );
                }
                return null;
              }}
            </ProFormDependency>
            <Col
              xs={{
                span: 12,
              }}
              offset={3}
            >
              <ProFormItem label="是否允许回看">
                <Row
                  // 垂直居中
                  align="middle"
                >
                  <Col
                    xs={{
                      span: 18,
                    }}
                  >
                    <ProFormDependency name={['look_back']}>
                      {({ look_back }) => {
                        return (
                          <ProFormSelect
                            name="look_back_day"
                            noStyle
                            disabled={!look_back || readonly}
                            showSearch
                            placeholder="请选择有效期"
                            width="md"
                            options={[1, 2, 3, 4, 5, 6, 7].map(
                              (item, index: number) => {
                                return {
                                  label: `${item}天`,
                                  value: item,
                                };
                              },
                            )}
                          />
                        );
                      }}
                    </ProFormDependency>
                  </Col>
                  <Col
                    xs={{
                      span: 6,
                      push: 3,
                    }}
                  >
                    <ProFormSwitch
                      name="look_back"
                      width="md"
                      noStyle
                      fieldProps={{
                        onChange(value, option) {
                          formRef.current?.setFieldValue(
                            'look_back_day',
                            undefined,
                          );
                        },
                      }}
                    />
                  </Col>
                </Row>
              </ProFormItem>
            </Col>
            <Col
              xs={{
                span: 12,
              }}
              offset={3}
            >
              <ProFormItem label="是否允许保存">
                <Row
                  // 垂直居中
                  align="middle"
                >
                  <Col
                    xs={{
                      span: 18,
                    }}
                  >
                    <ProFormDependency name={['save_resource']}>
                      {({ save_resource }) => {
                        return (
                          <ProFormTreeSelect
                            name="save_folder"
                            noStyle
                            disabled={!save_resource || readonly}
                            placeholder="保存至"
                            fieldProps={{
                              treeData: treeData.saveFolder,
                              // 自定义构建
                              treeNodeLabelProp: 'label',
                              //FIXME 需填为labelInValue
                              // labelInValue: true,
                              async loadData(dataNode) {
                                const { value, key } = dataNode;
                                try {
                                  const { data } =
                                    await LiveService.onloadTreeLeaf({
                                      folderPath: encodeURIComponent(`${key}/`),
                                    });
                                  const children =
                                    data?.map((item) => {
                                      const label = dataNode.label
                                        ? `${dataNode.label}/${item.name}`
                                        : `个人资源/${item.name}`;
                                      return {
                                        key: item.path,
                                        value: item.path,
                                        title: item.name,
                                        label,
                                        isLeaf: item.childCount === 0,
                                      };
                                    }) ?? [];
                                  setTreeData((arg) => {
                                    // 找到当前子节点
                                    const data = arg.saveFolder;
                                    const node = getDataNode(
                                      value as string,
                                      data,
                                    )!;
                                    node.children = children;
                                  });
                                } catch (error) {
                                  message.error('获取下级目录失败');
                                }
                              },
                            }}
                            rules={[
                              {
                                required: true,
                                message: '请选择目录',
                                async validator(_, value) {
                                  // 禁用状态不检查
                                  if (!save_resource) {
                                    return Promise.resolve();
                                  }
                                  if (!value) {
                                    return Promise.reject(
                                      new Error('请选择目录'),
                                    );
                                  }
                                  return Promise.resolve();
                                },
                              },
                            ]}
                            width="md"
                          />
                        );
                      }}
                    </ProFormDependency>
                  </Col>
                  <Col
                    xs={{
                      span: 6,
                      push: 3,
                    }}
                  >
                    <ProFormSwitch
                      name="save_resource"
                      width="md"
                      noStyle
                      fieldProps={{
                        onChange(value, option) {
                          formRef.current?.setFieldValue(
                            'save_folder',
                            undefined,
                          );
                        },
                      }}
                    />
                  </Col>
                </Row>
              </ProFormItem>
            </Col>
            <Access accessible={isRapid}>
              <ProFormRadio.Group
                label="直播源"
                // 无用,作定位用
                name="live_address_type"
                layout="vertical"
                wrapperCol={{ span: 6 }}
                rules={[
                  {
                    required: true,
                    message: '这是必填项',
                  },
                ]}
                fieldProps={{
                  onChange() {
                    formRef.current?.setFieldValue('flow', undefined);
                    formRef.current?.setFieldValue('area_name', undefined);
                    formRef.current?.setFieldValue(
                      'voice_identify_stream',
                      undefined,
                    );
                    setFlowAddress({});
                  },
                }}
                options={[
                  {
                    label: '教室相机直播流',
                    value: LIVE_ADDRESS_TYPE.CLASSROOM,
                  },
                  {
                    label: '自定义直播流',
                    value: LIVE_ADDRESS_TYPE.CUSTOM,
                  },
                ]}
              />
              <ProFormDependency name={['live_address_type']}>
                {({ live_address_type }) => {
                  if (live_address_type === LIVE_ADDRESS_TYPE.CLASSROOM) {
                    return (
                      <>
                        <ProFormTreeSelect
                          key="classroom"
                          wrapperCol={{ span: FORM_COL / 2 }}
                          label="选择教室"
                          name="area_name"
                          placeholder="请选择教师相机直播流"
                          help={classRoomSelectHelp}
                          fieldProps={{
                            // 自定义构建
                            treeNodeLabelProp: 'label',
                            labelInValue: true,

                            placement: 'topLeft',
                          }}
                          request={async () => {
                            try {
                              const { extend_message } =
                                await LiveService.fetchAreaTree();
                              const tree = getFlowData(extend_message);
                              setAreaTree(tree);
                              return tree;
                            } catch (error) {
                              message.error('获取教师相机直播流失败');
                              return [];
                            }
                          }}
                          rules={[
                            {
                              required: true,
                              message: '请选择教师相机直播流',
                            },
                          ]}
                        />
                      </>
                    );
                  }
                  return (
                    <ProFormList
                      label="自定义直播流"
                      name="custom_live_address"
                      copyIconProps={false}
                      wrapperCol={{ span: FORM_COL / 2 }}
                      creatorButtonProps={{
                        position: 'bottom',
                        creatorButtonText: '添加直播流',
                      }}
                      max={5}
                      min={1}
                    >
                      {(f, index, action) => {
                        const data = action.getCurrentRowData();
                        console.log(
                          '%c [ data ]-1129',
                          'font-size:13px; background:pink; color:#bf2c9f;',
                          data,
                        );
                        const {
                          push_url = '无',
                          pull_url = '无',
                          stream_address = '无',
                          is_mute,
                        } = data?.flow?.item ?? {};
                        const isMute = is_mute ? '有' : '无';

                        const help = (
                          <>
                            <p className={style.help}>
                              直播流地址({isMute}声) {stream_address}
                            </p>
                            <p className={style.help}>
                              推流地址为({isMute}声) {push_url}
                            </p>
                            <p className={style.help}>
                              拉流地址为({isMute}声) {pull_url}
                            </p>
                            {index === 0
                              ? '注：直播将默认播放“视频流-1”的声音，直播中可切换至任意视频流的声音'
                              : ''}
                          </>
                        );

                        return (
                          <ProFormItem help={help}>
                            <Row>
                              <Col
                                xs={{
                                  span: FORM_COL / 2,
                                }}
                              >
                                <ProFormDependency
                                  ignoreFormListField={false}
                                  name={[`flow`]}
                                >
                                  {({ flow }) => {
                                    return (
                                      <>
                                        <ProFormText
                                          disabled={!flow}
                                          name="name"
                                          width="md"
                                          fieldProps={{
                                            onChange() {},
                                          }}
                                          // noStyle
                                          rules={[
                                            {
                                              required: true,
                                              async validator(_, value) {
                                                if (!value) {
                                                  return Promise.reject(
                                                    '请输入直播流名称',
                                                  );
                                                }

                                                const addr =
                                                  formRef.current?.getFieldValue(
                                                    'custom_live_address',
                                                  );
                                                // 校验数组中的重复name
                                                const isRepeat = addr?.some(
                                                  (item: any, i: number) =>
                                                    i !== index &&
                                                    item.name === value,
                                                );
                                                if (isRepeat) {
                                                  return Promise.reject(
                                                    new Error(
                                                      '直播流名称不能重复',
                                                    ),
                                                  );
                                                }

                                                return Promise.resolve();
                                              },
                                            },
                                          ]}
                                        />
                                      </>
                                    );
                                  }}
                                </ProFormDependency>
                              </Col>
                              <Col
                                xs={{
                                  span: FORM_COL,
                                  push: 3,
                                }}
                              >
                                <ProFormPageSelect
                                  name="flow"
                                  width="md"
                                  // noStyle
                                  showSearch
                                  request={async ({
                                    keyWords,
                                    current,
                                    pageSize,
                                  }) => {
                                    try {
                                      const {
                                        extend_message: { results, total },
                                      } = await LiveService.fetchFlows(
                                        current,
                                        pageSize,
                                        keyWords!,
                                      );
                                      return {
                                        data: results.map((item) => {
                                          return {
                                            label: item.stream_name,
                                            value: item._id,
                                            key: item._id,
                                            item,
                                          };
                                        }),
                                        total,
                                      };
                                    } catch (error) {
                                      message.error('获取直播流失败');
                                      return {
                                        total: 0,
                                        data: [],
                                      };
                                    }
                                  }}
                                  fieldProps={{
                                    onChange(_, option: any) {
                                      const data = action.getCurrentRowData();
                                      action.setCurrentRowData({
                                        ...data,
                                        name: option?.label,
                                      });
                                    },
                                    labelInValue: true,
                                  }}
                                  placeholder="请选择直播流"
                                  rules={[
                                    {
                                      required: true,
                                      async validator(_, value) {
                                        if (!value) {
                                          return Promise.reject('请选择直播流');
                                        }
                                        const addr =
                                          formRef.current?.getFieldValue(
                                            'custom_live_address',
                                          );
                                        const now = addr?.[index];
                                        // 校验数组中的重复flow
                                        const isRepeatFlow = addr?.some(
                                          (item: any, i: number) => {
                                            return (
                                              i !== index &&
                                              item?.flow?.key === now?.flow.key
                                            );
                                          },
                                        );
                                        if (isRepeatFlow) {
                                          return Promise.reject(
                                            new Error('不能选择同一直播流'),
                                          );
                                        }
                                        return Promise.resolve();
                                      },
                                    },
                                  ]}
                                />
                              </Col>
                            </Row>
                          </ProFormItem>
                        );
                      }}
                    </ProFormList>
                  );
                }}
              </ProFormDependency>
            </Access>
            <ProFormSwitch
              label="语音识别"
              fieldProps={{
                onChange(value, option) {
                  if (!value) {
                    formRef.current?.setFieldValue('voice_translation', false);
                    formRef.current?.setFieldValue('is_si', false);
                  }
                },
              }}
              name="voice_identify"
            />
            <ProFormDependency name={['voice_identify']}>
              {({ voice_identify }) => {
                if (voice_identify) {
                  return (
                    <ProFormSelect
                      mode="multiple"
                      placeholder="请选择发起语音识别的视频流"
                      label="需语音识别的流"
                      name="voice_identify_stream"
                      wrapperCol={{ span: FORM_COL / 2 }}
                      rules={[
                        {
                          required: true,
                          message: '请选择发起语音识别的视频流',
                        },
                      ]}
                      options={clearAryEmpty(stream_address)?.map((item) => {
                        // 判断直播源是教室相机

                        if (live_address_type === LIVE_ADDRESS_TYPE.CLASSROOM) {
                          return {
                            label: item?.stream_name,
                            value: item?.stream_name,
                          };
                        }
                        return {
                          label: item?.stream_name,
                          value: item?._id,
                        };
                      })}
                    />
                  );
                }
              }}
            </ProFormDependency>
            <ProFormSwitch
              label="实时翻译"
              name="voice_translation"
              help="需开启语音识别"
              fieldProps={{
                onChange(value, option) {
                  if (value) {
                    formRef.current?.setFieldValue('voice_identify', true);
                  } else {
                    formRef.current?.setFieldValue('is_si', false);
                  }
                },
              }}
            />
            <ProFormSwitch
              label="同声传译"
              fieldProps={{
                onChange(value, option) {
                  if (value) {
                    formRef.current?.setFieldValue('voice_translation', true);
                    formRef.current?.setFieldValue('voice_identify', true);
                  }
                },
              }}
              name="is_si"
              help="需开启实时翻译"
            />
            <ProFormSwitch name="is_share" label="允许共享" />
            <ProFormTextArea
              label="直播简介"
              colProps={{ span: 12, push: 3 }}
              name="live_introduce"
              placeholder="请输入直播简介"
              fieldProps={{
                autoSize: { minRows: 6, maxRows: 6 },
              }}
            />
          </ProForm>
        </div>
      </div>
    </Provider>
  );
};

interface RapidProps {
  /** 是否在弹窗中 */
  inModal?: boolean;

  readonly?: boolean;
  id?: string;
  type?: 'rapid' | 'series';
  hideFooter?: boolean;
}
export default Rapid;
Rapid.displayName = 'Rapid';
function beforeUpload(file: any) {
  const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png';
  if (!isJpgOrPng) {
    message.error('只能上传JPG/PNG文件!');
  }
  const isLt2M = file.size / 1024 / 1024 < 2;
  if (!isLt2M) {
    message.error('图片大小必须小于2MB!');
  }
  return isJpgOrPng && isLt2M;
}

function getDataNode(key: React.Key, tree: DataNode[]): DataNode | undefined {
  for (let i = 0; i < tree.length; i++) {
    const element = tree[i];
    if (element.key === key) {
      return element;
    } else if (element.children) {
      const dataNode = getDataNode(key, element.children);
      if (dataNode) {
        return dataNode;
      }
    }
  }
}

// 递归处理流数据
function getFlowData(data: LiveService.AreaTreeNode[], depth = 1): DataNode[] {
  return data?.map((item) => {
    return {
      key: item.id,
      value: item.id,
      title: item.name,
      label: item.area_fullname,
      selectable: depth === 3,
      children: getFlowData(item.children, depth + 1),
    };
  });
}
