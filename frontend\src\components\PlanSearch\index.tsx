import React, { useState, useEffect } from 'react';
import { Modal, Progress, Button, Spin, message, InputNumber, Form, Input, Select, Cascader } from 'antd';
// import './index.less'
import courseManagementService from '@/service/courseManagementService'
import TeacherItem from '@/components/formItemBox/teacherItem'
const { Option } = Select
interface CompoentProps {
    formdata: any
    searchList?: () => void
    resetList?: () => void
    task?: boolean
}
export interface IreturnTree {
    code: string
    id: number
    name: string
    super_area: number | null
    type: string
    children?: IreturnTree[]
    area_fullname: string
}
const PlanSearch: React.FC<CompoentProps> = (props) => {
    const {
        formdata,
        task,
        searchList = () => {
            /**/
        },
        resetList = () => {
            /**/
        },
    } = props
    // const [searchform] = Form.useForm()
    const [course, setCourse] = useState<any>({})
    const [WeeklyList, setWeeklyList] = useState<any>([])
    const [teacherList, setTeacherList] = useState<any>()
    const [sectionList, setSectionList] = useState<any>([])
    const [courseType, setCourseType] = useState<string[]>([])
    const [treeData, setTreeData] = useState<any>([])
    const [selectValue, setSelectValue] = useState<string>('')
    const weeklist: any = {
        1: '星期一',
        2: '星期二',
        3: '星期三',
        4: '星期四',
        5: '星期五',
        6: '星期六',
        7: '星期天',
    }
    // 周次
    const getWeeklyList = () => {
        // courseManagementService.getCurrentWeekly().then((res) => {
        //     if (res && res.errorCode === 'success') {
        //         setWeeklyList(res.extendMessage)
        //     }
        // })
        setWeeklyList(Array.from(new Array(25 + 1).keys()).slice(1))
    }
    // 节次
    const getSectionList = () => {
        // 数据不能用了 暂定写死
        let temp: any = [];
        for (let i = 0; i < 12; i++) {
            temp.push({
                name: `第${i + 1}节`,
                // sectionnum:i
            })
        }
        setSectionList(temp)
        // courseManagementService.getSection().then((res) => {
        //     if (res && res.errorCode === 'success' && res.extendMessage) {
        //         setSectionList(res.extendMessage[0].timeDetailsShows)
        //     }
        // })
    }
    const gettree = () => {
        courseManagementService.searchtree().then((res) => {
            if (res && res.error_msg === 'Success' && res.extend_message.length) {
                setTreeData(forTree(res.extend_message))
            }
        })
    }
    const forTree = (tree: IreturnTree[]): any => {
        return tree.map((item: IreturnTree) => {
            return {
                // key: item.id.toString(),
                // title: item.name,
                // type: item.type,
                // disabled: item.type !== '教室',
                // children: item.children ? forTree(item.children) : [],

                value: item.id.toString() + ',' + item.area_fullname,
                label: item.name,
                children: item.children ? forTree(item.children) : null,
            }
        })
    }
    const getcourselist = () => {
        courseManagementService.getCourse().then((res) => {
            if (res && res.success && res.data) {
                setCourse(res.data)
                setCourseType(res.data.selectCurriculumResponse.courseNameAggregate)
            }
        })
    }
    const getfieldslist = () => {
        courseManagementService.getfields().then((res) => {
            if (res && res.errorCode === '200') {
                res.extendMessage.forEach((item: any) => {
                    if (item.fieldName === 'teacher') {
                        setTeacherList(item.controlData ? JSON.parse(item.controlData) : {})
                    }
                })
            }
        })
    }
    useEffect(() => {
        // getcourselist()
        getfieldslist()
        getWeeklyList()
        getSectionList()
        gettree()
    }, [])


    const coursetypechange = (value: any) => {
        formdata.setFieldsValue({ course: { coursedetail: '' } })
        // value === 'courseNumber'
        //     ? setCourseType(course.selectCurriculumResponse.courseNumberAggregate)
        //     : setCourseType(course.selectCurriculumResponse.courseNameAggregate)
    }
    const onChange = (value: any) => {
        // console.log(value)
        // setSelectValue(value)

        if (value[value.length - 1].split(',')[1] !== 'undefined') {
            formdata.setFieldsValue({ classroom: value })
        } else {
            formdata.setFieldsValue({ classroom: [] })
        }
    }
    return (
        <div className='top-search'>
            <Form
                layout={'inline'}
                name="basic"
                labelCol={{ span: 7 }}
                form={formdata}
            >
                <Form.Item>
                    <Input.Group compact>
                        <Form.Item name={['course', 'courseType']} noStyle>
                            <Select
                                defaultValue="courseName"
                                style={{ width: 100 }}
                                onChange={coursetypechange}
                            >
                                <Option value="courseName">课程名称</Option>
                                <Option value="courseNumber">课程号</Option>
                            </Select>
                        </Form.Item>
                        <Form.Item name={['course', 'coursedetail']} noStyle>
                            <Input placeholder="请输入" style={{ width: 200 }} />
                        </Form.Item>
                    </Input.Group>
                </Form.Item>
                <Form.Item
                    // label='课序号'
                    name={'serialNumber'}
                >
                    <Input style={{ width: 140 }} placeholder='课序号' autoComplete='off' />
                    {/* <InputNumber style={{ width: 140 }} min={1} /> */}
                </Form.Item>
                <TeacherItem
                    multiple={false}
                    required={false}
                    message={'请选择教师'}
                    label={'教师'}
                    name={'teacher'}
                    key="teacher1"
                    style={{ margin: '0 16px 10px 0',width:'120px' }}
                />
                {/* <Form.Item
                    label='教师'
                    name='teacher'
                >
                    <Select
                        showSearch
                        style={{ width: 150 }}
                        placeholder='请选择教师'
                        allowClear={true}
                    >
                        {teacherList &&
                            Object.keys(teacherList).map((key: any) => {
                                return (
                                    <Option value={key} key={'teacher' + key}>
                                        {teacherList[key]}
                                    </Option>
                                )
                            })}
                    </Select>
                </Form.Item> */}
                <Form.Item
                    // label='周次'
                    name='weekly'
                >
                    <Select
                        showSearch
                        style={{ width: 150 }}
                        placeholder='请选择周次'
                        allowClear={true}
                    >
                        {WeeklyList.map((item: any, index: number) => {
                            return (
                                <Option value={item} key={index}>
                                    第{item}周
                                </Option>
                            )
                        })}
                    </Select>
                </Form.Item>
                <Form.Item
                    // label='星期'
                    name='week'
                >
                    <Select
                        showSearch
                        style={{ width: 150 }}
                        placeholder='请选择星期'
                        allowClear={true}
                    >
                        {weeklist &&
                            Object.keys(weeklist).map((key: any) => {
                                return (
                                    <Option value={key} key={'teacher' + key}>
                                        {weeklist[key]}
                                    </Option>
                                )
                            })}
                    </Select>
                </Form.Item>
                <Form.Item
                    // label='节次'
                    name='section'
                >
                    <Select
                        showSearch
                        style={{ width: 150 }}
                        placeholder='请选择节次'
                        allowClear={true}
                    >
                        {sectionList.map((item: any, index: number) => {
                            return (
                                <Option value={index + 1} key={index}>
                                    {item.name}
                                </Option>
                            )
                        })}
                    </Select>
                </Form.Item>
                <Form.Item
                    // label='教室'
                    name='classroom'
                >
                    {/* <TreeSelect
                        style={{ width: 250 }}
                        value={selectValue}
                        dropdownStyle={{ height: 300, overflow: 'auto' }}
                        treeData={treeData}
                        placeholder="请选择教室"
                        treeDefaultExpandAll
                        onChange={onChange}
                    /> */}

                    <Cascader
                        options={treeData}
                        placeholder="请选择教室"
                        onChange={onChange}
                    />
                </Form.Item>
                {
                    task && <>
                        <Form.Item
                            // label='直播'
                            name='broadcast'
                        >
                            <Select
                                showSearch
                                style={{ width: 150 }}
                                placeholder='请选择直播状态'
                                allowClear={true}
                            >
                                <Option value='1'>未开始</Option>
                                <Option value='2'>直播中</Option>
                                <Option value='3'>已结束</Option>
                                {/* <Option value='4'>已关闭</Option>
                                <Option value='5'>异常</Option> */}
                            </Select>
                        </Form.Item>
                        <Form.Item
                            // label='语音识别'
                            name='voice'
                        >
                            <Select
                                showSearch
                                style={{ width: 150 }}
                                placeholder='请选择语音识别状态'
                                allowClear={true}
                            >
                                <Option value='1'>关闭</Option>
                                <Option value='2'>开启</Option>
                            </Select>
                        </Form.Item>
                        <Form.Item
                            // label='翻译转写'
                            name='translate'
                        >
                            <Select
                                showSearch
                                style={{ width: 150 }}
                                placeholder='请选择翻译转写状态'
                                allowClear={true}
                            >
                                <Option value='1'>关闭</Option>
                                <Option value='2'>开启</Option>
                            </Select>
                        </Form.Item>
                    </>
                }
                <Form.Item><Button type="primary" onClick={searchList}>查询</Button></Form.Item>
                <Form.Item><Button onClick={resetList}>重置</Button></Form.Item>
            </Form>
        </div>
    );
};

export default PlanSearch;
