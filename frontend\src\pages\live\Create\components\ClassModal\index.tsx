/*
 * @Author: 冉志诚
 * @Date: 2023-08-02 16:17:30
 * @LastEditTime: 2023-08-02 16:17:38
 * @FilePath: \frontend\src\pages\live\Create\components\ClassModal\index.tsx
 * @Description:
 */

import { Modal, ModalProps } from 'antd';
import React, { useImperativeHandle, useRef } from 'react';
import { useImmer } from 'use-immer';

const ClassModal: React.FC<ClassModalProps> = ({
  actionRef: propActionRef,
}) => {
  const [open, setOpen] = useImmer(false);
  const [loading, setLoading] = useImmer(false);
  useImperativeHandle(
    propActionRef,
    () => {
      return {
        setOpen,
      };
    },
    [],
  );
  const modalConfig: ModalProps = {
    open,
    title: '班级选择',
    onCancel() {
      setOpen(false);
    },
    onOk(e) {},
    confirmLoading: loading,
    maskClosable: false,
    destroyOnClose: false,
  };

  return <Modal {...modalConfig}></Modal>;
};

interface ClassModalProps {
  actionRef: React.MutableRefObject<ClassRefType | null>;
}
export default ClassModal;
ClassModal.displayName = 'ClassModal';
export type ClassRefType = {
  setOpen: (open: boolean) => void;
};
