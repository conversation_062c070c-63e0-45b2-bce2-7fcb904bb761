/*
 * @Author: spring breeze
 * @Date: 2023-07-03 15:57:00
 * @LastEditTime: 2023-07-03 15:57:06
 * @FilePath: \frontend\src\pages\live\My\Apply\index.tsx
 * @Description:
 */
import React, { useEffect, useMemo, useRef, useState } from 'react';
import style from './index.less';
import {
  Button,
  Dropdown,
  Modal,
  Popconfirm,
  Popover,
  Radio,
  Select,
  Space,
  Tabs,
  TabsProps,
  Tooltip,
  message,
} from 'antd';
// 导入tab样式
import 'antd/lib/tabs/style/index.css';
import { Tab } from 'rc-tabs/lib/interface';
import {
  ActionType,
  ProColumns,
  ProTable,
  ProTableProps,
} from '@ant-design/pro-components';
import { useImmer } from 'use-immer';
import { useQuery, useQueryPagination } from '@/hooks';
import IconFont from '@/components/iconFont/iconFont';
import { ArrowDownOutlined, ArrowUpOutlined } from '@ant-design/icons';
import LegendMode, { TABLE_MODE } from '../../components/LegendMode';
import { history } from 'umi';
import { LiveService } from '@/api/live';
import moment from 'moment';
import Access from '@/components/Access';
import { isDevelopment, openNewPage } from '@/utils/utils';
import './reset.less';
import { CreateService } from '@/api/CreateService';
const items: Tab[] = [
  {
    key: '1',
    label: '活动直播',
  },
];
type Item = Partial<LiveService.MyLiveData>;
type Params = {
  current?: number;
  pageSize?: number;
  order_by?: string;
} & Item;
type ConfigTypes = {
  /** 列表或图例模式 */
  mode: TABLE_MODE;
};
let radioType = 'rapid';

const MyApply: React.FC<AppProps> = () => {
  const tabsConfig: TabsProps = {
    items,
    tabBarExtraContent: (
      <Button
        onClick={() => {
          Modal.confirm({
            title: '请选择直播类型',
            type: 'info',
            onOk: () => {
              history.push({
                pathname: `/live/create`,
                query: {
                  hideLeft: '1',
                  type: radioType,
                },
              });
            },
            closable: true,
            icon: null,
            maskClosable: true,
            content: (
              <div>
                <Radio.Group
                  defaultValue={radioType}
                  onChange={(e) => {
                    radioType = e.target.value;
                  }}
                  // value={radioType}
                >
                  <Space direction="vertical">
                    <Radio value="rapid">快速直播</Radio>
                    <Radio value="series">系列直播</Radio>
                  </Space>
                </Radio.Group>
              </div>
            ),
          });
        }}
        type="primary"
      >
        申请直播
      </Button>
    ),
  };
  const columns: ProColumns<Item>[] = [
    {
      title: '直播名称',
      fieldProps: {
        placeholder: '请输入直播名称',
      },
      formItemProps: {
        label: null,
      },
      dataIndex: 'live_name',
      width: 150,
      ellipsis: true,
    },
    {
      title: '申请状态',
      fieldProps: {
        placeholder: '请输入申请状态',
        showSearch: true,
      },
      formItemProps: {
        label: null,
      },
      dataIndex: 'apply_status',
      valueEnum: {
        in_review: {
          text: '审核中',
          status: 'Processing',
        },
        not_pass: {
          text: '未通过',
          status: 'Error',
        },
        pass: {
          text: '已通过',
          status: 'Success',
        },
        draft: {
          text: '草稿',
          status: 'Default',
        },
      },
    },
    {
      title: '直播状态',
      fieldProps: {
        placeholder: '请输入直播状态',
        showSearch: true,
      },
      formItemProps: {
        label: null,
      },
      dataIndex: 'schedule_status',

      valueEnum: {
        1: {
          text: '未开始',
          status: 'Default',
        },
        2: {
          text: '直播中',
          status: 'Processing',
        },
        3: {
          text: '已结束',
          status: 'Error',
        },
        4: {
          text: '已关闭',
          status: 'Error',
        },
        5: {
          text: '异常',
          status: 'Error',
        },
      },
    },
    {
      title: '主讲人',
      search: false,
      dataIndex: 'live_speaker',
    },
    {
      title: '听众对象',
      search: false,
      dataIndex: 'look_permission',
      valueEnum: {
        public: '社会公开',
        school: '校内公开',
        password: '需要密码加入',
        school_custom: '校内自定义范围',
      },
    },
    {
      title: '创建时间',
      search: false,
      dataIndex: 'add_time',
      renderText(text, record, index, action) {
        return moment(text * 1000).format('YYYY-MM-DD HH:mm:ss');
      },
    },
    {
      title: '开始时间',
      search: false,
      dataIndex: 'origin_start_time',
      renderText(text, record, index, action) {
        if (!text) {
          return;
        }
        return moment(text * 1000).format('YYYY-MM-DD HH:mm:ss');
      },
    },
    {
      title: '结束时间',
      search: false,
      dataIndex: 'origin_finish_time',
      renderText(text, record, index, action) {
        if (!text) {
          return;
        }
        return moment(text * 1000).format('YYYY-MM-DD HH:mm:ss');
      },
    },
    {
      title: '操作',
      valueType: 'option',
      width: 150,
      fixed: 'right',
      render: (_, record, index, action) => {
        const {
          _id,
          look_back,
          look_back_status,
          schedule_status,
          apply_status,
          is_series,
        } = record;
        const disabledReplay = !(
          look_back &&
          look_back_status === 2 &&
          schedule_status === 3
        );
        const deleteBtnDisabled = schedule_status === 2;
        return (
          <div key="operation" className={style.operation}>
            <Button
              type="link"
              disabled={disabledReplay}
              onClick={() => {
                window.open(`${location.origin}/learn/live/activity/${_id}`);
              }}
            >
              回放
            </Button>
            <Popconfirm
              title="确定删除吗？"
              disabled={deleteBtnDisabled}
              onConfirm={async () => {
                try {
                  const { error_code, extend_message } =
                    await LiveService.deleteLiveBulk([_id!]);
                  if (error_code === 'livemanage.0000.0000') {
                    message.success('删除成功');
                    action?.reload();
                  } else {
                    throw new Error(extend_message);
                  }
                } catch (error) {
                  message.error((error as any).message);
                }
              }}
            >
              <Button
                style={
                  deleteBtnDisabled
                    ? {
                        padding: 0,
                      }
                    : {
                        color: 'red',
                      }
                }
                disabled={deleteBtnDisabled}
                type="link"
              >
                删除
              </Button>
            </Popconfirm>

            <Dropdown
              overlayClassName="table-extra-drop-down"
              menu={{
                items: [
                  {
                    label: (
                      <Button
                        type="link"
                        disabled={
                          apply_status === 'pass' ||
                          apply_status === 'in_review'
                        }
                        onClick={async () => {
                          submitLive(record);
                        }}
                      >
                        提交申请
                      </Button>
                    ),
                    key: '1',
                  },
                  {
                    label: (
                      <Button
                        disabled={apply_status !== 'in_review'}
                        type="link"
                        onClick={async () => {
                          cancelLive(record);
                        }}
                      >
                        取消申请
                      </Button>
                    ),
                    key: '2',
                  },
                  {
                    label: (
                      <Button
                        type="link"
                        onClick={() => {
                          history.push({
                            pathname: '/live/create',
                            query: {
                              hideLeft: '1',
                              type: is_series ? 'series' : 'rapid',
                              id: _id!,
                            },
                          });
                        }}
                      >
                        编辑申请
                      </Button>
                    ),
                    key: '3',
                  },
                  {
                    label: (
                      <Button
                        type="link"
                        disabled={record.apply_status !== 'not_pass'}
                        onClick={() => {
                          Modal.info({
                            content: (
                              <div className={style.reason}>
                                <h2>拒绝理由</h2>
                                <span>{record.audit_reason}</span>
                              </div>
                            ),
                            maskClosable: true,
                          });
                        }}
                      >
                        查看原因
                      </Button>
                    ),
                    key: '4',
                  },
                ],
              }}
              placement="bottom"
              arrow
            >
              <Button type="link">操作</Button>
            </Dropdown>
          </div>
        );
      },
    },
  ];
  const [config, setConfig] = useImmer<ConfigTypes>({
    mode: TABLE_MODE.LIST,
  });
  const [params, setParams] = useImmer<Params>({
    order_by: '-add_time',
  });
  const actionRef = useRef<ActionType>();
  const { pagination } = useQueryPagination({
    pageSize: 9,
  });

  const Condition = () => {
    const { mode } = config;
    return (
      <div className={style.condition}>
        <Select
          className={style.item}
          key="createTime"
          value={params.order_by}
          onChange={(v) => {
            setParams((d) => {
              d.order_by = v;
            });
          }}
          options={[
            {
              label: (
                <>
                  开始时间
                  <ArrowUpOutlined />
                </>
              ),
              value: 'start_time',
            },
            {
              label: (
                <>
                  开始时间
                  <ArrowDownOutlined />
                </>
              ),
              value: '-start_time',
            },
            {
              label: (
                <>
                  创建时间
                  <ArrowUpOutlined />
                </>
              ),
              value: 'add_time',
            },
            {
              label: (
                <>
                  创建时间
                  <ArrowDownOutlined />
                </>
              ),
              value: '-add_time',
            },
          ]}
        ></Select>

        <div
          className={mode === TABLE_MODE.CARD ? style.active : ''}
          onClick={() => {
            setConfig((d) => {
              d.mode = TABLE_MODE.CARD;
            });
          }}
        >
          <Tooltip title="图例模式">
            <IconFont type="iconhebingxingzhuangfuzhi2" />
          </Tooltip>
        </div>
        <div
          className={mode === TABLE_MODE.LIST ? style.active : ''}
          onClick={() => {
            setConfig((d) => {
              d.mode = TABLE_MODE.LIST;
            });
          }}
        >
          <Tooltip title="列表模式">
            <IconFont type="iconliebiao" />
          </Tooltip>
        </div>
      </div>
    );
  };

  const [loading, setLoading] = useState(false);
  const tableConfig: ProTableProps<Item, Params> = {
    rowKey: '_id',
    toolBarRender: false,
    search: {
      optionRender(searchConfig, props, dom) {
        return [
          <div
            style={{
              display: 'flex',
              gap: 10,
            }}
            key="defaultDom"
          >
            {dom}
          </div>,
          <Condition key="condition" />,
        ];
      },
      span: 4,
      showHiddenNum: true,
      labelWidth: 'auto',
      className: 'in-pro-search',
      searchText: '搜索',
    },
    columns,
    pagination,
    loading,
    params,
    actionRef,
    onLoadingChange(loading) {
      setLoading(loading as boolean);
    },
    tableRender(props, defaultDom) {
      const { dataSource, loading } = (props as any).action ?? {};
      const { pagination } = props;
      return config.mode === TABLE_MODE.LIST ? (
        defaultDom
      ) : (
        <div>
          <LegendMode
            cardButtonsRender={(item) => {
              if (!item) {
                return [<></>];
              }
              const {
                schedule_status,
                apply_status,
                look_back,
                look_back_status,
                is_series,
                _id,
              } = item;

              const disabledSetting = schedule_status !== 1;
              const disabledReplay = !(
                look_back &&
                look_back_status === 2 &&
                schedule_status === 3
              );
              const showReplay = schedule_status === 3;

              return [
                <Popover
                  key="detail"
                  placement="bottom"
                  content={
                    <div className={style.reason}>
                      <h2>拒绝理由</h2>
                      <span>{item.audit_reason}</span>
                    </div>
                  }
                >
                  <Access accessible={apply_status === 'not_pass'}>
                    <Button type="link">详情</Button>
                  </Access>
                </Popover>,
                <Access key="replay_btn" accessible={showReplay}>
                  <Button
                    disabled={disabledReplay}
                    type="link"
                    onClick={() => {
                      window.open(
                        `${location.origin}/learn/live/activity/${_id}`,
                      );
                    }}
                  >
                    回放
                  </Button>
                </Access>,
                <Access key="cancel" accessible={apply_status === 'in_review'}>
                  <Button
                    type="link"
                    onClick={() => {
                      cancelLive(item);
                    }}
                  >
                    取消申请
                  </Button>
                </Access>,
                <Access
                  key="submit"
                  accessible={
                    !(apply_status === 'pass' || apply_status === 'in_review')
                  }
                >
                  <Button
                    type="link"
                    onClick={() => {
                      submitLive(item);
                    }}
                  >
                    提交申请
                  </Button>
                </Access>,
                <Button
                  key="setting"
                  type="link"
                  onClick={() => {
                    history.push({
                      pathname: '/live/create',
                      query: {
                        hideLeft: '1',
                        type: is_series ? 'series' : 'rapid',
                        id: _id,
                      },
                    });
                  }}
                >
                  设置
                </Button>,
              ];
            }}
            pagination={pagination}
            dataSource={dataSource}
            loading={loading}
          />
        </div>
      );
    },
    async request(params, sort, filter) {
      try {
        console.log('发生请求', params);
        const {
          current,
          pageSize,
          schedule_status,
          live_name,
          order_by,
          apply_status,
        } = params;
        const {
          extend_message: { results, total },
        } = await LiveService.getMyLive({
          type_name: LiveService.MyLiveTypeName.CREATE,
          page: current!,
          size: pageSize!,
          live_name,
          schedule_status,
          order_by,
          apply_status,
        });
        return {
          data: results,
          // success 请返回 true，
          // 不然 table 会停止解析数据，即使有数据
          success: true,
          // 不传会使用 data 的长度，如果是分页一定要传
          total,
        };
      } catch (error) {
        return {
          data: [],
          success: false,
        };
      }
    },
  };
  async function cancelLive(record: Item) {
    try {
      const { error_code, extend_message } = await CreateService.cancel([
        record._id!,
      ]);
      if (error_code === 'livemanage.0000.0000') {
        message.success('取消成功');
        actionRef.current?.reload();
      } else {
        throw new Error('取消失败');
      }
    } catch (error) {
      message.error((error as any).message);
    }
  }
  async function submitLive(record: Item) {
    try {
      const { error_code, extend_message, error_msg } =
        await CreateService.apply([record]);
      if (error_code === 'livemanage.0000.0000') {
        message.success('申请成功');
        actionRef.current?.reload();
      } else {
        throw new Error(error_msg ?? '申请失败');
      }
    } catch (error) {
      message.error((error as any).message);
    }
  }
  return (
    <div className={style.apply}>
      <Tabs {...tabsConfig}></Tabs>
      <ProTable {...tableConfig} />
    </div>
  );
};

interface AppProps {}
export default MyApply;
MyApply.displayName = 'MyApply';
