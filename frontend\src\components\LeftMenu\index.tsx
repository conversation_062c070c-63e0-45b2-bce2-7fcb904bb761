import { remoteJsToScript } from '@/utils/utils';
import React, { useEffect, useRef } from 'react';
import { useSelector } from 'umi';
import { CUSTOMER_NPU } from '../header/header';

const LeftMenu: React.FC<any> = () => {
  const { userInfo, menuShow, parameterConfig } = useSelector<any, any>(
    (state) => state.global,
  );

  const commonMenu = useRef<Record<string, any> | null>();
  const isNPU = parameterConfig?.target_customer === CUSTOMER_NPU;
  const getMenus = async (header: string) => {
    // await remoteJsToScript('/learn/workbench/CommonMenu.min.js');
    commonMenu.current = new window.CommonMenu('menu-header-box', {
      headerUrl: header,
      headerHeight: `calc(100vh - ${isNPU ? 70 : 52}px)`,
    });
    commonMenu.current?.open(); // 开启挂载
  };
  useEffect(() => {
    getMenus(require('@/static/images/defualt-avatar.png'));
  }, []);
  useEffect(() => {
    commonMenu.current?.changeExpand(menuShow);
  }, [menuShow]);
  useEffect(() => {
    if (userInfo?.avatar) {
      commonMenu.current?.setHeader(userInfo.avatar);
    }
  }, [userInfo]);
  return <div id="menu-header-box"></div>;
};

export default LeftMenu;
