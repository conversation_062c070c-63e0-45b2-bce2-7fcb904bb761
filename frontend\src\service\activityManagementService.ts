import RequestConstant from '@/constant/request';
import { request } from 'umi';
namespace activityManagementService {
  /**
   * 根据直播流地址名称查询流地址
   */
  export const fetchFlowsByName = function (
    stream_name: string,
    page: number,
    size: number,
    order_by: string,
  ) {
    return request<Request.IResponseList<ActivityType.IFlow>>(
      `/v1/stream/man/`,
      {
        params: {
          stream_name,
          page,
          size,
          str: new Date().getTime(),
          order_by,
        },
        prefix: RequestConstant.PREFIX.LIVE_MANAGE,
      },
    );
  };

  /**
   * 查询流地址列表
   */
  export const fetchFlows = function (
    page: number,
    size: number,
    keyword: string,
  ) {
    return request<Request.IResponseList<ActivityType.IFlow>>(
      `/v1/stream/man/`,
      {
        params: {
          str: new Date().getTime(),
          stream_name: keyword,
          page,
          size,
        },
        prefix: RequestConstant.PREFIX.LIVE_MANAGE,
      },
    );
  };

  /**
   * 新建直播流地址
   *
   * @param {string} stream_name
   * @param {string} stream_address
   */
  export const addFlow = (
    stream_name: string,
    stream_address: string,
    push_url: string,
    pull_url: string,
    is_mute: boolean,
  ) =>
    request<Request.IResponse>('/v1/stream/man/', {
      method: 'POST',
      data: {
        stream_name,
        stream_address,
        push_url,
        pull_url,
        is_mute,
      },
      prefix: RequestConstant.PREFIX.LIVE_MANAGE,
    });

  /**
   * 批量删除直播流地址
   *
   * @param {string[]} _ids
   */
  export const deleteFlowBulk = (_ids: React.Key[]) =>
    request<Request.IResponse>('/v1/stream/man/', {
      method: 'DELETE',
      data: {
        _ids,
      },
      prefix: RequestConstant.PREFIX.LIVE_MANAGE,
    });

  /**
   * 修改直播流地址
   *
   * @param {string} _id
   * @param {string} stream_name
   * @param {string} stream_address
   */
  export const updateFlow = function (flow: ActivityType.IFlowForm) {
    return request<Request.IResponse>('/v1/stream/man/', {
      method: 'PATCH',
      data: {
        ...flow,
      },
      prefix: RequestConstant.PREFIX.LIVE_MANAGE,
    });
  };

  /**
   * 修改直播流地址状态
   *
   * @param {string} _id
   * @param {boolean} steam_status
   * @return {*}
   */
  export const updateFlowStatus = function (
    _id: string,
    stream_status: boolean,
  ) {
    return request('/v1/stream/man/action/', {
      method: 'PATCH',
      data: {
        _id,
        stream_status,
      },
      prefix: RequestConstant.PREFIX.LIVE_MANAGE,
    });
  };

  /**
   * 自动生成直播地址
   *
   */
  export const fetchFlowAddrAuto = (type: string) =>
    request<
      Request.IResponse<{
        play_url: string;
        pull_url: string;
        push_url: string;
      }>
    >(`/create_url?type=${type}`, {
      prefix: RequestConstant.module.ipingestman,
      method: 'GET',
    });

  // ----------------------------------------------------

  /**
   * 获取直播计划列表
   *
   * @param {ActivityType.ILiveSearchParam} params
   * @return {*}
   */
  export const fetchLiveList = (params: ActivityType.ILiveSearch) => {
    return request<Request.IResponseList<ActivityType.ILive>>(
      '/v1/general-schedule/man/',
      {
        method: 'GET',
        params: {
          ...params,
          str: new Date().getTime(),
        },
        prefix: RequestConstant.PREFIX.LIVE_MANAGE,
      },
    );
  };

  /**
   * 通过 id 获取直播详情
   *
   * @param {string} _id
   * @return {*}
   */
  export const fetchLive = (_id: string) => {
    return request<Request.IResponse<ActivityType.ILive>>(
      '/v1/general-schedule/man/detail/',
      {
        params: {
          _id,
          str: new Date().getTime(),
        },
        prefix: RequestConstant.PREFIX.LIVE_MANAGE,
      },
    );
  };

  // 获取角色信息
  export const searchRoles = (params: any) => request('/v1/role/general/search', {
  method: 'POST',
  data: params,
  prefix: RequestConstant.module.unifiedplatform,
});

  // /**
  //  * 查询主讲人列表
  //  *
  //  * @return {*}
  //  */
  // export const fetchSpeakers = () => {
  //   return request<Request.IUnifiedResponse<ActivityType.IBasicItem[]>>('/v1/base/data/database/get/show/base/data', {
  //     prefix: RequestConstant.module.unifiedplatform
  //   })
  // }

  /**
   * 创建活动直播计划
   *
   * @param {ActivityType.ILive[]} lives
   * @return {*}
   */
  export const addLive = (lives: ActivityType.ILiveOrigin[]) => {
    return request<Request.IResponse>('/v1/general-schedule/man/', {
      method: 'POST',
      data: lives,
      prefix: RequestConstant.PREFIX.LIVE_MANAGE,
    });
  };

  /**
   * 修改活动直播计划
   *
   * @param {ActivityType.ILive[]} lives
   * @return {*}
   */
  export const updateLive = (live: ActivityType.ILiveOrigin) => {
    return request<Request.IResponse>('/v1/general-schedule/man/', {
      method: 'PATCH',
      data: {
        ...live,
      },
      prefix: RequestConstant.PREFIX.LIVE_MANAGE,
    });
  };

  /**
   * 活动直播任务开始
   *
   * @param {string} _id
   * @return {*}
   */
  export const updateLiveToStart = (_id: string) => {
    return request<Request.IResponse>('/v1/general-schedule/man/start/', {
      method: 'PATCH',
      data: {
        _id,
      },
      prefix: RequestConstant.PREFIX.LIVE_MANAGE,
    });
  };

  /**
   * 活动直播任务停止
   *
   * @param {string} _id
   * @return {*}
   */
  export const updateLiveToEnd = (_id: string) => {
    return request<Request.IResponse>('/v1/general-schedule/man/stop/', {
      method: 'PATCH',
      data: {
        _id,
      },
      prefix: RequestConstant.PREFIX.LIVE_MANAGE,
    });
  };

  /**
   * 删除活动直播计划
   *
   * @param {string[]} ids
   * @return {*}
   */
  export const deleteLiveBulk = (_ids: React.Key[]) => {
    return request<Request.IResponse>('/v1/general-schedule/man/', {
      method: 'DELETE',
      data: {
        _ids,
      },
      prefix: RequestConstant.PREFIX.LIVE_MANAGE,
    });
  };

  /**
   * 查询区域-树结构
   *
   * @return {*}
   */
  export const fetchAreaTree = () => {
    return request<Request.IResponse>('/area_tree/', {
      prefix: RequestConstant.module.ipingestman,
      method: 'GET',
    });
  };

  /**
   * 根据教室id获取直播流地址
   *
   * @param {number} id
   */
  export const fetchFlowAddrsByArea = (id: number) => {
    // return request<Request.IResponse<ActivityType.IFlowClassroom>>('/area_urls', {
    return request<Request.IResponse<ActivityType.IFlowClassroom>>(
      '/area_voice_urls',
      {
        prefix: RequestConstant.module.ipingestman,
        method: 'GET',
        params: {
          area: id,
        },
      },
    );
  };
  //------------------------------------------------------------------
  /**
   * 上传封面图片
   *
   * @param {string} img
   * @return {*}
   */
  export const uploadCover = (img: string) => {
    return request<Request.IResponse<{ url: string }>>(
      '/v1/general-schedule/man/cover/',
      {
        method: 'POST',
        data: {
          image_base64: img,
        },
        prefix: RequestConstant.PREFIX.LIVE_MANAGE,
      },
    );
  };

  /**
   * 获取默认封面图片
   *
   * @param {string} img
   * @return {*}
   */
  export const fetchDefaultCover = () => {
    return request<Request.IResponse<{ url: string }>>(
      '/v1/general-schedule/man/cover/default/',
      {
        method: 'GET',
        prefix: RequestConstant.PREFIX.LIVE_MANAGE,
      },
    );
  };

  /**
   * 获取资源目录
   *
   * @param {string} img
   * @return {*}
   */
  export const gettreebylevel = (gettreebylevel: number = 2) => {
    return request<any>(`/v1/folder/all/tree?level=${gettreebylevel}`, {
      method: 'GET',
      prefix: RequestConstant.module.rman,
    });
  };
  export const onloadTreeLeaf = (path: string, isOwner?: any) => {
    return request<any>(
      `/v1/folder/children?folderPath=${encodeURIComponent(
        path,
      )}%2F&isChildCount=true${isOwner ? '&isOwner=true' : ''}`,
      {
        method: 'GET',
        prefix: RequestConstant.module.rman,
      },
    );
  };
  export const filemerge = (data: any) => {
    return request<any>(`/rman/v1/upload/filemerge`, {method: 'post', data});
  };
  export const storageConfig = (data: any) => {
    return request<any>(`/v1/upload/v4/path`, {method: 'post', data, prefix: RequestConstant.module.rman});
  };
  export const fetchMergeStatus = (fileGuid: string) =>
    request<any>(`/rman/v1/upload/get/composite/task/details/${fileGuid}`, {method: 'GET'});
  export const unpublished = (data: any) => {
    return request(`/v1/general-schedule/man/upload/file`, {method: 'post', data});
  };
  export const getAllFieldsByType = (type: string) => {
    return request(
      `/v1/metadata/config/fields/upload?EntityType=${type}&Type=basic&ResourceType=model_sobey_object_entity`, {method: 'GET', prefix: RequestConstant.module.rman}
    );
  };

  export const getTaskDetail = (id: string) => {
    return request(
      `/v1/schedule-tasks/live/live_record/`, {method: 'GET', params: {id}}
    );
  };

  export const reStartTask = (data: any) => {
    return request(
      `/v1/schedule-tasks/live/live_record/`, {method: 'POST', data: data}
    );
  };
  

}
export default activityManagementService;
