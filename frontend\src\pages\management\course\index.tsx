import React, { useState, useEffect, useRef } from 'react';
import moment from 'moment';
import {
  Modal,
  Progress,
  Button,
  Spin,
  message,
  Table,
  Form,
  Input,
  Select,
  Tooltip,
} from 'antd';
import './index.less';
import PlanSearch from '@/components/PlanSearch';
import AddCourseModal from '@/components/AddCourseModal';
import courseManagementService from '@/service/courseManagementService';
import VoiceModal, { VoiceModalRef } from './components/VoiceModal';
const { Option } = Select;
const OverviewPlan: React.FC<{}> = () => {
  const [searchform] = Form.useForm();
  const [current, setCurrent] = useState<number>(1); //当前页码
  const [totalPage, setTotalPage] = useState<number>(0); //素材总数
  const [selectedRowKeys, setSelectedRowKeys] = useState<Array<string>>([]);
  const [newSelectedRows, setNewSelectedRows] = useState([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [addCourseVis, setAddCourseVis] = useState<boolean>(false);
  const [dataSource, setDataSource] = useState<CourseType.IScheduleData[]>([]);
  const [oneOrBatch, setOneOrBatch] = useState<boolean>(true); //批量或单独
  const [operationData, setOperationData] =
    useState<CourseType.IScheduleData>();
  const actionRef = useRef<VoiceModalRef>();

  const [isDeleteModalVisible, setIsDeleteModalVisible] = useState(false); //删除modal
  const [deleteLoading, setDeleteLoading] = useState(false);

  const [distinguish, setdistinguish] = useState<boolean>(false); //语音识别modal
  const [distinguishOrNot, setdistinguishOrNot] = useState<boolean>(false);

  const [translate, settranslate] = useState<boolean>(false); //翻译转写modal
  const [translateOrNot, settranslateOrNot] = useState<boolean>(false);

  const [searchData, setSearchData] = useState<any>();

  useEffect(() => {
    getList();
  }, [current, searchData]);
  const getList = () => {
    setLoading(true);
    // let param = `page=${current}&size=${30}&schedule_type=${1}&key=${moment().format('x')}`
    let param = `page=${current}&size=${30}`;
    if (searchData) {
      const {
        teacher,
        serialNumber,
        course,
        classroom,
        section,
        week,
        weekly,
      } = searchData;
      teacher && (param = param + `&teacher=${teacher.split(',')[1]}`);
      serialNumber && (param = param + `&course_no=${serialNumber}`);
      classroom &&
        (param =
          param + `&area=${classroom[classroom.length - 1].split(',')[0]}`);
      section && (param = param + `&course_section=${section}`);
      week && (param = param + `&week=${week}`);
      weekly && (param = param + `&times=${weekly}`);
      course.coursedetail &&
        (param =
          param +
          `&${
            course.courseType === 'courseNumber' ? 'course_id' : 'course_name'
          }=${course.coursedetail}`);
    }
    courseManagementService.getCourseScheduleList(param).then((res: any) => {
      setLoading(false);
      if (res && res.error_msg === 'Success') {
        setDataSource(res.extend_message.results);
        setTotalPage(res.extend_message.total);
      }
    });
  };
  const columns = [
    {
      title: '课程名称',
      dataIndex: 'course_name',
      key: 'course_name',
      width: '20%',
      render: (text: string) => {
        return <span title={text}>{text}</span>;
      },
    },
    {
      title: '课程号',
      dataIndex: 'course_id',
      key: 'course_id',
      align: 'center',
    },
    {
      title: '课序号',
      dataIndex: 'course_no',
      key: 'course_no',
      align: 'center',
    },
    {
      title: '循环周次',
      dataIndex: 'times',
      key: 'times',
      align: 'center',
    },
    {
      title: '星期',
      dataIndex: 'week',
      key: 'week',
      align: 'center',
    },
    {
      title: '节次',
      dataIndex: 'course_section',
      key: 'course_section',
      align: 'center',
      render: (text: string) => {
        return <span>{text.replace(',', '-')}</span>;
      },
    },
    {
      title: '教师',
      dataIndex: 'teacher_name',
      key: 'teacher_name',
      align: 'center',
    },
    {
      title: '教室',
      dataIndex: 'area',
      key: 'area',
      align: 'center',
    },
    {
      title: '语音识别状态',
      dataIndex: 'is_voice_identify',
      key: 'is_voice_identify',
      align: 'center',
      render: (text: boolean) => {
        return text ? (
          <span style={{ color: 'green' }}>开启 </span>
        ) : (
          <span style={{ color: 'red' }}>关闭</span>
        );
      },
    },
    {
      title: '语音翻译状态',
      dataIndex: 'is_voice_translation',
      key: 'is_voice_translation',
      align: 'center',
      render: (text: boolean) => {
        return text ? (
          <span style={{ color: 'green' }}>开启 </span>
        ) : (
          <span style={{ color: 'red' }}>关闭</span>
        );
      },
    },
    {
      title: '语音合成状态',
      dataIndex: 'is_voice_tts',
      key: 'is_voice_tts',
      align: 'center',
      render: (text: boolean) => {
        return text ? (
          <span style={{ color: 'green' }}>开启 </span>
        ) : (
          <span style={{ color: 'red' }}>关闭</span>
        );
      },
    },
    {
      title: '发起语音识别的视频流',
      key: 'voice_identify_stream',
      align: 'center',
      width: 120,
      render(record: any) {
        const v = record?.voice_identify_stream;
        return v ? (
          <Tooltip title={v}>
            <p
              style={{
                whiteSpace: 'nowrap',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
              }}
            >
              {v}
            </p>
          </Tooltip>
        ) : (
          '无'
        );
      },
    },
    {
      title: '操作',
      key: 'stream_name_voice_operation',
      align: 'center',
      width: 120,
      render(record: any) {
        return [
          <Button
            type="link"
            onClick={async () => {
              actionRef.current?.setParams({
                ...record,
                area: record.area_id,
              });
              actionRef.current?.setInitialState(
                record?.voice_identify_stream?.split(',') ?? [],
              );
              actionRef.current?.setOpen(true);
            }}
          >
            修改
          </Button>,
        ];
      },
    },
    // {
    //   title: '操作',
    //   dataIndex: 'operation',
    //   key: 'operation',
    //   width: '220px',
    //   render: (_text: any, record: CourseType.IScheduleData) => {
    //     return (
    //       <div className="operation">
    //         {
    //           record.is_voice_identify ?
    //             <a
    //               onClick={() => {
    //                 setOneOrBatch(true)
    //                 setOperationData(record)
    //                 setdistinguishOrNot(false)
    //                 setdistinguish(true)
    //               }}
    //             >禁用识别</a> :
    //             <a
    //               onClick={() => {
    //                 setOneOrBatch(true)
    //                 setOperationData(record)
    //                 setdistinguishOrNot(true)
    //                 setdistinguish(true)
    //               }}
    //             >启用识别</a>
    //         }
    //         {
    //           record.is_voice_translation ?
    //             <a
    //               onClick={() => {
    //                 setOneOrBatch(true)
    //                 setOperationData(record)
    //                 settranslateOrNot(false)
    //                 settranslate(true)
    //               }}
    //             >禁用翻译</a> :
    //             <a
    //               onClick={() => {
    //                 setOneOrBatch(true)
    //                 setOperationData(record)
    //                 settranslateOrNot(true)
    //                 settranslate(true)
    //               }}
    //             >启用翻译</a>
    //         }
    //         <a
    //           onClick={() => {
    //             setOneOrBatch(true)
    //             setOperationData(record)
    //             setIsDeleteModalVisible(true)
    //           }}
    //         >删除</a>
    //       </div>
    //     )
    //   },
    // }
  ];
  // const rowSelection = {
  //   onChange: (newSelectedRowKeys: Array<any>, newSelectedRows: any) => {
  //     setSelectedRowKeys(newSelectedRowKeys)
  //     setNewSelectedRows(newSelectedRows)
  //   },
  //   selectedRowKeys: selectedRowKeys
  // }
  const changepage = (page: number) => {
    setCurrent(page);
  };
  const detection = () => {
    let gonon = true;
    if (oneOrBatch) {
      gonon = !!(operationData?.schedule_status !== 2);
    } else {
      gonon = newSelectedRows.some((item: any) => {
        return item.schedule_status !== 2;
      });
    }
    return gonon;
  };
  const handleDeleteOk = () => {
    if (!detection()) {
      message.info('课程直播中，无法操作');
      return;
    }
    setDeleteLoading(true);
    let param = [];
    if (oneOrBatch) {
      param.push(operationData ? operationData._id : '');
    } else {
      param = [...selectedRowKeys];
    }
    courseManagementService
      .deleteSchedule({
        _ids: [...param],
      })
      .then((res: any) => {
        if (res && res.error_msg === 'Success') {
          message.success('删除成功');
        } else {
          message.error('删除失败');
        }
        getList();
        setSelectedRowKeys([]);
        setDeleteLoading(false);
        setIsDeleteModalVisible(false);
      });
  };
  // 语音识别
  const handleDistinguishOK = () => {
    if (!detection()) {
      message.info('课程直播中，无法操作');
      return;
    }
    let param = [];
    if (oneOrBatch) {
      param.push(operationData ? operationData._id : '');
    } else {
      param = [...selectedRowKeys];
    }
    courseManagementService
      .identifySchedule({
        action: distinguishOrNot ? 'on' : 'off',
        _ids: [...param],
      })
      .then((res: any) => {
        if (res && res.error_msg === 'Success') {
          message.success(distinguishOrNot ? '启用成功' : '禁用成功');
        }
        getList();
        setSelectedRowKeys([]);
        setdistinguish(false);
      });
  };
  // 翻译转写
  const handleTranslateOK = () => {
    if (!detection()) {
      message.info('课程直播中，无法操作');
      return;
    }
    let param = [];
    if (oneOrBatch) {
      param.push(operationData ? operationData._id : '');
    } else {
      param = [...selectedRowKeys];
    }
    courseManagementService
      .translationSchedule({
        action: translateOrNot ? 'on' : 'off',
        _ids: [...param],
      })
      .then((res: any) => {
        if (res && res.error_msg === 'Success') {
          message.success(translateOrNot ? '启用成功' : '禁用成功');
        }
        getList();
        setSelectedRowKeys([]);
        settranslate(false);
      });
  };
  const searchList = () => {
    // console.log(searchform.getFieldsValue())
    setCurrent(1);
    setSearchData(searchform.getFieldsValue());
  };
  const resetData = () => {
    searchform.resetFields();
    searchList();
  };
  return (
    <div className="overview">
      <PlanSearch
        formdata={searchform}
        searchList={searchList}
        resetList={resetData}
      />
      {/* <div className='buttom-box'>
        <div><Button type='primary' onClick={() => (setAddCourseVis(true))}>手动选择</Button></div>
        <div className='batch-operation'>
          <Button
            type='primary'
            disabled={newSelectedRows.some((item: any) => { return item.voice_identify === true }) || !selectedRowKeys.length}
            onClick={() => {
              setdistinguishOrNot(true)
              setOneOrBatch(false)
              setdistinguish(true)
            }}
          >启用语音识别</Button>
          <Button
            type='primary'
            disabled={newSelectedRows.some((item: any) => { return item.voice_identify === false }) || !selectedRowKeys.length}
            onClick={() => {
              setdistinguishOrNot(false)
              setOneOrBatch(false)
              setdistinguish(true)
            }}
          >禁用语音识别</Button>
          <Button
            type='primary'
            disabled={newSelectedRows.some((item: any) => { return item.voice_translation === true }) || !selectedRowKeys.length}
            onClick={() => {
              settranslateOrNot(true)
              setOneOrBatch(false)
              settranslate(true)
            }}
          >启用翻译转写</Button>
          <Button
            type='primary'
            disabled={newSelectedRows.some((item: any) => { return item.voice_translation === false }) || !selectedRowKeys.length}
            onClick={() => {
              settranslateOrNot(false)
              setOneOrBatch(false)
              settranslate(true)
            }}
          >禁用翻译转写</Button>
          <Button type='primary'
            disabled={!selectedRowKeys.length}
            onClick={() => {
              setOneOrBatch(false)
              setIsDeleteModalVisible(true)
            }}
          >批量删除</Button>
        </div>
      </div> */}
      <Table
        loading={loading}
        dataSource={dataSource}
        columns={columns}
        scroll={{ y: 480 }}
        rowKey="_id"
        // rowSelection={{
        //   ...rowSelection
        // }}
        pagination={{
          current: current,
          showSizeChanger: true,
          showQuickJumper: true,
          defaultPageSize: 30,
          total: totalPage,
          onChange: changepage,
        }}
      />
      {/* <AddCourseModal
        modalVisible={addCourseVis}
        modalClose={() => (setAddCourseVis(false))}
        refresh={getList}
      /> */}
      <Modal
        title="删除"
        visible={isDeleteModalVisible}
        onOk={handleDeleteOk}
        onCancel={() => setIsDeleteModalVisible(false)}
        confirmLoading={deleteLoading}
      >
        确定要删除该计划吗？
      </Modal>
      <Modal
        title={distinguishOrNot ? '启用' : '禁用'}
        visible={distinguish}
        onOk={handleDistinguishOK}
        onCancel={() => setdistinguish(false)}
      >
        确定要{distinguishOrNot ? '启用' : '禁用'}语音识别？
      </Modal>
      <Modal
        title={translateOrNot ? '启用' : '禁用'}
        visible={translate}
        onOk={handleTranslateOK}
        onCancel={() => settranslate(false)}
      >
        确定要{translateOrNot ? '启用' : '禁用'}翻译转写
      </Modal>
      <VoiceModal
        actionRef={actionRef}
        onSuccess={() => {
          getList();
        }}
      />
    </div>
  );
};

export default OverviewPlan;
