/*
 * @Author: 冉志诚
 * @Date: 2023-08-02 16:17:30
 * @LastEditTime: 2023-08-02 16:17:38
 * @FilePath: \frontend\src\pages\live\Create\components\DetailModal\index.tsx
 * @Description:
 */

import { UpFormResponse } from '@/api';
import { LiveService } from '@/api/live';
import RequestConstant from '@/constant/request';
import Create from '@/pages/live/Create';
import { Button, Input, Modal, ModalProps, message } from 'antd';
import React, { useImperativeHandle, useRef } from 'react';
import { useImmer } from 'use-immer';

const DetailModal: React.FC<ClassModalProps> = ({
  actionRef: propActionRef,
  onSuccess,
}) => {
  const [loading, setLoading] = useImmer(false);
  const [state, setState] = useImmer<DetailModalState>({
    open: false,
    readonly: false,
    id: undefined,
    hideFormFooter: true,
  });

  const [reason, setReason] = useImmer('');
  useImperativeHandle(
    propActionRef,
    () => {
      return {
        setState,
      };
    },
    [],
  );
  const modalConfig: ModalProps = {
    open: state.open,
    // title: '班级选择',
    onCancel() {
      // rejectOrPass(true);
      setState((draft) => {
        draft.open = false;
      });
    },
    width: 1000,
    cancelButtonProps: {
      loading,
    },
    // maskClosable: false,
    destroyOnClose: true,
    //footer居中
    okText: '通过',
    cancelText: '拒绝',
    footer: state.hideModalFooter ? null : (
      <div
        style={{
          width: '100%',
          textAlign: 'center',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
        }}
      >
        <Button
          loading={loading}
          onClick={() => {
            rejectOrPass(true);
          }}
        >
          拒绝
        </Button>
        <Button
          loading={loading}
          type="primary"
          onClick={() => {
            rejectOrPass();
          }}
        >
          通过
        </Button>
      </div>
    ),
  };
  async function rejectOrPass(reject = false) {
    let body: LiveService.AuditBody = {
      id: state.id!,
      is_pass: !reject,
      audit_reason: reason,
    };
    setLoading(true);
    try {
      if (reject) {
        body.audit_reason = '拒绝';
        Modal.confirm({
          title: '填写拒绝理由？',
          cancelButtonProps: {
            hidden: true,
          },
          content: (
            <Input.TextArea
              onChange={(e) => {
                setReason(e.target.value);
              }}
            />
          ),
          centered: true,
          async onOk(...args) {
            await beginAudit(body);
          },
        });
      } else {
        await beginAudit(body);
      }
    } catch (error: any) {
      console.error(error);
      message.error(error.message ?? '操作失败');
    }
    setLoading(false);
  }
  async function beginAudit(body: LiveService.AuditBody) {
    const { error_msg, error_code } = await LiveService.audit(body);
    if (error_code !== RequestConstant.CODE.SUCCESS_LIVE_MANAGE) {
      throw new Error(error_msg);
    } else {
      message.success('操作成功');
      onSuccess?.();
      setState((draft) => {
        draft.open = false;
      });
    }
  }
  return (
    <Modal {...modalConfig}>
      <Create inModal {...state} hideFooter={state.hideFormFooter} />
    </Modal>
  );
};

interface ClassModalProps {
  actionRef: React.MutableRefObject<DetailRefType | null>;
  onSuccess?: () => void;
}
export default DetailModal;
DetailModal.displayName = 'DetailModal';
export type DetailRefType = {
  setState: (state: DetailModalState) => void;
};

type DetailModalState = {
  open: boolean;
  readonly: boolean;
  id?: string;
  type?: 'rapid' | 'series';
  hideModalFooter?: boolean;
  hideFormFooter?: boolean;
};
