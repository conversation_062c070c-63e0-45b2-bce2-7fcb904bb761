declare namespace Request {
  /**
   * 通用response
   */
  export interface IResponse<T = object | string | [] | null> {
    error_code: string;
    error_msg: string;
    extend_message: T;
  }

  export interface IUnifiedResponse<T = object | string | [] | null> {
    errorCode: string;
    errorMsg: string;
    extendMessage: T;
  }

  export interface IPager<T> {
    page?: number;
    size?: number;
    total?: number;
    results: Array<T>;
  }

  export interface IResponseList<T = object> extends IResponse<IPager<T>> {}
}
