/*
 * @Author: 李武林
 * @Date: 2021-08-20 13:45:16
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2023-08-23 10:18:59
 * @FilePath: \frontend\src\utils\headerConfig.ts
 * @Description:
 *
 * Copyright (c) 2022 by 李武林/索贝数码, All Rights Reserved.
 */
const isDev = process.env.NODE_ENV === 'development';
const config = {
  safeMode: false,
  adminList: ['admin'],
  defaultAvatar: require('@/static/images/defualt-avatar.png'),
  iconScript: 'iconfont.js',
  themeStorage: 'theme_config',
  baseUrl: isDev ? 'http://**************' : '',
  pageSizeOptions: ['12', '24', '48', '60', '90', '120'],
};
export default config;
