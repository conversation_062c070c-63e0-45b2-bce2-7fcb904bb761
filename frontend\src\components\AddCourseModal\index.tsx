import React, { useState, useEffect } from 'react';
import { Modal, Progress, Button, Spin, message, Table, Form, Input, Select, InputNumber } from 'antd';
import courseManagementService from '@/service/courseManagementService'
import ClassroomItem from '@/components/formItemBox/classroomItem'
import TeacherItem from '@/components/formItemBox/teacherItem'
import './index.less'
const { Option } = Select
interface CreateModalProps {
    modalVisible: boolean;
    modalClose: () => void;
    refresh: () => void // 刷新
}
const AddCourseModal: React.FC<CreateModalProps> = (props) => {
    const { modalClose, modalVisible, refresh } = props;
    const [searchform] = Form.useForm()
    const [current, setCurrent] = useState<number>(1) //当前页码
    const [size, setSize] = useState<number>(20) //当前页码
    const [totalPage, setTotalPage] = useState<number>(0) //素材总数
    const [selectedRowKeys, setSelectedRowKeys] = useState<Array<any>>([]);
    const [selectedRows, setSelectedRows] = useState<any>([]);
    const [loading, setLoading] = useState<boolean>(false);
    const [dataSource, setDataSource] = useState<any>([]);
    const [course, setCourse] = useState<any>({})
    const [collegeList, setCollegeList] = useState<any>()
    const [teacherList, setTeacherList] = useState<any>()
    const [courseType, setCourseType] = useState<string[]>([])
    const [searchData, setSearchData] = useState<any>()
    useEffect(() => {
        if (modalVisible) {
            getcourselist()
            getfieldslist()
        }
    }, [modalVisible])
    useEffect(() => {
        if (modalVisible) {
            getList()
        }
    }, [modalVisible, current, searchData, size])
    const getList = () => {
        let param = `page=${current}&size=${size}&is_delete=0&area=`
        if (searchData) {
            const { teacher, serialNumber, course, classroom, college } = searchData
            teacher && (param = param + `&teacher_name=${teacher.split(',')[0]}`)
            college && (param = param + `&college=${college.split(',')[1]}`)
            serialNumber && (param = param + `&course_no=${serialNumber}`)
            classroom && (param = param + `&area=${classroom[classroom.length - 1].split(',')[0]}`)
            course.coursedetail && (param = param + `&${course.courseType === "courseNumber" ? 'course_id' : 'course_name'}=${course.coursedetail}`)
        }
        setLoading(true)
        courseManagementService.getCurrSearchList(param).then((res) => {
            setLoading(false)
            if (res && res.error_code === 'cloud_sc.0000.0000') {
                setDataSource(res.extend_message.results)
                setTotalPage(res.extend_message.total)
            }
        })
    }
    const getcourselist = () => {
        courseManagementService.getCourse().then((res) => {
            if (res && res.success && res.data) {
                setCourse(res.data)
                setCourseType(res.data.selectCurriculumResponse.courseNameAggregate)
            }
        })
    }
    const getfieldslist = () => {
        courseManagementService.getfields().then((res) => {
            if (res && res.errorCode === 'success') {
                res.extendMessage.forEach((item: any) => {
                    if (item.fieldName === 'teacher') {
                        setTeacherList(item.controlData ? JSON.parse(item.controlData) : {})
                    }
                    if (item.fieldName === 'college') {
                        setCollegeList(item.controlData ? JSON.parse(item.controlData) : {})
                    }
                })
            }
        })
    }
    const columns = [
        {
            title: '课程名称',
            dataIndex: 'course_name',
            key: 'course_name',
        },
        {
            title: '课程号',
            dataIndex: 'course_id',
            key: 'course_id',
        },
        {
            title: '课序号',
            dataIndex: 'course_no',
            key: 'course_no',
        },
        {
            title: '开课学院',
            dataIndex: 'college_name',
            key: 'college_name',
        },
        {
            title: '负责教师',
            dataIndex: 'teacher_name',
            key: 'teacher_name',
        },
        {
            title: '循环周次',
            dataIndex: 'times',
            key: 'times',
        },
        {
            title: '区域',
            dataIndex: 'area_name',
            key: 'area_name',
        },
    ];
    const coursetypechange = (value: any) => {
        searchform.setFieldsValue({ course: { coursedetail: '' } })
        value === 'courseNumber'
            ? setCourseType(course.selectCurriculumResponse.courseNumberAggregate)
            : setCourseType(course.selectCurriculumResponse.courseNameAggregate)
    }
    const rowSelection = {
        onChange: (newSelectedRowKeys: Array<any>, newSelectedRows: any) => {
            setSelectedRowKeys(newSelectedRowKeys)
            setSelectedRows(newSelectedRows)
        },
    }
    const changepage = (page: number) => {
        setCurrent(page)
    }
    const searchCourse = () => {
        setCurrent(1)
        setSearchData(searchform.getFieldsValue())
    }
    const resetData = () => {
        searchform.resetFields()
        searchCourse()
    }
    const modalOk = () => {
        courseManagementService.addCourseSchedule(JSON.stringify(selectedRows)).then((res) => {
            if (res && res.error_code === 'livemanage.0000.0000') {
                message.success('添加成功')
            } else {
                message.error(res.extend_message)
            }
            resetData()
            refresh()
            modalClose()
        })
    }
    const onShowSizeChange = (current: number, pageSize: number) => {
        setSize(pageSize)
        setCurrent(current)
        // setCurrent(1)
    }
    return (
        <Modal
            destroyOnClose
            title='选择课程'
            visible={modalVisible}
            closable={false}
            footer={[
                <Button key="submit" type="primary" onClick={modalOk} disabled={!selectedRows.length}>确定</Button>,
                <Button
                    key="back"
                    onClick={() => {
                        resetData()
                        modalClose()
                    }}
                >取消</Button>,
            ]}
            width={1500}
        >
            <div className='add-course-modal'>
                <Form
                    layout={'inline'}
                    name="basic"
                    labelCol={{ span: 7 }}
                    form={searchform}
                >
                    <Form.Item>
                        <Input.Group compact>
                            <Form.Item name={['course', 'courseType']} noStyle>
                                <Select
                                    defaultValue="courseName"
                                    style={{ width: 100 }}
                                    onChange={coursetypechange}
                                >
                                    <Option value="courseName">课程名称</Option>
                                    <Option value="courseNumber">课程号</Option>
                                </Select>
                            </Form.Item>
                            <Form.Item name={['course', 'coursedetail']} noStyle>
                                <Input placeholder="请输入" style={{ width: 200 }} />
                            </Form.Item>
                        </Input.Group>
                    </Form.Item>
                    <Form.Item
                        label='课序号'
                        name={'serialNumber'}
                    >
                        <Input style={{ width: 140 }} />
                    </Form.Item>
                    <TeacherItem
                        multiple={false}
                        required={false}
                        message={'请选择教师'}
                        label={'教师'}
                        name={'teacher'}
                        key="teacher1"
                    />
                    {/* <Form.Item
                        label='教师'
                        name='teacher'
                    >
                        <Select
                            showSearch
                            style={{ width: 150 }}
                            placeholder='请选择教师'
                            allowClear={true}
                        >
                            {teacherList &&
                                Object.keys(teacherList).map((key: any) => {
                                    return (
                                        <Option value={teacherList[key]} key={'teacher' + key}>
                                            {teacherList[key]}
                                        </Option>
                                    )
                                })}
                        </Select>
                    </Form.Item> */}
                    <Form.Item
                        label='学院'
                        name='college'
                    >
                        <Select
                            showSearch
                            style={{ width: 150 }}
                            placeholder='请选择学院'
                            allowClear={true}
                        >
                            {collegeList &&
                                Object.keys(collegeList).map((key: any) => {
                                    return (
                                        <Option value={collegeList[key] + ',' + key} key={'teacher' + key}>
                                            {collegeList[key]}
                                        </Option>
                                    )
                                })}
                        </Select>
                    </Form.Item>
                    <ClassroomItem required={false} key="classroom" form={searchform} />
                    <Form.Item><Button type="primary" onClick={searchCourse}>查询</Button></Form.Item>
                    <Form.Item><Button onClick={resetData}>重置</Button></Form.Item>
                </Form>
                <Table
                    loading={loading}
                    dataSource={dataSource}
                    columns={columns}
                    bordered
                    scroll={{ y: 350 }}
                    rowKey="_id"
                    rowSelection={{
                        ...rowSelection
                    }}
                    pagination={{
                        onShowSizeChange: onShowSizeChange,
                        showSizeChanger: true,
                        current: current,
                        pageSize: size,
                        total: totalPage,
                        onChange: changepage,
                    }}
                />
            </div>
        </Modal>
    );
};

export default AddCourseModal;
