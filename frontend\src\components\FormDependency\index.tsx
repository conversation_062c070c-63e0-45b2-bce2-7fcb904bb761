import { Form, FormInstance, FormItemProps } from 'antd';
import { NamePath } from 'antd/lib/form/interface';
import get from 'rc-util/lib/utils/get';
import React from 'react';
/*
 * @Author: spring breeze
 * @Date: 2023-07-24 11:04:53
 * @LastEditTime: 2023-07-24 11:15:29
 * @FilePath: \frontend\src\components\FormDependency\index.tsx
 * @Description:
 */
export type FormDependencyProps<T = Record<string, any>> = Omit<
  FormItemProps<any>,
  'name' | 'noStyle' | 'children' | 'label'
> & {
  name: NamePath[];
  originDependencies?: NamePath[];
  ignoreFormListField?: boolean;
  children: RenderChildren<T>;
};
declare type RenderChildren<Values = any> = (
  values: Record<string, any>,
  form: FormInstance<Values>,
) => React.ReactNode;

//TODO: 后续优化
const FormDependency = React.memo(
  <T,>({
    name: nameList,
    originDependencies = nameList,
    children,
    ...rest
  }: FormDependencyProps<T>) => {
    const form = Form.useFormInstance();

    return (
      <Form.Item
        {...rest}
        noStyle
        shouldUpdate={(prevValues, nextValues, info) => {
          if (typeof rest.shouldUpdate === 'boolean') {
            return rest.shouldUpdate;
          } else if (typeof rest.shouldUpdate === 'function') {
            return rest.shouldUpdate?.(prevValues, nextValues, info);
          }
          const r = originDependencies.some((name) => {
            const path = [name.toString()];

            return get(prevValues, path) !== get(nextValues, path);
          });
          return r;
        }}
      >
        {(form) => {
          const values = form.getFieldsValue(true);
          return children?.(values, form as any);
        }}
      </Form.Item>
    );
  },
);
FormDependency.displayName = 'FormDependency';
export default FormDependency;
