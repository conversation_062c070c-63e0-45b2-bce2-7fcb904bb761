const routes = [
  // {
  //   exact: true,
  //   path: '/',
  //   redirect: '/apply',
  // },
  {
    path: '/mg',
    component: '@/layouts/mgLayout',
    routes: [
      {
        path: '/mg/course',
        component: '@/pages/management/course',
      },
      {
        path: '/mg/course/task',
        component: '@/pages/management/course/taskList',
      },
      {
        path: '/mg/activity',
        component: '@/pages/management/activity',
      },
      {
        path: '/mg/activity/flow',
        component: '@/pages/management/activity/flow',
      },
      {
        path: '/mg/activity/createlive',
        component: '@/pages/management/activity/createLive',
      },
      {
        path: '/mg/live',
        component: '@/pages/management/live',
      },
    ],
  },
  {
    path: '/live',
    component: '@/pages/live/Layout',
    routes: [
      {
        path: 'my',
        title: '我的直播',
        routes: [
          {
            path: '/live/my/apply',
            component: '@/pages/live/My/Apply',
            title: '我申请的',
          },
          {
            path: '/live/my/speaker',
            component: '@/pages/live/My/Speaker',
            title: '我主讲的',
          },
          {
            path: '/live/my/collect',
            component: '@/pages/live/My/Collect',
            title: '我收藏的',
          },
          {
            path: '/live/my/share',
            component: '@/pages/live/My/Share',
            title: '我分享的',
          },
        ],
      },
      {
        path: 'share',
        component: '@/pages/live/Share',
        title: '共享直播',
      },
      {
        path: 'all',
        component: '@/pages/live/All',
        title: '全部直播',
      },

      {
        path: 'create',
        component: '@/pages/live/Create',
      },

      {
        path: 'control',
        component: '@/pages/live/Control',
      },
    ],
  },
  // {
  //   path: '/apply',
  //   component: '@/pages/apply',
  // },
  // {
  //   path: 'test',
  //   component: '@/pages/textPage',
  // },
  {
    component: '@/pages/404',
    title: '404',
  },
];

export default routes;
