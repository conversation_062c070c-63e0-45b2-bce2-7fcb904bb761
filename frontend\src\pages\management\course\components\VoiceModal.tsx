/*
 * @Author: spring breeze
 * @Date: 2023-07-24 15:03:51
 * @LastEditTime: 2023-07-24 15:08:33
 * @FilePath: \frontend\src\pages\management\course\components\VoiceModal.tsx
 * @Description:
 */
import { STREAM_NAME_MAP } from '@/constant/Stream';
import { areaStreamUrls, setAreaStreamUrls } from '@/service/live';
import { removeEmpty, pick } from '@/utils/utils';
import { Modal, Select, message } from 'antd';
import React, { useEffect, useImperativeHandle, useState } from 'react';
import { useImmer } from 'use-immer';

export type Item = {
  label: string;
  value: string;
};
const VoiceModal: React.FC<VoiceModalProps> = ({ actionRef, onSuccess }) => {
  const [open, setOpen] = useState(false);
  const [options, setOptions] = useState<Item[]>([]);
  const [params, setParams] = useState<Record<string, any>>({});
  const [value, setValue] = useState<string[]>([]);
  const [initialState, setInitialState] = useImmer<string[]>([]);

  useEffect(() => {
    if (options?.length > 0 && initialState?.length > 0) {
      const value: string[] = [];
      initialState.forEach((item) => {
        options.forEach((option) => {
          if (item === option.label) {
            value.push(option.value);
          }
        });
      });
      setValue(value);
    }
  }, [initialState, options]);
  useImperativeHandle(
    actionRef,
    (): VoiceModalRef => ({
      setOpen,
      setInitialState,
      setParams(params) {
        setParams(
          pick(params, [
            'area',
            'semester',
            'course_id',
            'course_no',
            'course_section',
            'week',
            'class_weekly',
          ]),
        );
      },
    }),
  );
  useEffect(() => {
    getData();
  }, [params]);
  async function getData() {
    try {
      if (Object.keys(params ?? {}).length === 0) return;
      const { extend_message: data } = await areaStreamUrls(params);
      const notNulData = removeEmpty(
        pick(data, [
          ...(Object.keys(STREAM_NAME_MAP) as any),
          ...Object.keys(STREAM_NAME_MAP).map((v) => v.slice(0, -4)),
        ]),
      );

      const res = Object.keys(notNulData)
        .filter((v: any) => STREAM_NAME_MAP.hasOwnProperty(v))
        .reduce((pre, cur) => {
          pre.push({
            label: STREAM_NAME_MAP[cur as keyof typeof STREAM_NAME_MAP],
            value: data[cur.slice(0, -4) as keyof typeof data] as any,
          });
          return pre;
        }, [] as Item[]);

      setOptions(res);
    } catch (error) {
      message.error('获取发起语音识别的视频流失败');
    }
  }
  return (
    <Modal
      visible={open}
      title="发起语音识别的视频流"
      onCancel={() => {
        setOpen(false);
      }}
      onOk={async () => {
        try {
          if (!value || value.length == 0) {
            message.error('请至少选择一个视频流');
            return;
          }
          const { extend_message } = await setAreaStreamUrls({
            ...params,
            area_id: params.area,
            voice_identify_stream: value?.map((item) => {
              return {
                stream_name: options.find((v) => v.value === item)?.label!,
                stream_url: item,
              };
            }),
          });
          onSuccess();
          setValue([]);
          message.success('修改发起语音识别成功');
          setOpen(false);
        } catch (_) {
          message.error('修改发起语音识别失败');
        }
      }}
      destroyOnClose
    >
      <Select
        style={{
          width: '100%',
        }}
        value={value}
        onChange={(value) => {
          setValue(value);
        }}
        placeholder="请选择发起语音识别的视频流"
        // 多选
        mode="multiple"
        options={options}
      ></Select>
    </Modal>
  );
};

interface VoiceModalProps {
  actionRef: React.MutableRefObject<VoiceModalRef | undefined>;
  onSuccess: () => void;
}

export type VoiceModalRef = {
  setOpen: (open: boolean) => void;
  setParams: (params: Record<string, any>) => void;
  setInitialState: (initialState: string[]) => void;
};
export default VoiceModal;
VoiceModal.displayName = 'VoiceModal';
