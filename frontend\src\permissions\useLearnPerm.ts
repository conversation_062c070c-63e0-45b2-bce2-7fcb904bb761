/*
 * @Author: lijin
 * @Description:
 * @Date: 2021-12-16 14:18:26
 * @LastEditTime: 2021-12-16 19:15:34
 * @FilePath: \frontend\src\permissions\useLearnPerm.ts
 */

import { useUpdateEffect } from 'ahooks';
import { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';

/*
 * @Author: 李晋
 * @Date: 2021-12-09 11:11:12
 * @Email: <EMAIL>
 * @LastEditTime: 2021-12-09 12:28:12
 * @Description: 我的学习部分的权限
 * @Company: Sobey
 */
export enum LearnModule {
  LearningPortalHomePage = 'LearningPortalHomePage', //首页
  LearningPortalCourse = 'LearningPortalCourse', //课程
  LearningPortalClassReview = 'LearningPortalClassReview', //在线课堂
  LearningPortalLiveStreaming = 'LearningPortalLiveStreaming', //直播
}

/**
 * 我的课程权限
 *
 * @export
 * @interface ICoursePerm
 */
export interface ICoursePerm {
  ifShowMenu: boolean;
  ifShowMooc: boolean;
  ifShowSpoc: boolean;
  ifIclassroom: boolean;
}

/**
 * 我的收藏权限
 *
 * @export
 * @interface ICollectionPerm
 */
export interface ICollectionPerm {
  ifShowMenu: boolean;
  ifShowMicro: boolean;
  ifShowReview: boolean;
}

/**
 * 我的笔记权限
 *
 * @export
 * @interface INotePerm
 */
export interface INotePerm {
  ifShowMenu: boolean;
  ifShowAll: boolean;
  ifShowCourse: boolean;
  ifShowReview: boolean;
  ifShowLive: boolean;
}
/**
 * 学习统计权限
 *
 * @export
 * @interface IStatisticPerm
 */
export interface IStatisticPerm {
  ifShowMenu: boolean;
}

const useLearnPerm = () => {
  const { modulesLearning } = useSelector<
    { global: any },
    { modulesLearning: string[] }
  >(state => state.global);

  const { parameterConfig } = useSelector<
    { global: any },
    { parameterConfig: any }
  >(state => state.global);

  // 我的课程权限
  const [coursePerm, setCoursePerm] = useState<ICoursePerm>({
    ifShowMenu: false,
    ifShowMooc: false,
    ifShowSpoc: false,
    ifIclassroom: false
  });
  // 我的收藏权限
  const [collectionPerm, setCollectionPerm] = useState<ICollectionPerm>({
    ifShowMenu: false,
    ifShowMicro: false,
    ifShowReview: false,
  });
  // 我的笔记权限
  const [notePerm, setNotePerm] = useState<INotePerm>({
    ifShowMenu: false,
    ifShowAll: false,
    ifShowCourse: false,
    ifShowReview: false,
    ifShowLive: false,
  });
  // 学习统计权限
  const [statisticPerm, setStatisticPerm] = useState<IStatisticPerm>({
    ifShowMenu: false,
  });

  /**
   * 获取我的课程权限
   *
   */
  function getMyCoursePerm() {
    let initPerm = {
      ifShowMenu: false,
      ifShowMooc: false,
      ifShowSpoc: false,
      ifIclassroom: false,
    };
    if (modulesLearning.includes(LearnModule.LearningPortalCourse)) {
      initPerm.ifShowMenu = true;
      initPerm.ifShowMooc = true;
      initPerm.ifShowSpoc = true;
    }
    if (parameterConfig.iclassroom == "true") {
      initPerm.ifIclassroom = true;
    }
    setCoursePerm(initPerm);
  }

  /**
   * 获取我的收藏权限
   *
   */
  function getMyCollectionPerm() {
    let initPerm = {
      ifShowMenu: false,
      ifShowMicro: false,
      ifShowReview: false,
    };
    if (modulesLearning.includes(LearnModule.LearningPortalCourse)) {
      initPerm.ifShowMenu = true;
      initPerm.ifShowMicro = true;
    }
    if (modulesLearning.includes(LearnModule.LearningPortalClassReview)) {
      initPerm.ifShowMenu = true;
      initPerm.ifShowReview = true;
    }
    setCollectionPerm(initPerm);
  }

  /**
   * 获取我的笔记菜单权限
   *
   */
  function getMyNotePerm() {
    let initPerm = {
      ifShowMenu: false,
      ifShowAll: false,
      ifShowCourse: false,
      ifShowReview: false,
      ifShowLive: false,
    };

    if (modulesLearning.includes(LearnModule.LearningPortalCourse)) {
      initPerm.ifShowAll = true;
      initPerm.ifShowMenu = true;
      initPerm.ifShowCourse = true;
    }
    if (modulesLearning.includes(LearnModule.LearningPortalClassReview)) {
      initPerm.ifShowAll = true;
      initPerm.ifShowMenu = true;
      initPerm.ifShowReview = true;
    }
    if (modulesLearning.includes(LearnModule.LearningPortalLiveStreaming)) {
      initPerm.ifShowAll = true;
      initPerm.ifShowMenu = true;
      initPerm.ifShowLive = true;
    }
    setNotePerm(initPerm);
  }

  /**
   * 获取学习统计菜单权限
   *
   */
  function getStatisticPerm() {
    let initPerm = {
      ifShowMenu: false,
    };
    if (modulesLearning.includes(LearnModule.LearningPortalCourse)) {
      initPerm.ifShowMenu = true;
    }
    setStatisticPerm(initPerm);
  }

  useEffect(() => {
    getMyCoursePerm();
    getMyCollectionPerm();
    getMyNotePerm();
    getStatisticPerm();
  }, [modulesLearning]);

  return { coursePerm, collectionPerm, notePerm, statisticPerm };
};

export default useLearnPerm;
