import React, { FC, useEffect, useMemo, useState } from 'react';
import './header.less';
import { useSelector } from '@@/plugin-dva/exports';
import { IGlobal } from '@/models/global';
import { Dropdown, Image, Menu, message, Divider, Badge, Popover } from 'antd';
import { Link, useDispatch, history, useHistory, useLocation } from 'umi';
import config from '@/utils/headerConfig';
// import { history } from '@@/core/history';
import IconFont from '@/components/iconFont';
import { intersection, size } from 'lodash';
import moduleCfg, {
  globalParams,
  ModuleCapumanCfg,
} from '@/permissions/moduleCfg';
import Icon from '@ant-design/icons';
import { ReactComponent as clip_icon } from '@/static/images/icons/clip_icon.svg';
import { ReactComponent as literature_icon } from '@/static/images/icons/literature_icon.svg';
import userApi from '@/api/user';
import MessageBox from '@/components/Message';
import { isDevelopment } from '@/utils/utils';
import NPUHeader from '../NPUHeader';

interface LinkItem {
  key: string;
  name: string;
  href?: string;
  target?: string;
  disabled?: boolean;
}

interface HeaderProps {
  subtitle?: string;
  // navList?: LinkItem[];
  showNav: boolean;
  navActive?: string;
  ifBack?: boolean;
  /** 自定义title */
  customTitle?: string;
}

interface NavProps {
  list: LinkItem[];
  active?: string;
}

interface UserAvatarProps {
  username: string;
  avatar: string;
  work: boolean;
  // teacher: boolean;
  student: boolean;
  admin: boolean;
  // rman: boolean;
  // joveone: boolean;
  workUrl: string;
  // personal: boolean;
  platform: string;
  utilization: boolean;
  capuman: boolean;
  onLogout: () => void;
}

const Nav: FC<NavProps> = ({ list, active }) => {
  const [navList, setNavList] = useState<any>([]);
  const [dropdownList, setDropdownList] = useState<any>([]);
  useEffect(() => {
    if (list.length > 0) {
      handleChange();
      window.addEventListener('resize', handleChange);
      return () => {
        window.removeEventListener('resize', handleChange);
      };
    }
  }, [list]);
  const handleChange = () => {
    if (list.length > 0) {
      const width = window.innerWidth > 1300 ? window.innerWidth : 1300;
      const logoWidth = title.length * 20; // 一个字20px
      const otherWidth = 470; // logo + 工具栏 + 消息 + 用户 + 间距
      const navWidth = width - logoWidth - otherWidth;
      let curWidth = 0;
      const i = list.findIndex((item: any) => {
        curWidth += item.name.length * 14 + 40;
        console.info(curWidth, navWidth);
        return curWidth > navWidth;
      });
      const index = i === -1 ? list.length : i - 1;
      const nav = list.slice(0, index);
      console.info(nav);
      const other = list.slice(index);
      setNavList(nav);
      setDropdownList(other);
    }
  };
  return (
    <div className="nav-menu-wrapper">
      {navList
        .filter((l) => !l.disabled)
        .map((link: any, index: number) =>
          link.href ? (
            <a
              key={index}
              className={
                active?.split('/')?.[1]?.startsWith(link.key) ? 'active' : ''
              }
              href={link.href}
              target={link.target}
            >
              <div className="nav-item">{link.name}</div>
            </a>
          ) : (
            <span key={index}>{link.name}</span>
          ),
        )}
      {navList.length < list.length && (
        <Dropdown
          overlay={
            <div className="drop-container">
              {dropdownList.map((item: any) => (
                <div
                  className="drop-item"
                  onClick={() => {
                    if (item.href) {
                      window.open(item.href, item.target || '_self');
                    }
                  }}
                >
                  {item.name}
                </div>
              ))}
            </div>
          }
          placement="bottomCenter"
          getPopupContainer={(e: any) => e.parentNode}
        >
          <span
            key="more"
            style={{ cursor: 'pointer', color: '#666', marginBottom: '4px' }}
          >
            更多
          </span>
        </Dropdown>
      )}
    </div>
  );
};

const UserAvatar: FC<UserAvatarProps> = ({
  username,
  avatar,
  work,
  // teacher,
  workUrl,
  student,
  admin,
  // personal,
  // rman,
  // joveone,
  onLogout,
  utilization,
  capuman,
  platform,
}) => {
  const { userInfo } = useSelector<any, IGlobal>((state) => state.global);
  const location = useLocation();
  // let workUrl = ""
  // let target = ""
  // if (teacher) {
  //   workUrl = "/learn/workbench/#/course"
  //   target = "my_teaching"
  // } else if (rman) {
  //   workUrl = "/rman/#/basic/rmanCenterList"
  //   target = "source_manage"
  // } else if (admin) {
  //   workUrl = "#/basic"
  //   target = "sys_manage"
  // } else if (joveone) {
  //   workUrl = "/joveone"
  //   target = "joveone"
  // }
  const managementLink = useMemo(() => {
    if (isDevelopment) {
      return `/unifiedplatform/#/management`;
    } else {
      return `/unifiedplatform/#/management`;
    }
  }, [location.pathname]);
  const menu = (
    <Menu>
      {work && (
        <Menu.Item>
          <a href={workUrl} target="work">
            工作台
          </a>
        </Menu.Item>
      )}
      {student && (
        <Menu.Item>
          <Link to="/learn/mycourse" target="my_study">
            我的学习
          </Link>
        </Menu.Item>
      )}
      {/* 只兰台屏蔽账号管理，知了不能屏蔽 */}
      {admin && platform != 'ILA' && (
        <Menu.Item>
          <a href={managementLink}>管理中心</a>
        </Menu.Item>
      )}
      {/* {personal && (
        <Menu.Item>
          {platform === 'ILA' ? (
            <Link to="/personal/info" target="personal_center">
              账号管理
            </Link>
          ) : (
            <Link to="/personal/home" target="personal_center">
              个人中心
            </Link>
          )}
        </Menu.Item>
      )} */}
      {utilization && (
        <Menu.Item>
          <a
            // href="/capubmam/#/utilization/archives/subject/preview"
            target="_blank"
            onClick={() => {
              if (
                userInfo.roles.some((item: any) => item.roleName === '利用')
              ) {
                window.location.href = `/capubmam/#/utilization/archives/open`;
              } else {
                window.location.href = `/capubmam/#/utilization/archives/subject/preview`;
              }
            }}
          >
            利用平台
          </a>
          {/* <Link to="/utilization">利用平台</Link> */}
        </Menu.Item>
      )}
      {capuman && (
        <Menu.Item>
          <a href="/capubmam/#/capubmam" target="_blank">
            管理平台
          </a>
        </Menu.Item>
      )}
      <Menu.Item onClick={onLogout}>退出登录</Menu.Item>
    </Menu>
  );

  return (
    <Dropdown overlay={menu} className="user-avatar-wrapper">
      <div>
        <Image
          src={
            avatar
              ? `${isDevelopment ? '/api' : ''}${avatar}`
              : config.defaultAvatar
          }
          fallback={config.defaultAvatar}
          preview={false}
        />
        <p className="user-name" title={username}>
          {username}
        </p>
      </div>
    </Dropdown>
  );
};

const OtherNav: FC<NavProps> = ({ list }) => {
  const getIcon = (iconName: string) => {
    const ClipIcon = (props: any) => <Icon component={clip_icon} {...props} />;
    const LiteratureIcon = (props: any) => (
      <Icon component={literature_icon} {...props} />
    );
    switch (iconName) {
      case 'joveone':
        return <IconFont type="iconzaixianbianji" />;
      case 'literature':
        return <LiteratureIcon />;
      case 'textclip':
        return <IconFont type="iconyuyinbianji" />;
      default:
        return <span></span>;
    }
  };
  //antd 版本不兼容
  const menu = (
    <Menu>
      {list
        .filter((l) => !l.disabled)
        .map((item: any, index: number) => {
          return (
            <Menu.Item key={index}>
              <a
                key={item.key}
                className="other"
                href={item.href}
                target={item.target}
              >
                {getIcon(item.key)}
                {item.name}
              </a>
            </Menu.Item>
          );
        })}
    </Menu>
  );
  return (
    <div className="other-nav-wrapper">
      {list.filter((l) => !l.disabled).length > 0 && (
        <Dropdown overlay={menu}>
          <div>
            <IconFont type="icongongjuxiang" />
            工具箱
          </div>
        </Dropdown>
      )}
    </div>
  );
};

const Header: FC<HeaderProps> = ({
  subtitle,
  // navList,
  showNav,
  navActive,
  ifBack = false,
  customTitle,
}) => {
  const dispatch = useDispatch();
  const { userInfo, menuShow } = useSelector<any, IGlobal>(
    (state) => state.global,
  );
  const [headerList, setHeaderList] = useState<any[]>([]);
  const { title, logoUrl, isShow } = useSelector<any, any>(
    (state) => state.themes,
  );
  const { modules, permissions, rmanGlobalParameter } = useSelector<
    { global: any },
    { modules: string[]; permissions: string[]; rmanGlobalParameter: string[] }
  >((state) => state.global);
  const handleLogout = async () => {
    window.location.href = `/unifiedlogin/v1/loginmanage/loginout/go`;
    // let res = await api.user.logout();
    // if (res && res.errorCode === 'success') {
    //   message.success('注销成功');
    //   // history.push('/basic?action=login');
    //   // window.location.replace(`/cvodweb`);
    // }
  };
  // console.log(111,modules)
  const [platform, setPlatform] = useState<any>(
    window.localStorage.getItem('upform_platform'),
  );
  const [unReadCount, setUnreadCount] = useState<number>();

  useEffect(() => {
    dispatch({
      type: 'global/initPlatform',
      callback: (res: any) => {
        setPlatform(res);
      },
    });
    if (window.innerWidth <= 768) {
      menuShowChange();
    }
    if (platform !== 'ILA') {
      getUnreadCount();
      const timer = setInterval(getUnreadCount, 10000);
      return () => {
        clearInterval(timer);
      };
    }
  }, []);

  useEffect(() => {
    if (platform !== 'ILA') {
      // settingApis.fetchHeaderList().then((res: any) => {
      //   if (res?.errorCode === 'success') {
      //     const list = res.extendMessage
      //       .filter((item: any) => item.name !== '在线剪辑')
      //       .map((item: any) => ({
      //         key: item.isSystem
      //           ? item.link.includes('rman')
      //             ? 'rman'
      //             : item.link.split('#/')?.[1]?.split('/')?.[0]
      //           : 'key',
      //         name: item.name,
      //         href: item.link,
      //         target: item.openWay
      //           ? item.link.split('/')?.[1] || item.name
      //           : null,
      //         disabled: item.disabled,
      //       }));
      //     setHeaderList(list);
      //   }
      // });
    }
  }, []);

  const stopJump = (e: any) => {
    if (platform === 'ILA') {
      e.preventDefault();
    }
  };

  const getUnreadCount = () => {
    userApi.reqUnreadMessageCount().then((res) => {
      if (res?.errorCode === 'success') {
        setUnreadCount(res.extendMessage);
      }
    });
  };

  const headerTitle = useMemo(() => {
    // 我的学习的路由
    let arr = [
      '/learn/mycourse',
      '/learn/mycollection',
      '/learn/mynotes',
      '/learn/certificates',
      '/learn/learningstatistics',
      '/learn/message',
      '/learn/info',
    ];
    if (customTitle) return customTitle;
    if (arr.includes(history.location.pathname)) {
      return '我的学习';
    } else {
      return '系统管理';
    }
  }, [history.location.pathname, customTitle]);
  const menuShowChange = () => {
    console.log(menuShow);
    dispatch({
      type: 'global/menuShowChange',
      payload: !menuShow,
    });
  };

  return (
    <div
      className={
        platform === 'ILA' ? 'uf-header-wrapper-ILA' : 'uf-header-wrapper'
      }
    >
      <div className="uf-header-left-part">
        <IconFont
          type="iconwenzhangpailie2-221"
          onClick={() => menuShowChange()}
        />
        <a
          href={
            (process.env.NODE_ENV === 'development'
              ? 'http://172.16.151.202'
              : '') + '/unifiedplatform/v1/app/home/<USER>'
          }
          onClick={stopJump}
          className="home-part"
        >
          {ifBack && platform !== 'ILA' && (
            <div className="go-back-btn">
              <IconFont type="iconjiantouda" />
            </div>
          )}
          <div className="icon_box">
            <img
              src={
                logoUrl
                  ? `${isDevelopment ? '/api' : ''}${logoUrl}`
                  : require('@/static/images/default_logo.png')
              }
              className="uf-header-icon"
            />
          </div>
          {platform !== 'ILA' && (
            <h2>
              {isShow
                ? location.href.includes('personal') ||
                  location.href.includes('statistics/overview')
                  ? '工作台'
                  : headerTitle
                : null}
            </h2>
          )}
        </a>
        {false && subtitle && platform !== 'ILA' && (
          <>
            {/* <Divider className="vertical-line" type="vertical" /> */}
            <h4 style={{ marginLeft: '26px' }}>|</h4>
            <h4>{subtitle}</h4>
          </>
        )}
      </div>
      <div className="uf-header-right-part">
        {/* {platform !== 'ILA' && showNav && (
          <Nav list={headerList} active={navActive} />
        )}
        {// subtitle === '系统管理' &&  //兼容统计模块
        platform !== 'ILA' && (
          <OtherNav
            list={[
              {
                key: 'joveone',
                name: '在线剪辑',
                href: '/joveone',
                target: 'joveone',
                disabled: !modules.includes(moduleCfg.jove),
                // ||!permissions.includes(perCfg.jove_use),
              },
              {
                key: 'textclip',
                name: '语音剪辑',
                href: '/textclip/#/clip/myTextClip',
                target: 'textclip',
                disabled: !modules.includes(moduleCfg.textclip),
              },
            ]}
          />
        )} */}
        {platform !== 'ILA' && (
          <Popover overlayClassName="message-popover" content={<MessageBox />}>
            <Badge count={unReadCount} offset={[5, 0]}>
              <div className="message-container">
                <IconFont type="iconxiaoxitongzhi" />
              </div>
            </Badge>
          </Popover>
        )}
        <UserAvatar
          platform={platform}
          username={userInfo?.nickName}
          avatar={userInfo?.avatar}
          workUrl={'/unifiedplatform/v1/app/workbench/direction'}
          work={modules.includes(moduleCfg.work)}
          // joveone={modules.includes(moduleCfg.jove) && permissions.includes(perCfg.jove_use)}
          // teacher={modules.includes(moduleCfg.teacher)}
          student={modules.includes(moduleCfg.student)}
          admin={modules.includes(moduleCfg.admin)}
          // rman={
          //   modules.includes(moduleCfg.rman) &&
          //   permissions.includes(perCfg.show_resource_management)
          // }
          // personal={modules.includes(moduleCfg.personal)}
          onLogout={handleLogout}
          capuman={
            size(intersection(modules, Object.values(ModuleCapumanCfg))) > 0
          }
          utilization={
            modules.includes(moduleCfg.Utilization_M_) ||
            permissions.includes(moduleCfg.Utilization_U_)
          }
        />
      </div>
    </div>
  );
};
export const CUSTOMER_NPU = 'npu';

export default (props: any) => {
  const { parameterConfig, userInfo } = useSelector<{ global: any }, any>(
    (state) => state.global,
  );
  useEffect(() => {
    if (parameterConfig?.target_customer === CUSTOMER_NPU) {
      // 添加样式
      document.querySelector('#root')?.classList.add('reset-npu');
    }
    return () => {
      document.querySelector('#root')?.classList.remove('reset-npu');
    };
  }, [parameterConfig]);

  return (
    <>
      {parameterConfig?.target_customer === CUSTOMER_NPU ? (
        <div id="other-header">
          <NPUHeader token={userInfo?.extend?.jwt} {...props} />
        </div>
      ) : (
        <Header {...props} />
      )}
    </>
  );
};
