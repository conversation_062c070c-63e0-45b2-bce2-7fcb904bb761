
/*
 * @Author: 李武林
 * @Date: 2021-08-20 13:45:16
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2023-08-23 10:28:36
 * @FilePath: \frontend\src\api\index.ts
 * @Description:
 *
 * Copyright (c) 2022 by <PERSON>武林/索贝数码, All Rights Reserved.
 */
// import area from "./area";
// import server from "./server";
// import camera from "./camera";
// import task from "./task";
import user from './user';
import setting from './setting';

let api = {
  user,
  setting,

};

export default api;

export interface IResponse<T = object | string | [] | null> {
  error_code: string;
  error_msg: string;
  extend_message: T;
}
export interface UpFormResponse<T = object | string | [] | null> {
  errorCode: string;
  errorMsg: string;
  extendMessage: T;
}
export interface UpFormListResponse<T extends any>
  extends UpFormResponse<{
    results: T;
    pageIndex: number;
    pageSize: number;
    pageTotal: number;
    recordTotal: number;
  }> {}

export interface RmanResponse<T = object | string | [] | null> {
  error: any;
  data: T;
  success: boolean;
}
export interface SupervisionResponse<T = object | string | [] | null> {
  message: string;
  stackMessage: string;
  statusCode: number;
  hostId: string;
  data: T;
}
