.main {
  display: flex;
  .content {
    width: 100%;
    box-sizing: border-box;
    // padding: 66px;
    display: flex;
    height: calc(100vh - 52px);
    > div {
      flex: 1;
      // flex默认值
    }
  }
}

.left {
  position: fixed;
  left: 0;
  top: 52px;
}
.child {
  padding-top: 52px;
  margin-left: 180px;
  @media screen and (max-width: 769px) {
    margin-left: 0;
  }
}
.header {
  position: fixed;
  top: 0;
  z-index: 10;
  width: 100%;
}
