/*
 * @Author: zhicheng ran
 * @Date: 2023-01-30 16:13:57
 * @LastEditTime: 2023-01-30 16:13:58
 * @FilePath: \frontend\src\components\UploadModal\index.tsx
 * @Description:
 */
import { CloudUploadOutlined } from '@ant-design/icons';
import { Modal, ModalProps, Spin, Upload } from 'antd';
import { DraggerProps, UploadFile } from 'antd/es/upload';
import React, { useImperativeHandle } from 'react';
import { useImmer } from 'use-immer';
import style from './index.less';

const { Dragger } = Upload;
const UploadModal: React.FC<UploadModalProps> = ({
  title,
  tip,
  errCallback,
  onOk,
  // uploadProps = {},
  okText = '确定',
  cancelText = '取消',
  accept = '.xls,.xlsx',
  maxCount = 1,
  multiple = false,
  actionRef,
}) => {
  const [open, setOpen] = useImmer(false);
  const [fileList, setFileList] = useImmer<UploadFile[]>([]);
  const [loading, setLoading] = useImmer(false);
  const draggerConfig: DraggerProps = {
    onDrop(e) {
      console.log('Dropped files', e.dataTransfer.files);
    },
    name: 'file',
    multiple,
    maxCount,
    accept,
    fileList,
    onChange(info) {
      setFileList(info.fileList);
    },
    beforeUpload: (file) => {
      return false;
    },
    // ...uploadProps,
  };
  useImperativeHandle(
    actionRef,
    () => ({
      setOpen,
    }),
    [],
  );
  const modalConfig: ModalProps = {
    open,
    onOk: async () => {
      setLoading(true);
      if (!(!onOk || !(await onOk(fileList)))) {
        setOpen(false);
      }
      setLoading(false);
    },
    onCancel: () => {
      setOpen(false);
    },
    title,
    okText,
    cancelText,
    confirmLoading: loading,
  };
  return (
    <Modal {...modalConfig}>
      <Spin spinning={loading}>
        <Dragger {...draggerConfig}>
          <p className="ant-upload-drag-icon">
            <CloudUploadOutlined rev />
          </p>
          <p className={style.uploadText}>
            将文件拖到此处，或
            <span className={style.strong}> 点击上传</span>
          </p>
        </Dragger>
        <p className={style.tip}>{tip}</p>
      </Spin>
    </Modal>
  );
};

export interface UploadModalProps {
  title: string | React.ReactNode;
  tip: string | React.ReactNode;
  errCallback: (err: any) => void;
  // uploadProps?: DraggerProps;
  onOk?: (fileList: UploadFile[]) => Promise<boolean>;
  okText?: string;
  cancelText?: string;
  accept?: string;
  maxCount?: number;
  multiple?: boolean;
  actionRef?: React.MutableRefObject<UploadRefType | null>;
}

export type UploadRefType = {
  setOpen: (open: boolean) => void;
};

export default UploadModal;
UploadModal.displayName = 'UploadModal';
