/*
 * @Author: 冉志诚
 * @Date: 2023-08-02 16:17:30
 * @LastEditTime: 2023-08-02 16:17:38
 * @FilePath: \frontend\src\pages\live\Create\components\TimeModal\index.tsx
 * @Description:
 */

import {
  ProForm,
  ProFormDateRangePicker,
  ProFormDependency,
  ProFormDigit,
  ProFormInstance,
  ProFormItem,
  ProFormList,
  ProFormProps,
  ProFormRadio,
  ProFormText,
  ProFormTreeSelect,
} from '@ant-design/pro-components';
import {
  Col,
  Form,
  FormInstance,
  Modal,
  ModalProps,
  Row,
  Tooltip,
  message,
} from 'antd';
import React, {
  useContext,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from 'react';
import { useImmer } from 'use-immer';
import { CreateService } from '@/api/CreateService';
import moment from 'moment';
import { LiveService } from '@/api/live';
import { DataNode } from 'antd/lib/tree';
import ProFormPageSelect from '@/components/ProFormPageSelect';
import { clearAryEmpty, removeEmpty } from '@/utils/utils';
import style from './index.less';
import './reset.less';
import {
  FormContext,
  LIVE_ADDRESS_TYPE,
  generateCustomAddressByLiveAddress,
  getNodeByLabel,
} from '../../common';
import { useQuery } from '@/hooks';
/** 表单布局内容wrapper  */
const FORM_COL = 14;
const TimeModal: React.FC<TimeModalProps> = ({
  actionRef: propActionRef,
  // readonly 控制页面按钮是否可用
  readonly,
  onSuccess,
}) => {
  const [open, setOpen] = useImmer(false);
  const [title, setTitle] = useImmer('直播名称');
  const [loading, setLoading] = useImmer(false);
  const [areaTree, setAreaTree] = useState<DataNode[]>([]);
  const parentForm = useContext(FormContext);
  const seriesLives = Form.useWatch('series_lives', {
    form: parentForm!,
    preserve: true,
  });
  const { query } = useQuery<{
    type: 'series' | 'rapid';
    id?: string;
  }>();
  const isSeries = query.type === 'series';
  const [form] = Form.useForm();

  const live_address_type = Form.useWatch('live_address_type', form);
  const [flowAddress, setFlowAddress] = useImmer<LiveService.FlowAddress>({});
  const area_name = Form.useWatch('area_name', form);
  const custom_live_address = Form.useWatch('custom_live_address', form);

  useImperativeHandle(
    propActionRef,
    () => {
      return {
        setOpen,
        setTitle,
      };
    },
    [],
  );

  const formRef = useRef<ProFormInstance>(null);
  const proformConfig: ProFormProps = {
    layout: 'horizontal',
    labelCol: { span: 6 },
    formRef,
    grid: true,
    // size: 'middle',
    async onFinish(formData) {
      console.log(
        '%c [ formData ]-52',
        'font-size:13px; background:pink; color:#bf2c9f;',
        formData,
      );
      setLoading(true);
      try {
        const { origin_time, no } = formData;
        const [origin_start_time, origin_end_time] = origin_time;
        const start_time = moment(origin_start_time).valueOf() / 1000;
        const end_time = moment(origin_end_time).valueOf() / 1000;

        //校验流
        // if (extend_message.length > 0) {
        //   Modal.error({
        //     title: '提示',
        //     content: '该时段已有其他安排占用',
        //     closable: true,
        //     okText: '重新选择',
        //     onOk(...args) {
        //       formRef.current?.resetFields();
        //     },
        //   });
        // }
        const { live_address_type, area_name } = formData;
        const live_address = stream_address;

        const body = removeEmpty({
          live_address_type,
          area_name: area_name?.label,
          area_id: area_name?.value,
          start_time,
          end_time,
          live_address,
          no,
        });
        console.log(
          '%c [ body ]-89',
          'font-size:13px; background:pink; color:#bf2c9f;',
          body,
        );
        message.success('直播流服务器配置成功');
        onSuccess?.(body);
        setOpen(false);
      } catch (error) {
        message.error(error as any);
      }
      setLoading(false);
    },
    form,

    submitter: false,
    async request() {
      const initial = {
        live_address_type: LIVE_ADDRESS_TYPE.CLASSROOM,
        custom_live_address: [{}],
        //FIXME 写死了
        no: 1,
      };
      try {
        return {
          ...initial,
        };
      } catch (error) {
        return initial;
      }
    },
  };
  const stream_address: CreateService.LiveAddress[] = useMemo(() => {
    if (live_address_type === LIVE_ADDRESS_TYPE.CLASSROOM) {
      if (Object.keys(flowAddress).length === 0) {
        return [];
      }
      return clearAryEmpty(
        Object.keys(LiveService.STREAM_NAME_MAP).map(key => {
          const stream_name =
            LiveService.STREAM_NAME_MAP[
              key as LiveService.SHOW_STREAM_NAME_MAP_KEY
            ];
          const url =
            flowAddress[
              key.slice(
                0,
                key.length - 4,
              ) as LiveService.SHOW_STREAM_NAME_MAP_KEY
            ];
          const stream_address =
            flowAddress[key as LiveService.SHOW_STREAM_NAME_MAP_KEY];
          const has_voice_key = `${key.slice(
            0,
            key.length - 7,
          )}has_voice` as LiveService.SHOW_STREAM_NAME_MAP_KEY;
          const is_mute = flowAddress[has_voice_key];

          if (!url) {
            return null;
          }
          return {
            stream_name,
            push_url: url,
            pull_url: url,
            //@ts-ignore labelInValue
            _id: area_name?.value,
            is_mute,
            stream_address,
          };
        }),
      );
    } else {
      if (!custom_live_address) return [];
      return clearAryEmpty(custom_live_address).map((item: any) => {
        if (Object.keys(removeEmpty(item)).length === 0) return null;
        const { name, flow } = item;
        return {
          ...flow.item,
          stream_name: name,
          flow_name: flow.item.stream_name,
        };
      });
    }
  }, [custom_live_address, live_address_type, area_name, flowAddress]);
  const classRoomSelectHelp = useMemo(() => {
    if (Object.keys(flowAddress).length === 0) return '';
    return Object.keys(LiveService.STREAM_NAME_MAP).map(key => {
      const title =
        LiveService.STREAM_NAME_MAP[
          key as LiveService.SHOW_STREAM_NAME_MAP_KEY
        ];
      const value = flowAddress[key as LiveService.SHOW_STREAM_NAME_MAP_KEY];
      return (
        <p className={style.help} key={key}>
          {title}:
          {value ? (
            <span>
              {value.length > 50 ? (
                <Tooltip title={value}>
                  <span>{value.slice(0, 50) + '...'}</span>
                </Tooltip>
              ) : (
                value
              )}
            </span>
          ) : (
            '无'
          )}{' '}
          <br />
        </p>
      );
    });
  }, [flowAddress]);
  // 确保只执行一次
  const countRef = useRef(0);

  const modalConfig: ModalProps = {
    open,
    title,
    onCancel() {
      setOpen(false);
    },
    onOk(e) {
      formRef.current?.submit();
    },
    confirmLoading: loading,
    maskClosable: false,
    style: {
      width: 800,
    },
    destroyOnClose: false,
  };
  useEffect(() => {
    initLiveAddress();
  }, [seriesLives, areaTree, isSeries, query]);
  useEffect(() => {
    areaNameOnChange(area_name);
  }, [area_name]);
  async function initLiveAddress() {
    try {
      if (isSeries && query.id && seriesLives && countRef.current === 0) {
        const {
          live_address_type,
          start_time,
          end_time,
          live_address,
          area_name,
          area_id,
        } = seriesLives?.at(-1) ?? {};
        const origin_time = [
          start_time ? moment(start_time * 1000) : undefined,
          end_time ? moment(end_time * 1000) : undefined,
        ];
        // 如果是第一种live_address_type 然后树形结构为空
        if (
          live_address_type === LIVE_ADDRESS_TYPE.CLASSROOM &&
          areaTree?.length === 0
        ) {
          // 必须提前设置时间否则areaTree不会初始化
          return form.setFieldValue('origin_time', origin_time);
        }

        const body: Record<string, any> = removeEmpty({
          live_address_type,
          origin_time,
          area_id,
        });
        if (live_address_type === LIVE_ADDRESS_TYPE.CLASSROOM) {
          if (area_name) {
            const node = getNodeByLabel(areaTree, area_name!);
            body.area_name = {
              label: area_name,
              value: node?.key,
            };
          }
        } else {
          body.custom_live_address = generateCustomAddressByLiveAddress(
            live_address ?? [],
          );
        }
        console.log(
          '%c [ body ]-295',
          'font-size:13px; background:pink; color:#bf2c9f;',
          body,
        );

        form.setFieldsValue(body);
        countRef.current++;
      }
    } catch (error) {
      console.error(error);
    }
  }
  async function areaNameOnChange(value: any) {
    if (!value) return;
    const { value: id, label } = value;
    try {
      const { extend_message } = await LiveService.fetchFlowAddessByArea(id);
      setFlowAddress({
        ...extend_message,
      });
      // LiveService.STREAM_NAME_MAP
    } catch (error) {
      message.error('获取教师相机直播流失败');
    }
  }
  return (
    // 在 Modal 打开之前，视图中不存在子元素
    <Modal forceRender {...modalConfig}>
      <ProForm {...proformConfig} id="series_time">
        <ProFormDigit name="no" hidden />
        <ProFormDateRangePicker
          name="origin_time"
          label="直播起止时间"
          placeholder={['开始时间', '结束时间']}
          fieldProps={{
            format: 'YYYY-MM-DD HH:mm',
            showTime: { format: 'HH:mm' },
            disabledDate: current => {
              // 禁止选择现在分之前的日期
              return current && current < moment().subtract(1, 'minutes');
            },
            onChange(values, formatString) {
              formRef.current?.setFieldValue('area_name', undefined);
              setFlowAddress({});
            },
          }}
          rules={[
            {
              required: true,
              async validator(_, v) {
                if (!v) {
                  return Promise.reject('不能为空');
                }
                const [origin_start_time, origin_end_time] = v;
                const formatStr = 'YYYY-MM-DD HH:mm';
                const start_time =
                  moment(origin_start_time.format(), formatStr).valueOf() /
                  1000;

                const end_time =
                  moment(origin_end_time.format(), formatStr).valueOf() / 1000;
                const { extend_message } = await CreateService.timeArrange({
                  start_time,
                  end_time,
                });
                if (extend_message?.length > 0) {
                  return Promise.reject('该时间段已被占用');
                }
                return Promise.resolve();
              },
            },
          ]}
        />
        <ProFormRadio.Group
          label="直播源"
          name="live_address_type"
          layout="vertical"
          rules={[
            {
              required: true,
              message: '这是必填项',
            },
          ]}
          fieldProps={{
            onChange() {
              formRef.current?.setFieldValue('flow', undefined);
              formRef.current?.setFieldValue('area_name', undefined);
              setFlowAddress({});
            },
          }}
          options={[
            {
              label: '教室直播流',
              value: LIVE_ADDRESS_TYPE.CLASSROOM,
            },
            {
              label: '自定义直播流',
              value: LIVE_ADDRESS_TYPE.CUSTOM,
            },
          ]}
        />
        <ProFormDependency name={['live_address_type']}>
          {({ live_address_type }) => {
            if (live_address_type === LIVE_ADDRESS_TYPE.CUSTOM) {
              return (
                <ProFormList
                  label="自定义直播流"
                  name="custom_live_address"
                  copyIconProps={false}
                  creatorButtonProps={{
                    position: 'bottom',
                    creatorButtonText: '添加直播流',
                  }}
                  max={5}
                  min={1}
                >
                  {(f, index, action) => {
                    const data = action.getCurrentRowData();
                    const {
                      push_url = '无',
                      pull_url = '无',
                      stream_address = '无',
                      is_mute,
                    } = data?.flow?.item ?? {};
                    const isMute = is_mute ? '有' : '无';

                    const help = (
                      <>
                        <p className={style.help}>
                          直播流地址({isMute}声) {stream_address}
                        </p>
                        <p className={style.help}>
                          推流地址为({isMute}声) {push_url}
                        </p>
                        <p className={style.help}>
                          拉流地址为({isMute}声) {pull_url}
                        </p>
                        {index === 0
                          ? '注：直播将默认播放“视频流-1”的声音，直播中可切换至任意视频流的声音'
                          : ''}
                      </>
                    );

                    return (
                      <ProFormItem help={help}>
                        <Row>
                          <Col
                            xs={{
                              span: 6,
                            }}
                          >
                            <ProFormDependency
                              ignoreFormListField={false}
                              name={[`flow`]}
                            >
                              {({ flow }) => {
                                return (
                                  <>
                                    <ProFormText
                                      disabled={!flow}
                                      name="name"
                                      width="md"
                                      fieldProps={{
                                        onChange() {},
                                      }}
                                      // noStyle
                                      rules={[
                                        {
                                          required: true,
                                          async validator(_, value) {
                                            if (!value) {
                                              return Promise.reject(
                                                '请输入直播流名称',
                                              );
                                            }

                                            const addr = formRef.current?.getFieldValue(
                                              'custom_live_address',
                                            );
                                            // 校验数组中的重复name
                                            const isRepeat = addr?.some(
                                              (item: any, i: number) =>
                                                i !== index &&
                                                item.name === value,
                                            );
                                            if (isRepeat) {
                                              return Promise.reject(
                                                new Error('直播流名称不能重复'),
                                              );
                                            }

                                            return Promise.resolve();
                                          },
                                        },
                                      ]}
                                    />
                                  </>
                                );
                              }}
                            </ProFormDependency>
                          </Col>
                          <Col
                            xs={{
                              push: 3,
                              span: 12,
                            }}
                          >
                            <ProFormPageSelect
                              name="flow"
                              width="md"
                              // noStyle
                              showSearch
                              request={async ({
                                keyWords,
                                current,
                                pageSize,
                              }) => {
                                try {
                                  const {
                                    extend_message: { results, total },
                                  } = await LiveService.fetchFlows(
                                    current,
                                    pageSize,
                                    keyWords!,
                                  );
                                  return {
                                    data: results.map(item => {
                                      return {
                                        label: item.stream_name,
                                        value: item._id,
                                        key: item._id,
                                        item,
                                      };
                                    }),
                                    total,
                                  };
                                } catch (error) {
                                  message.error('获取直播流失败');
                                  return {
                                    total: 0,
                                    data: [],
                                  };
                                }
                              }}
                              fieldProps={{
                                onChange(_, option: any) {
                                  const data = action.getCurrentRowData();
                                  action.setCurrentRowData({
                                    ...data,
                                    name: option?.label,
                                  });
                                },
                                labelInValue: true,
                              }}
                              placeholder="请选择直播流"
                              rules={[
                                {
                                  required: true,
                                  async validator(_, value) {
                                    if (!value) {
                                      return Promise.reject('请选择直播流');
                                    }
                                    const addr = formRef.current?.getFieldValue(
                                      'custom_live_address',
                                    );
                                    const now = addr?.[index];
                                    // 校验数组中的重复flow
                                    const isRepeatFlow = addr?.some(
                                      (item: any, i: number) => {
                                        return (
                                          i !== index &&
                                          item?.flow?.key === now?.flow.key
                                        );
                                      },
                                    );
                                    if (isRepeatFlow) {
                                      return Promise.reject(
                                        new Error('不能选择同一直播流'),
                                      );
                                    }
                                    return Promise.resolve();
                                  },
                                },
                              ]}
                            />
                          </Col>
                        </Row>
                      </ProFormItem>
                    );
                  }}
                </ProFormList>
              );
            } else {
              return (
                <ProFormDependency name={['origin_time']}>
                  {({ origin_time }) => {
                    return (
                      <ProFormTreeSelect
                        key="classroom"
                        label="选择教室"
                        name="area_name"
                        placeholder={
                          origin_time
                            ? '请选择教师相机直播流'
                            : '请先选择直播起止时间'
                        }
                        disabled={!origin_time}
                        help={classRoomSelectHelp}
                        fieldProps={{
                          // 自定义构建
                          treeNodeLabelProp: 'label',
                          labelInValue: true,

                          placement: 'topLeft',
                        }}
                        params={{ origin_time }}
                        request={async params => {
                          try {
                            if (!origin_time) return [];
                            const [
                              origin_start_time,
                              origin_end_time,
                            ] = origin_time;
                            const start_time =
                              moment(origin_start_time).valueOf() / 1000;
                            const end_time =
                              moment(origin_end_time).valueOf() / 1000;
                            const {
                              extend_message,
                            } = await CreateService.getAreaLive({
                              start_time,
                              end_time,
                            });
                            const tree = getFlowOccupyData(extend_message);
                            setAreaTree(tree);
                            return tree;
                          } catch (error) {
                            message.error('获取教师相机直播流失败');
                            return [];
                          }
                        }}
                        rules={[
                          {
                            required: true,
                            message: '请选择教师相机直播流',
                          },
                        ]}
                      />
                    );
                  }}
                </ProFormDependency>
              );
            }
          }}
        </ProFormDependency>
      </ProForm>
    </Modal>
  );
};

interface TimeModalProps {
  actionRef: React.MutableRefObject<TimeRefType | null>;
  readonly?: boolean;
  onSuccess?: (value: any) => void;
}
export default TimeModal;
TimeModal.displayName = 'TimeModal';
export type TimeRefType = {
  setOpen: (open: boolean) => void;
  setTitle: (title: string) => void;
};

// 递归处理流数据
function getFlowOccupyData(
  data: CreateService.AreaLiveTreeNode[],
  depth = 1,
): DataNode[] {
  return data?.map(item => {
    let title: React.ReactNode = item.name;
    let selectable = depth === 3;
    if (depth === 3 && item.is_occupy) {
      title = (
        <span>
          {item.name}
          <span style={{ color: 'red' }}>(已占用)</span>
        </span>
      );
      selectable = false;
    }
    return {
      key: item.id,
      value: item.id,
      title,
      label: item.area_fullname,
      selectable,
      children: getFlowOccupyData(item.children, depth + 1),
    };
  });
}
