/*
 * @Author: 冉志诚
 * @Date: 2023-08-10 11:20:09
 * @LastEditTime: 2023-08-10 13:46:01
 * @FilePath: \frontend\src\pages\live\Create\common.ts
 * @Description:
 */

import { CreateService } from '@/api/CreateService';
import { omit } from '@/utils/utils';
import { FormInstance } from 'antd';
import { DataNode } from 'antd/lib/tree';
import React from 'react';

export const FormContext = React.createContext<FormInstance<
  CreateService.ApplyDetail & Record<string, any>
> | null>(null);

export function generateCustomAddressByLiveAddress(
  live_address: CreateService.LiveAddress[],
) {
  return live_address.map(item => {
    return {
      name: item.stream_name,
      flow: {
        label: item.flow_name ?? item.stream_name,
        value: item._id,
        key: item._id,
        item: omit(item, ['voice_identify']),
      },
    };
  });
}

// 寻找节点根据label
export function getNodeByLabel(
  data: DataNode[],
  label: string,
): DataNode | undefined {
  for (const v of data) {
    //@ts-ignore
    if (v.label === label) {
      return v;
    } else if (v.children) {
      const node = getNodeByLabel(v.children, label);
      if (node) {
        return node;
      }
    }
  }
}

/**直播流类型 */
export enum LIVE_ADDRESS_TYPE {
  /** 教室直播流 */
  CLASSROOM = 1,
  /** 自定义直播流 */
  CUSTOM = 2,
}
