import './index.less';
import Module from '@/components/Module';
import {
  Form,
  Switch,
  Radio,
  Button,
  Input,
  Select,
  DatePicker,
  TimePicker,
  Space,
  Upload,
  Divider,
  PageHeader,
  message,
  TreeSelect,
  Modal,
} from 'antd';
// 解决图片剪裁组件-滑动条样式错误问题（也可在global.less中引入antd全局样式@import '~antd/dist/antd.less';
// import 'antd/lib/slider/style'
import {
  LoadingOutlined,
  InfoCircleFilled,
  LeftOutlined,
} from '@ant-design/icons';
import jQuery from 'jquery';
import _ from 'lodash';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import ActivityConstant from '@/constant/activity';
import activityManagementService from '@/service/activityManagementService';
import config from '@/utils/config';
import ImgCrop from 'antd-img-crop';
import VideoFlow from '@/components/VideoFlow';
import CamaraFlow from '@/components/CamaraFlow';
import TeacherItem from '@/components/formItemBox/teacherItem';
import moment from 'moment';
import { history, useDispatch, useSelector } from 'umi';
import FormDependency from '@/components/FormDependency';
import WebUploader from './WebUploader';
import { asyncLoadScript } from "@/utils/utils";
const { Option } = Select;
interface IFlowListPager {
  page: number;
  size: number;
  noMore: boolean;
}

const defaultImgUrl = config.defaultCover;

const CreateLive = (props: any) => {
  const id = props?.location?.query?.id;
  const [loading, setLoading] = useState(false);
  const [imageUrl, setImageUrl] = useState('');
  const [roleList, setRoleList] = useState<any[]>([]); // 是否发送弹幕
  const [flowType, setFlowType] = useState(
    ActivityConstant.flowRadioOption.classroomFlow,
    // ActivityConstant.flowRadioOption.customFlow,
  );
  const [speakerChooseType, setSpeakerChooseType] = useState(
    ActivityConstant.SpeakerOption.choose,
  );
  const [filePath, setFilePath] = useState<string>('');
  const dispatch = useDispatch();
  const [addresses, setAddresses] = useState<
    (ActivityType.IFlowOrigin | undefined)[]
  >([]);
  const [camaraAddresses, setCamaraAddresses] = useState<
    (ActivityType.IFlowOrigin | undefined)[]
  >([]);
  const [areaName, setAreaName] = useState<string>('');
  const [flows, setFlows] = useState<ActivityType.IFlow[]>([]);
  const [info, setInfo] = useState<any>({});
  // 注：若不填写结束时间，则只能手动结束直播
  const [endtimehelp, setEndtimehelp] = useState<boolean>(false);
  // const [liveInfo, setLiveInfo] = useState<ActivityType.ILive>()
  const ref = useRef<any>(null)

  const [flowListPager, setFlowListPager] = useState<IFlowListPager>({
    page: 0,
    size: 20,
    noMore: false,
  });
  const {
    taskPanls,
  } = useSelector<{ upload: any }, any>(({ upload }) => {
    return upload;
  });
  const [metadata, setMetadata] = useState<any>({})
  const typeMapping: { [propsName: string]: string } = {
    picture: 'biz_sobey_picture',
    video: 'biz_sobey_video',
    audio: 'biz_sobey_audio',
    document: 'biz_sobey_document',
    other: 'biz_sobey_other',
  };
  const [baseForm] = Form.useForm();
  const [dataTree, setDataTree] = useState<any>([]);
  const [flag, setFlag] = useState<boolean>(false);
  const [saveModalVisible, setSaveModalVisible] = useState<boolean>(false);
  const [passwordVisible, setPasswordVisible] = useState<boolean>(false);
  const [params, setParams] = useState<any>({
    look_back: false,
     is_barrage: false,
    save_resource: false,
    save_folder: '',
  });

  const stream_address = useMemo(() => {
    if (flowType === ActivityConstant.flowRadioOption.classroomFlow) {
      return camaraAddresses;
    }
    return addresses;
  }, [addresses, camaraAddresses, flowType]);
  const getAllFields = () => {
    Object.keys(typeMapping).forEach(key => {
        activityManagementService.getAllFieldsByType(typeMapping[key]).then(res => {
            if (res && res.data) {
                res.data.forEach(item => {
                    if (!item.value) {
                        item.value = item.isArray ? [] : '';
                    }
                });
                const value: any = {};
                value[typeMapping[key]] = res.data;
                setMetadata((pre: any) => Object.assign(pre, value))
            }
        });
    });
};

   // 获取角色列表
   const getRoleList = () => {
      const params = {
          count_users: false,
          includeEA: false,
          page: 1,
          size: 100,
      }
      searchRoles(params).then((data: any) => {
        setRoleList(data);
      })
  }

  useEffect(() => {
    // fetchFlowList(true);
    if (id) {
      // 修改
      fetchLiveById(id).then((info: any) => {
        setInfo(info);
        setParams({
          look_back: info.look_back,
           is_barrage: info. is_barrage,
          save_resource: info.save_resource,
          save_folder: info.save_folder,
          area_name: info.area_name,
        });
        setPasswordVisible(info.look_permission === 'password');
        const voice_identify_stream: React.Key[] = [];
        console.log(info);
        info?.live_address?.forEach((item: any) => {
          if (item.voice_identify) {
            voice_identify_stream.push(
              info?.live_address_type ===
                ActivityConstant.flowRadioOption.classroomFlow
                ? item.stream_name
                : item._id,
            );
          }
        });

        baseForm.setFieldsValue({
          live_name: info?.live_name,
          live_introduce: info?.live_introduce,
          live_speaker_id: info ? info.live_speaker : '',
          live_speaker: info?.live_speaker,
          live_speaker_desc:info?.live_speaker_desc,
          origin_start_time: info?.origin_start_time
            ? moment.unix(info?.origin_start_time)
            : undefined,
          origin_finish_time: info?.origin_finish_time
            ? moment.unix(info?.origin_finish_time)
            : undefined,
          voice_identify: info?.voice_identify,
          voice_translation: info?.voice_translation,
          is_si: info?.is_si,
          look_back_day: info.look_back_day,
          barrage_role: info.barrage_role ? info.barrage_role.split(',') : [''],
          look_permission: info.look_permission,
          look_password: info.look_password,
          save_folder: pathTransfer(info.save_folder),
          // 撒打算,
          voice_identify_stream,
        });
        if (info?.live_cover) {
          setImageUrl(info?.live_cover);
        }
        if (info?.live_address_type) {
          setFlowType(info?.live_address_type);
        }
        if (info?.live_address) {
          if (
            info?.live_address_type ===
            ActivityConstant.flowRadioOption.customFlow
          ) {
            setAddresses(info?.live_address);
          } else {
            setCamaraAddresses(info?.live_address);
          }
          setFlows(info.live_address);
        }
        if (info?.live_speaker_id) {
          setSpeakerChooseType(ActivityConstant.SpeakerOption.choose);
        } else {
          if (info.live_speaker) {
            setSpeakerChooseType(ActivityConstant.SpeakerOption.write);
          }
        }
      });
    } else {
      // 新建
      handleSetDefaultImg();
    }
    getRoleList();
    searchpublic();
  }, []);
  async function fetchLiveById(id: string) {
    const res = await activityManagementService.fetchLive(id);
    if (res.error_code === config.successCode) {
      const info = res.extend_message;
      return info;
    }
  }

  async function searchRoles(params: any) {
    const res = await activityManagementService.searchRoles(params);
    if (res.errorCode === 'success') {
      const allRoleInfo = [{
        roleName: '全部角色',
        roleCode: '',
      }]
      const info = allRoleInfo.concat(res.extendMessage?.results) || allRoleInfo;
      return info;
    }
  }

  async function addLive(live: ActivityType.ILiveOrigin) {
    const res = await activityManagementService.addLive([live]);
    if (res.error_code === config.successCode) {
      return true;
    } else {
      message.error(res.extend_message);
      return false;
    }
  }

  async function updateLive(live: ActivityType.ILiveOrigin) {
    const res = await activityManagementService.updateLive(live);
    if (res.error_code === config.successCode) {
      return true;
    } else {
      message.error(res.extend_message);
      return false;
    }
  }

  async function uploadImg(img: string) {
    const res = await activityManagementService.uploadCover(img);
    setLoading(true);
    if (res.error_code === config.successCode) {
      return Promise.resolve(res.extend_message.url);
    }
  }

  async function fetchFlowList(paging?: boolean, keyword?: any) {
    //paging - 是否翻页
    let { page, size, noMore } = flowListPager;
    if (noMore && paging) return;
    page = paging ? page + 1 : 1;
    const res = await activityManagementService.fetchFlows(page, size, keyword);
    if (res.error_code === config.successCode) {
      setFlowListPager({
        ...flowListPager,
        page,
        noMore: res.extend_message.results?.length < flowListPager.size,
      });
      let shiftFlows = paging ? flows : [];
      setFlows([
        ...shiftFlows,
        ...res.extend_message?.results.filter((item) => {
          return item.stream_status === true;
        }),
      ]);
    }
  }

  function getBase64(img: any, callback: any) {
    const reader = new FileReader();
    reader.addEventListener('load', () => callback(reader.result));
    reader.readAsDataURL(img);
  }

  function beforeUpload(file: any) {
    const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png';
    if (!isJpgOrPng) {
      message.error('只能上传JPG/PNG文件!');
    }
    const isLt2M = file.size / 1024 / 1024 < 2;
    if (!isLt2M) {
      message.error('图片大小必须小于2MB!');
    }
    return isJpgOrPng && isLt2M;
  }

  const handleImgRequest = (e: any) => {
    getBase64(e.file, (imageUrl: any) => {
      uploadImg(imageUrl).then((urlRes) => {
        setLoading(false);
        setImageUrl(urlRes ? urlRes : defaultImgUrl);
      });
    });
  };

  const handleSave = () => {
    baseForm.validateFields().then((baseInfo: any) => {
      console.log('baseInfo', baseInfo);
      // const baseInfo = baseForm.getFieldsValue()

      let areaname = '';
      // 直播流地址
      let myAddresses: any[] = [];
      if (flowType === ActivityConstant.flowRadioOption.classroomFlow) {
        myAddresses = camaraAddresses.filter((item) => item != undefined);
        areaname = areaName;
      } else {
        myAddresses = addresses.filter((item) => item != undefined);
      }
      if (myAddresses.length <= 0) {
        message.error('至少选择一个直播流');
        return;
      }
      // 全部关闭语音
      myAddresses.forEach((item) => {
        item.voice_identify = false;
      });
      // 注入语音分析
      if (baseInfo.voice_identify) {
        const addr = baseInfo.voice_identify_stream;

        addr?.forEach((item: string) => {
          const result = myAddresses.find(({ stream_address: address }) => {
            return (
              stream_address.find((v) => {
                if (
                  flowType === ActivityConstant.flowRadioOption.classroomFlow
                ) {
                  return v?.stream_name === item;
                }
                return v?._id === item;
              })?.stream_address === address
            );
          });
          if (result) {
            result.voice_identify = true;
          }
        });

        if(myAddresses.length){
          myAddresses = myAddresses.map((item:any)=>{
            if(addr.includes(item.stream_name) ){
              return {
                ...item,
                is_mute:true
              }
            }else{
              return {
                ...item,
                is_mute:false
              }
            }
          })
        }
      }


      // 主讲人id
      let speakerId: string = '';
      switch (speakerChooseType) {
        case ActivityConstant.SpeakerOption.choose:
          speakerId = baseInfo.live_speaker_id?.split(',')[1]
            ? baseInfo.live_speaker_id.split(',')[1]
            : info.live_speaker_id;
          break;
        case ActivityConstant.SpeakerOption.write:
          speakerId = '';
          break;
      }
      // if (!speakerId) {
      //   message.error('请填写主讲人信息');
      //   return;
      // }
      const liveInfo: ActivityType.ILiveOrigin = {
        live_name: baseInfo.live_name,
        live_introduce: baseInfo.live_introduce
          ? baseInfo.live_introduce
          : undefined,
        live_speaker_id: speakerId,
        live_speaker: speakerId
          ? baseInfo.live_speaker_id.split(',')[0]
          : baseInfo.live_speaker,
        live_cover: imageUrl ? imageUrl : undefined,
        voice_identify: baseInfo.voice_identify,
        voice_translation: baseInfo.voice_translation,
        is_si: baseInfo.is_si,
        live_address: myAddresses,
        live_speaker_desc:baseInfo.live_speaker_desc,
        origin_start_time: Number(baseInfo.origin_start_time?.format('X')),
        origin_finish_time:
          Number(baseInfo.origin_finish_time?.format('X')) || '',
        live_address_type: flowType,
        ...params,
        look_back_day: baseInfo.look_back_day,
        barrage_role: baseInfo.barrage_role ? baseInfo.barrage_role.join(',') : undefined,
        look_permission: baseInfo.look_permission,
        look_password: baseInfo.look_password,
      };
      console.log(liveInfo);
      if (areaname) {
        liveInfo.area_name = areaname;
      }
      if (id) {
        // 修改
        if (!liveInfo.live_speaker && speakerId === '') {
          //处理编辑 删除主讲人 给后台的标识
          liveInfo.live_speaker = '';
        }
        if (info.schedule_status === ActivityConstant.liveStatus.on) {
          //直播中禁止修改时间并且 也禁止传字段
          delete liveInfo.origin_start_time;
          delete liveInfo.origin_finish_time;
        }
        updateLive({
          ...liveInfo,
          _id: id,
        }).then((ifSucess) => {
          if (ifSucess) {
            message.success('修改成功');
            history.push('/mg/activity');
          }
        });
      } else {
        // 创建
        addLive(liveInfo).then((ifSucess) => {
          if (ifSucess) {
            message.success('创建成功');
            history.push('/mg/activity');
          }
        });
      }
    });
  };

  const handleSetDefaultImg = () => {
    // activityManagementService.fetchDefaultCover().then((res) => {
    //   if (res.error_code === config.successCode) {
    //     setImageUrl(res?.extend_message.url)
    //     baseForm.setFieldsValue({live_cover: res.extend_message?.url})
    //   }
    // })
    setImageUrl(config.defaultCover);
    baseForm.setFieldsValue({
      live_cover: config.defaultCover,
      origin_start_time: moment(),
    });
  };

  const range = (start: number, end: number) => {
    const result = [];
    for (let i = start; i < end; i++) {
      result.push(i);
    }
    return result;
  };
  const removeTreeListItem = (treeList: any, name: string) => {
    // 根据id属性从数组（树结构）中移除元素
    if (!treeList || !treeList.length) {
      return;
    }
    for (let i = 0; i < treeList.length; i++) {
      if (treeList[i].name === name) {
        treeList.splice(i, 1);
        break;
      }
      removeTreeListItem(treeList[i].children, name);
    }
  };
  const searchpublic = async () => {
    activityManagementService.gettreebylevel(2).then((res: any) => {
      // setLoading(false);
      // upLoadClicktag = true;
      if (res && res.data && res.success) {
        let data = res.data;
        const newData: any = [];
        removeTreeListItem(data, '录播资源');
        removeTreeListItem(data, '共享资源');
        removeTreeListItem(data, '群组资源');

        res.data[0].children?.forEach((item: any) => {
          newData.push({ ...item });
        });
        newData.push(res.data[1]);
        // console.log('newData', newData);
        const rootData = newData.map((item: any) => {
          return {
            key: item.path,
            value: item.path,
            title: item.name,
            id: item.id,
            disabled: item.name === '群组资源' ? true : false,
          };
        });
        setDataTree(rootData);
        setFlag(true);
      }
    });
  };
  const onLoadChild = (node: any, isRoot: boolean = false) => {
    const { key, children, code, title } = node;
    return new Promise(async (resolve) => {
      if (key === 'add') {
        resolve(null);
        return;
      }
      if (children) {
        resolve(null);
        return;
      }
      function updateTreeData(list: any, key: React.Key, children: any): any {
        return list.map((node: any) => {
          if (node.key === key) {
            return {
              ...node,
              children,
            };
          } else if (node.children) {
            return {
              ...node,
              children: updateTreeData(node.children, key, children),
            };
          }
          return node;
        });
      }
      const res: any = await activityManagementService.onloadTreeLeaf(key);
      if (res && res.data && res.success) {
        let treeList = res.data;
        treeList = treeList.filter((item: any) => {
          return item.name !== '录播资源';
        });
        setDataTree((origin: any) =>
          updateTreeData(
            origin,
            key,
            treeList.map((item: any) => {
              return {
                key: item.path,
                value: item.path,
                title: item.name,
                id: item.contentId,
              };
            }),
          ),
        );
        resolve(null);
      }
    });
  };
  const copyObject: <T>(obj: T) => T = oldObj => {
    if (oldObj instanceof Object) {
      // return JSON.parse(JSON.stringify(oldObj)); //这种强转会使文件丢失orignalFile属性
      return _.cloneDeep(oldObj);
    } else {
      return oldObj;
    }
  };
  //保存目录确认
  const selectChange = (value: any, label: any, extra: any) => {
    console.log(value, label, extra);
    setParams((pre: any) => ({
      ...pre,
      save_folder: value,
    }));
  };
  //路径转义
  const pathTransfer = (path: string) => {
    let name = '个人资源';
    if (!path) return;
    const array: any = path.split('/');
    for (let i = 3; i < array.length; i++) {
      name = name + '/' + array[i];
    }
    return name;
  };
  //保存目录确认
  const saveConfirm = () => {
    console.log('params', params);
    if (!params.save_folder) {
      message.info('请选择目录');
      return;
    }
    baseForm.setFieldsValue({
      ...baseForm.getFieldsValue(),
      save_folder: pathTransfer(params.save_folder),
    });
    setSaveModalVisible(false);
  };
  const stateRef = useRef<any>()
  useEffect(() => {
    stateRef.current = taskPanls
  }, [taskPanls])
  const chooseFile = () => {
    // setCheckedList([])
    ref.current.click()
    ref.current.onchange = (event: { target: { files: any[]; }; }) => {
      let files = event.target.files;
      // setCheckedList((pre) => _.concat(pre, fileMap));
      // setUploadModal(true)
      files.length> 0 && uploadFile(files)
    }
  }
  const uploadFile = async (files: any) => {
    setLoading(true)
    // if (!webUploader) {
    //   return;
    // }
    const newTaskPanls: any = copyObject(taskPanls);
    let showFlag = false;
    //获取每一个上传对象的存储方式 start
    const param = _.map(files, (item) => ({
      fileLength: item.size,
      fileName: item.name,
      fileType: item.type.includes("image")
        ? "picture"
        : item.type === "video"
          ? "video"
          : "other",
      poolType: window.localStorage.getItem('upform_platform') === 'Lark' ? 'ROLE' : '',
      // isPrivate:props.targetFolder.includes('global_sobey_defaultclass/private') //v3 暂时弃用
      pathType: 1
    }));
    const storageConfigRes: any = await activityManagementService.storageConfig(param);
    debugger
    if (!storageConfigRes?.success) {
      setLoading(false)
      return
    };
    //获取每一个上传对象的存储方式 end
    for (let index = 0; index < files.length; index++) {
      const item: any = files[index];
      item.uid = guid()
      const storage_: any = storageConfigRes.data[index];
      const metadataTypes = metadata[typeMapping[param[index].fileType]];
      const uploadMetas = _.map(metadataTypes, (item) => ({
        ...item,
        value:
          item.fieldName === "name" || item.fieldName === "name_"
            ? param[index].fileName.substring(0, param[index].fileName.lastIndexOf("."))
            : item.value,
      }));
      //判断存储方式
      if (!storage_.access_type) continue;
      if (storage_.access_type === 'NAS') {
        showFlag = true;
        const file = new (window as any).WebUploader.Lib.File(
          (window as any).WebUploader.Base.guid(),
          // orginTasks[index],
          item //兼容拖拽有originFileObj的bug
          // item.file.originFileObj?.size || item.file //兼容拖拽有originFileObj的bug
        );
        file.guid = (window as any).WebUploader.Base.guid();
        file.metadata = uploadMetas;
        file.folderPath = '';
        file.fileGuid = storage_.path;//针对同名文件增加不同标识
        // @ts-ignore
        const webUploader = new WebUploader({
          uploadError,
          uploadComplete,
        });
        webUploader.addFiles(file);
        const getFiles = webUploader.getFiles();
        getFiles.forEach((item: any) => {
          if (
            newTaskPanls.filter(i => i.guid === item.source.guid).length === 0
          ) {
            newTaskPanls.push({
              uploader: webUploader,
              name: item.name,
              size: item.size,
              status: 0,
              progress: 0,
              index,
              guid: item.source.guid,
              uploading: false,
              pause: false,
            });
          }
        });
        dispatch({
          type: 'upload/setTaskPanls',
          payload: {
            value: newTaskPanls,
          },
        });
      }
    }
  };
  const uploadError = (file: any) => {
    // message.error(
    //   file.name +
    //   intl.formatMessage({
    //     id: 'upload-error',
    //     defaultMessage: '上传失败',
    //   }),
    // );
    setLoading(false)
  };
  const uploadComplete = (file: any, newWebUploader: any) => {
    activityManagementService.filemerge({ fileGuid: file.source.fileGuid, fileName: file.name, guid: file.source.guid }).then(res => {
      if (res?.success) {
        pollMergeStatus(file, newWebUploader);
      } else {
        uploadError(file);
      }
    });
  };
  /**
  * 轮询合并状态
  * @param file
  * @param newWebUploader
  */
  const pollMergeStatus = async (file: any, newWebUploader: any) => {
    const res = await activityManagementService.fetchMergeStatus(file.source.guid);
    if (res?.data?.state === 1 && res.data.finalFilePath) {
      setFilePath(res.data.finalFilePath)
      activityManagementService.unpublished({
        file_path: res.data.finalFilePath,
        live_id: info?._id,
      })
        .then(dd => {
          if (dd && dd.data && dd.success) {
            message.success('上传成功')
            dispatch({
              type: 'upload/updateTaskPanls',
              payload: {
                value: { guid: file.source.guid, progress: 1 },
              },
            });
            // 从队列中删除
            newWebUploader.removeFile(file, true);

            // 删除task
            // 弹窗提示
            setLoading(false)
            // setUploadPath([])
            // setCheckedList([])
            // message.success(
            //   file.name +
            //   intl.formatMessage({
            //     id: 'upload-success',
            //     defaultMessage: '上传成功',
            //   }),
            // );
            // setUploadModal(false)
          } else {
            uploadError(file);
          }
        });
    } else if (res?.data?.state === 0) {
      // 手动移除掉的任务 停止轮询
      const realTaskPanls = stateRef.current;
      if (realTaskPanls.some((item: any) => item.guid === file.source.guid)) {
        setTimeout(() => {
          pollMergeStatus(file, newWebUploader);
        }, 500);
      }
    } else if (res?.data?.state === -1 && res?.data.errorMsg) {
      // message.error(res?.data.errorMsg);
      uploadError(file);
    } else {
      uploadError(file);
    }
  };

  useEffect(() => {
    (window as any).$ = (window as any).jQuery = jQuery;
    asyncLoadScript('/rman/libs/webuploader/webuploader.js');
    getAllFields();
  }, []);
  return (
    <div className="activity_create_container">
      <PageHeader
        className="activity_header"
        onBack={() => history.goBack()}
        title={<span className="header_title">创建活动直播</span>}
        backIcon={<LeftOutlined />}
      />
      <div className="activity_main_container">
        <Module width="54%" horizonAlign="left" style={{ margin: '0 80px' }}>
          <Form
            form={baseForm}
            labelCol={{ span: 4 }}
            wrapperCol={{ span: 20 }}
          >
            <Form.Item label="直播封面">
              <div className="live_cover_container">
                {/* {imageUrl ? <img src={imageUrl} className="avatar_img" /> : null} */}
                <Form.Item name="live_cover" valuePropName="file" noStyle>
                  <ImgCrop rotate aspect={16 / 9}>
                    <Upload
                      customRequest={handleImgRequest}
                      name="avatar"
                      showUploadList={false}
                      beforeUpload={beforeUpload}
                    >
                      <div className="live_upload">
                        <img src={imageUrl} className="avatar_img" />
                        <div className="upload_tip hover_show">重新上传</div>
                        {loading && (
                          <div className="upload_tip">
                            <LoadingOutlined />
                          </div>
                        )}
                      </div>
                    </Upload>
                  </ImgCrop>
                </Form.Item>
                <Button type="primary" onClick={handleSetDefaultImg}>
                  恢复默认
                </Button>
              </div>
            </Form.Item>
            <Form.Item
              label="直播名称"
              name="live_name"
              rules={[
                {
                  required: true,
                  message: '请输入直播名称',
                },
              ]}
            >
              <Input />
            </Form.Item>
            <Form.Item label="主讲人">
              <Input.Group compact>
                <Select
                  value={speakerChooseType}
                  style={{ width: '30%' }}
                  onChange={(value) => {
                    let choose = '',
                      write = '';
                    // if(id){
                    //   if(info.live_speaker_id && info.live_speaker  && value === ActivityConstant.SpeakerOption.choose){
                    //     choose=info.live_speaker_id
                    //   }
                    //   else{
                    //     write=info.live_speaker
                    //   }
                    // }
                    baseForm.setFieldsValue({
                      ...baseForm.getFieldsValue(),
                      live_speaker: write,
                      live_speaker_id: choose,
                    });
                    setSpeakerChooseType(value);
                  }}
                >
                  <Option value={ActivityConstant.SpeakerOption.choose}>
                    选择用户
                  </Option>
                  <Option value={ActivityConstant.SpeakerOption.write}>
                    自定义人员
                  </Option>
                </Select>
                {speakerChooseType === ActivityConstant.SpeakerOption.write ? (
                  <Form.Item
                    name="live_speaker"
                    noStyle
                    rules={[{ required: true, message: '请选择主讲人' }]}
                  >
                    <Input
                      style={{ width: '70%' }}
                      placeholder="请输入主讲人姓名"
                    />
                  </Form.Item>
                ) : (
                  <TeacherItem
                    style={{ width: '70%' }}
                    multiple={false}
                    required={false}
                    message={'请选择主讲人'}
                    label={''}
                    name={'live_speaker_id'}
                    key="teacher1"
                  />
                )}
              </Input.Group>
            </Form.Item>
            <Form.Item label="主讲人介绍" name="live_speaker_desc">              
              <Input.TextArea rows={4} />
            </Form.Item>
            <Form.Item
              label="开始时间"
              name={'origin_start_time'}
              rules={[
                { required: true, message: '请选择直播开始日期' },
                // ({ getFieldValue }) => ({
                //   validator(_, value) {
                //     if (getFieldValue('origin_finish_time') && getFieldValue('origin_finish_time') < value) {
                //       return Promise.reject(new Error('开始时间需要小于结束时间'));
                //     }
                //     return Promise.resolve();
                //   },
                // })
              ]}
            >
              <DatePicker
                style={{ width: '100%' }}
                showTime={{
                  defaultValue: moment(moment(new Date()), 'HH:mm:ss'),
                }}
                dropdownClassName="display-now"
                format="YYYY-MM-DD HH:mm"
                disabled={
                  info.schedule_status === ActivityConstant.liveStatus.on
                }
                allowClear={false}
                // 时分秒的静止选择
                disabledTime={(currentDate: any) => {
                  if (currentDate == null) {
                    return {
                      disabledHours: () => range(0, new Date().getHours()),
                      disabledMinutes: () => range(0, new Date().getMinutes()),
                    };
                  } else {
                    // 对比时间大小
                    if (
                      moment(currentDate.format('YYYY-MM-DD')).valueOf() ==
                      moment(moment().format('YYYY-MM-DD')).valueOf()
                    ) {
                      // 当前的情况 判断小时切换
                      if (currentDate.hour() <= moment().hour()) {
                        return {
                          disabledHours: () => range(0, new Date().getHours()),
                          disabledMinutes: () =>
                            range(0, new Date().getMinutes()),
                        };
                      } else {
                        return {
                          disabledHours: () => range(0, new Date().getHours()),
                          disabledMinutes: () => [],
                        };
                      }
                    } else {
                      return {
                        disabledHours: () => [],
                        disabledMinutes: () => [],
                      };
                    }
                  }
                }}
                // 小于当前日期的都禁用
                disabledDate={(currentDate: any) => {
                  let newtime = moment(currentDate).format('YYYY-MM-DD');
                  let time2 = moment().format('YYYY-MM-DD');
                  return (
                    new Date(newtime).valueOf() < new Date(time2).valueOf()
                  );
                }}
                onChange={(date: any) => {
                  let selecttime = moment(
                    date.format('YYYY-MM-DD HH:mm'),
                  ).valueOf();
                  // 直播结束时间
                  let origin_finish_time =
                    baseForm.getFieldValue('origin_finish_time');
                  if (
                    origin_finish_time &&
                    selecttime >
                      moment(
                        origin_finish_time.format('YYYY-MM-DD HH:mm'),
                      ).valueOf()
                  ) {
                    message.error('开始时间不能大于结束时间');
                    baseForm.setFieldsValue({
                      ...baseForm.getFieldsValue(),
                      origin_finish_time: null,
                    });
                  } else {
                    setEndtimehelp(true);
                  }
                }}
              />
            </Form.Item>
            <Form.Item
              label="结束时间"
              name={'origin_finish_time'}
              help={
                endtimehelp
                  ? (!params.look_back ? '注：若不填写结束时间，则只能手动结束直播' : '请填写结束时间')
                  : '请先选择开始时间'
              }
              rules={[
                {required: params.look_back, message: ' 请填写结束时间'}
              ]}
            >
              <DatePicker
                style={{ width: '100%' }}
                showTime={{
                  defaultValue: (() => {
                    let origin_start_time =
                      baseForm.getFieldValue('origin_start_time');
                    if (origin_start_time) {
                      return moment(
                        moment(origin_start_time).add(3, 'minute'),
                        'HH:mm:ss',
                      );
                    } else {
                      return moment(
                        moment(new Date()).add(3, 'minute'),
                        'HH:mm:ss',
                      );
                    }
                  })(),
                }}
                // disabled={(() => {
                //   return (
                //     info.schedule_status === ActivityConstant.liveStatus.on ||
                //     !baseForm.getFieldValue('origin_start_time')
                //   );
                // })()}
                format="YYYY-MM-DD HH:mm"
                dropdownClassName="display-now"
                // // 时分秒的静止选择
                // disabledTime={(currentDate: any): any => {
                //   const afterTag = baseForm.getFieldValue('origin_start_time');
                //   if (currentDate == null) {
                //     if (afterTag) {
                //       return {
                //         disabledHours: () => range(0, afterTag.hours()),
                //         disabledMinutes: () => range(0, afterTag.minutes() + 3),
                //       };
                //     } else {
                //       return {
                //         disabledHours: () => range(0, new Date().getHours()),
                //         disabledMinutes: () =>
                //           range(0, new Date().getMinutes() + 3),
                //       };
                //     }
                //   } else {
                //     if (
                //       moment(currentDate.format('YYYY-MM-DD')).valueOf() ==
                //       moment(afterTag.format('YYYY-MM-DD')).valueOf()
                //     ) {
                //       debugger
                //       // 当前的情况 判断小时切换
                //       if (currentDate.hour() <= afterTag.hour()) {
                //         if(afterTag){
                //           return {
                //             disabledHours: () => range(0, afterTag.hours()),
                //             disabledMinutes: () =>range(0, afterTag.minutes() + 3),
                //           };
                //         }else{
                //           return {
                //             disabledHours: () => range(0, new Date().getHours()),
                //             disabledMinutes: () =>
                //               range(0, new Date().getMinutes() + 3),
                //           };
                //         }
                //       } else {
                //         if(afterTag){
                //           return {
                //             disabledHours: () => range(0, afterTag.hours()),
                //             disabledMinutes: () => range(0, afterTag.minutes() + 3),
                //           };
                //         }else{
                //           return {
                //             disabledHours: () => range(0, new Date().getHours()),
                //             disabledMinutes: () => [],
                //           };
                //         }
                //       }
                //     } else {
                //       return {
                //         disabledHours: () => [],
                //         disabledMinutes: () => [],
                //       };
                //     }
                //   }
                // }}
                // // 小于当前日期的都禁用
                // disabledDate={(currentDate: any) => {
                //   let newtime = moment(currentDate).format('YYYY-MM-DD');
                //   let time2 = moment().format('YYYY-MM-DD');
                //   return (
                //     new Date(newtime).valueOf() < new Date(time2).valueOf()
                //   );
                // }}
                onChange={(date: any) => {
                  if (date) {
                    let newtime = date.format('YYYY-MM-DD HH:mm');
                    const afterTag =
                      baseForm.getFieldValue('origin_start_time');
                    let time2 = afterTag.format('YYYY-MM-DD HH:mm');
                    if (newtime < time2) {
                      message.error('结束时间不能小于开始时间');
                      baseForm.setFieldsValue({
                        ...baseForm.getFieldsValue(),
                        origin_finish_time: '',
                      });
                    }
                  } else {
                    baseForm.setFieldsValue({
                      ...baseForm.getFieldsValue(),
                      origin_finish_time: '',
                    });
                  }
                }}
              />
            </Form.Item>
            <Form.Item
              label="直播简介"
              name="live_introduce"
              style={{ marginBottom: 0 }}
            >
              <Input.TextArea rows={6} />
            </Form.Item>
          </Form>
        </Module>
        <Divider />
        <Module width="54%" horizonAlign="left" style={{ margin: '0 80px' }}>
          <Form.Item
            label="直播源"
            style={{ margin: 0 }}
            labelCol={{ span: 4 }}
            wrapperCol={{ span: 20 }}
          >
            <Radio.Group
              value={flowType}
              onChange={(e) => {
                setFlowType(e.target.value);
                setParams({
                  ...params,
                  area_name: '',
                });
              }}
              style={{ width: '100%' }}
            >
              <Radio
                value={ActivityConstant.flowRadioOption.classroomFlow}
                style={{ width: '50%', textAlign: 'center' }}
              >
                教师相机直播流
              </Radio>
              {flowType === ActivityConstant.flowRadioOption.classroomFlow && (
                <CamaraFlow
                  defaultLength={1}
                  maxLength={2}
                  address={camaraAddresses}
                  areaName={areaName}
                  onAddressesChange={(name: any, addresses: any) => {
                    setAreaName(name);
                    setCamaraAddresses(addresses);
                  }}
                />
              )}
              <Radio
                value={ActivityConstant.flowRadioOption.customFlow}
                style={{ width: '50%', textAlign: 'center' }}
              >
                自定义直播流
              </Radio>
              {flowType === ActivityConstant.flowRadioOption.customFlow && (
                <VideoFlow
                  customFlows={flows}
                  defaultLength={1}
                  maxLength={5}
                  customList={addresses}
                  onAddressesChange={(addresses) => {
                    setAddresses(addresses);
                  }}
                  onUpdateCustomFlows={(keyword, paging) => {
                    fetchFlowList(paging, keyword);
                  }}
                />
              )}
              {flowType == ActivityConstant.flowRadioOption.customFlow && <Form.Item style={{ margin: '12px 0 0 12px' }} labelCol={{ span: 0 }} wrapperCol={{ span: 24 }}>
                  <Input
                    placeholder="请输入直播地点"
                    value={params.area_name}
                    onChange={(e) => {
                      setParams((pre: any) => ({
                        ...pre,
                        area_name: e.target.value,
                      }));
                    }}
                  />
              </Form.Item>}
            </Radio.Group>
          </Form.Item>
        </Module>
        <Divider />
        <Module width="54%" horizonAlign="left" style={{ margin: '0 80px' }}>
          <Form
            form={baseForm}
            labelCol={{ span: 6 }}
            onValuesChange={(value: any, all: any) => {
              setPasswordVisible(all.look_permission === 'password');
            }}
            wrapperCol={{ span: 18 }}
          >
            <Form.Item
              label="是否允许回看"
              // name="look_back"
              className="flexItem"
            >
              <Switch
                checked={params.look_back}
                onChange={(value) => {
                  setParams((pre: any) => ({
                    ...pre,
                    look_back: value,
                  }));
                  setEndtimehelp(value)
                }}
              />
              {/* <Form.Item
                name="look_back_day"
                noStyle
                rules={[
                  { required: params.look_back, message: '请选择观看有效期' },
                ]}
              >
                <Select disabled={!params.look_back} placeholder="观看有效期">
                  {[1, 2, 3, 4, 5, 6, 7].map((item: any, index: number) => {
                    return (
                      <Option value={item} key={index}>
                        {item + '天'}
                      </Option>
                    );
                  })}
                </Select>
              </Form.Item> */}
            </Form.Item>
            <Form.Item
              label="是否允许发送弹幕"
              className="flexItem"
            >
              <Switch
                checked={params. is_barrage}
                onChange={(value) => {
                  setParams((pre: any) => ({
                    ...pre,
                    is_barrage: value,
                  }));
                }}
              />
              <Form.Item
                name="barrage_role"
                noStyle
                rules={[
                  { required: params. is_barrage, message: '请选择角色' },
                ]}
              >
                <Select
                  showSearch
                  optionFilterProp="children"
                  disabled={!params. is_barrage}
                  placeholder='请选择角色（可多选）'
                  mode="multiple"
                  onChange={(checked: any) => {
                    if (checked[checked.length - 1] === '') {
                      baseForm.setFieldsValue({ barrage_role: [''] })
                    } else {
                      baseForm.setFieldsValue({ barrage_role: checked.filter((item: any) => item) })
                    }
                  }}
                >
                  {roleList.map((item: any) => (
                      <Option key={item.roleCode} value={item.roleCode}>
                          {item.roleName}
                      </Option>
                  ))}
                </Select>
              </Form.Item>
            </Form.Item>

            {/* <Form.Item label="是否需要保存" className="flexItem">
              <Switch
                checked={params.save_resource}
                onChange={(value) => {
                  setParams((pre: any) => ({
                    ...pre,
                    save_resource: value,
                  }));
                }}
              />
              <Form.Item
                name="save_folder"
                noStyle
                rules={[
                  { required: params.save_resource, message: '请选择保存目录' },
                ]}
              >
                <Input
                  placeholder="保存至"
                  disabled={!params.save_resource}
                  readOnly
                  onClick={() => setSaveModalVisible(true)}
                />
              </Form.Item>
            </Form.Item> */}
            <Form.Item
              label="观看权限"
              name="look_permission"
              rules={[{ required: true, message: '请选择观看权限' }]}
            >
              <Radio.Group>
                <Radio value={'public'}>社会公开</Radio>
                <Radio value={'school'}>校内公开</Radio>
                <Radio value={'password'}>需要密码加入</Radio>
              </Radio.Group>
            </Form.Item>
            {passwordVisible && (
              <Form.Item
                label="密码"
                name="look_password"
                rules={[{ required: true, message: '请输入密码' }]}
              >
                <Input placeholder="请输入直播观看密码" />
              </Form.Item>
            )}
            <Form.Item
              valuePropName="checked"
              label="语音识别"
              name="voice_identify"
              initialValue={false}
              rules={[
                ({ getFieldValue }) => ({
                  validator(_, value) {
                    if (
                      (getFieldValue('voice_translation') ||
                        getFieldValue('is_si')) &&
                      !value
                    ) {
                      baseForm.setFieldsValue({
                        voice_translation: false,
                        is_si: false,
                      });
                      return Promise.resolve();
                    }
                    return Promise.resolve();
                  },
                }),
              ]}
            >
              <Switch />
            </Form.Item>
            <FormDependency name={['voice_identify']}>
              {({ voice_identify }) => {
                if (voice_identify) {
                  return (
                    <Form.Item
                      name="voice_identify_stream"
                      label="需语音识别的流"
                      help="请先选择直播源"
                    >
                      <Select
                        placeholder="请选择发起语音识别的视频流"
                        // 多选
                        mode="multiple"
                        options={stream_address?.map((item) => {
                          // 判断直播源是教室相机
                          if (
                            flowType ===
                            ActivityConstant.flowRadioOption.classroomFlow
                          ) {
                            return {
                              label: item?.stream_name,
                              value: item?.stream_name,
                            };
                          }
                          return {
                            label: item?.stream_name,
                            value: item?._id,
                          };
                        })}
                      ></Select>
                    </Form.Item>
                  );
                }
                return null;
              }}
            </FormDependency>
            <Form.Item
              valuePropName="checked"
              label="实时翻译"
              name="voice_translation"
              initialValue={false}
              help="需开启语音识别"
            >
              <Switch
                onChange={(value) => {
                  if (value) {
                    baseForm.setFieldsValue({
                      voice_identify: true,
                    });
                  } else {
                    baseForm.setFieldsValue({
                      is_si: false,
                    });
                  }
                }}
              />
            </Form.Item>
            <FormDependency name={['voice_identify']}>
              {({ voice_identify }) => {
                if (voice_identify) {
                  return(
                    <>
                      <Form.Item
                        label="源语言"
                        tooltip="直播流里面的语言"
                        name="source_language"
                        initialValue={''}
                      >
                        <Select
                          placeholder="请选择直播流里面的语言"
                          options={[{ label: '中文', value: 'zh' }, { label: '英文', value: 'en'}]}
                        ></Select>
                      </Form.Item>
                      <Form.Item
                        label="目标语言"
                        tooltip="需要翻译的目标语言"
                        name="target_language "
                        initialValue={[]}
                      >
                        <Select
                          placeholder="请选择直播流里面的语言"
                          // 多选
                          mode="multiple"
                          options={[{ label: '中文', value: 'zh' }, { label: '英文', value: 'en'}]}
                        ></Select>
                      </Form.Item>
                    </>
                  )
                }
                return null;
              }}
            </FormDependency>

            <Form.Item
              valuePropName="checked"
              label="同声传译"
              name="is_si"
              initialValue={false}
              help="需开启实时翻译"
            >
              <Switch
                onChange={(value) => {
                  if (value) {
                    baseForm.setFieldsValue({
                      voice_identify: true,
                      voice_translation: true,
                    });
                  }
                }}
              />
            </Form.Item>
            {id && <Form.Item
              label="录制视频上传"
              name="video_upload"
              initialValue={false}
            >
                <Button className="btn" loading={loading} onClick={chooseFile} >上传</Button>
                <input accept="video/*" ref={ref} type="file" style={{ display: 'none' }} />
            </Form.Item>}
          </Form>
        </Module>
        <div className="button_wrapper">
          <Space>
            <Button type="primary" onClick={handleSave}>
              保存
            </Button>
          </Space>
        </div>
      </div>
      <Modal
        title="保存至"
        className="saveModal"
        visible={saveModalVisible}
        onCancel={() => setSaveModalVisible(false)}
        onOk={saveConfirm}
      >
        <div>请选择目录：</div>
        <TreeSelect
          onChange={selectChange}
          placeholder="保存至"
          // value={params.save_folder}
          getPopupContainer={(e: any) => e.parentElement.parentElement}
          treeData={dataTree}
          loadData={onLoadChild as any}
        />
      </Modal>
    </div>
  );
};

export default CreateLive;
// 生成一个全局唯一标识符
function guid() {
  // 生成一个随机数，并转换为16进制字符串
  function S1() {
    return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1);
  }
  // 返回一个由8位16进制数组成的字符串，中间用"-"分隔
  return (S1() + S1() + "-" + S1() + "-" + S1() + "-" + S1() + "-" + S1() + S1() + S1());
}
