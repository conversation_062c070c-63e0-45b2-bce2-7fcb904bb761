/*
 * @Author: 冉志诚
 * @Date: 2023-08-01 14:14:20
 * @LastEditTime: 2023-08-01 14:14:27
 * @FilePath: \frontend\src\pages\live\Create\components\StudentModal\index.tsx
 * @Description:
 */

import {
  Button,
  Modal,
  ModalProps,
  TablePaginationConfig,
  message,
} from 'antd';
import React, { useImperativeHandle, useRef } from 'react';
import { useImmer } from 'use-immer';
import style from './index.less';
import {
  ActionType,
  ProColumns,
  ProTable,
  ProTableProps,
} from '@ant-design/pro-components';
import Access from '@/components/Access';
import { SelectOutlined, ImportOutlined } from '@ant-design/icons';
import UploadModal, {
  UploadModalProps,
  UploadRefType,
} from '@/components/UploadModal';
import ClassModal, { ClassRefType } from '../ClassModal';

type Item = {};
type TableParams = {};

const StudentModal: React.FC<StudentModalProps> = ({
  type,
  actionRef: propActionRef,
}) => {
  const [open, setOpen] = useImmer(false);
  const [loading, setLoading] = useImmer({
    table: false,
  });
  const [selectedRowKeys, setSelectedRowKeys] = useImmer<React.Key[]>([]);
  const [selectedRows, setSelectedRows] = useImmer<Item[]>([]);
  const [tableParams, setTableParams] = useImmer<TableParams>({});
  const actionRef = useRef<ActionType>();
  const uploadRef = useRef<UploadRefType>(null);
  const classRef = useRef<ClassRefType>(null);
  useImperativeHandle(
    propActionRef,
    () => {
      return {
        setOpen,
      };
    },
    [],
  );
  const modalConfig: ModalProps = {
    open,
    title: '添加学生',
    width: 1000,
    onCancel() {
      setOpen(false);
    },
    maskClosable: false,
    destroyOnClose: false,
  };
  const columns: ProColumns<Item>[] = [
    {
      title: '姓名',
      fieldProps: {
        placeholder: '请输入姓名',
      },
      formItemProps: {
        label: null,
      },
      dataIndex: 'name',
    },
    {
      title: '学号',
      search: false,
      dataIndex: 'student_id',
    },

    {
      title: '性别',
      search: false,
      dataIndex: 'sex',
      width: 50,
    },
    {
      title: '学院',
      fieldProps: {
        placeholder: '请输入学院',
      },
      formItemProps: {
        label: null,
      },
      dataIndex: 'college',
    },
    {
      title: '专业',
      fieldProps: {
        placeholder: '请输入专业',
      },
      formItemProps: {
        label: null,
      },
      dataIndex: 'major',
    },
  ];
  const [pagination, setPagination] = useImmer<TablePaginationConfig>({
    current: 1,
    pageSize: 10,
    showTotal: total => `共${total}条`,

    onChange(page, pageSize) {
      setPagination(arg => {
        arg.current = page;
        arg.pageSize = pageSize;
      });
    },
  });

  const tableConfig: ProTableProps<Item, TableParams> = {
    toolBarRender: false,
    search: {
      optionRender(searchConfig, props, dom) {
        return [
          <div
            style={{
              display: 'flex',
              gap: 10,
            }}
            key="defaultDom"
          >
            {dom}
            {type === 'student' ? null : ( // </Button> //   批量导入 // > //   onClick={() => [uploadRef.current?.setOpen(true)]} //   icon={<ImportOutlined />} //   type="link" //   key="select" // <Button
              <>
                <Button
                  key="select"
                  type="link"
                  icon={<SelectOutlined />}
                  onClick={() => {
                    classRef.current?.setOpen(true);
                  }}
                >
                  班级选择
                </Button>
                <ClassModal actionRef={classRef} />
              </>
            )}
          </div>,
          <div key="empty"></div>,
        ];
      },
      span: 4,
      showHiddenNum: true,
      labelWidth: 'auto',
      className: 'in-pro-search',
      searchText: '检索',
    },
    columns,
    pagination,
    loading: loading.table,
    params: tableParams,
    actionRef,
    rowKey: 'id',
    onLoadingChange(loading) {
      setLoading(arg => {
        arg.table = !!loading;
      });
    },
    async request(params, sort, filter) {
      try {
        console.log('发生请求', params);
        return {
          data: Array(100).fill({}),
          // success 请返回 true，
          // 不然 table 会停止解析数据，即使有数据
          success: true,
          // 不传会使用 data 的长度，如果是分页一定要传
          total: 100,
        };
      } catch (error) {
        return {
          data: [],
          success: false,
        };
      }
    },
    rowSelection: {
      // 自定义选择项参考: https://ant.design/components/table-cn/#components-table-demo-row-selection-custom
      // 注释该行则默认不显示下拉选项
      // selections: [Table.SELECTION_ALL, Table.SELECTION_INVERT],
      columnWidth: 80,
      selectedRowKeys,
      onChange(keys, rows) {
        setSelectedRowKeys(keys);
        setSelectedRows(() => rows);
        console.log(
          '%c [ keys ]-184',
          'font-size:13px; background:pink; color:#bf2c9f;',
          `selected ${keys},${rows}`,
        );
      },
    },
  };
  const uploadConfig: UploadModalProps = {
    title: '重点群体毕业生信息',
    tip: (
      <>
        仅允许导入xls、xlsx格式文件。
        <span
          onClick={async () => {
            try {
            } catch (error) {
              message.error('下载失败请稍后再试');
            }
          }}
          style={{
            color: '#1890ff',
            cursor: 'pointer',
          }}
        >
          下载模板
        </span>
      </>
    ),
    errCallback(err) {
      message.error(err);
    },
    accept: '.xls,.xlsx',
    async onOk(fileList) {
      if (fileList.length === 0) {
        message.error('导入文件不能为空');
        return false;
      }
      try {
        let code = 200,
          data = 1,
          msg = '导入失败';

        if (code === 200 && !data) {
          message.success('导入成功');
          actionRef.current?.reload();
          return true;
        } else {
          if (data)
            Modal.confirm({
              title: '你确认下载导入失败错误提示文件吗？',
              cancelText: '取消',
              okText: '确认',
              closable: true,
              maskClosable: true,
              async onOk() {
                try {
                  const fileName = data;
                } catch (error) {
                  message.error((error as any).message);
                }
              },
            });
          throw new Error(data ? '导入失败' : msg);
        }
      } catch (error) {
        message.error((error as any).message);

        return false;
      }
    },
    okText: '导入',
    actionRef: uploadRef,
  };
  return (
    <Modal {...modalConfig}>
      <div className={style.container}>
        <Access accessible={type === 'student'}>
          <aside className={style.tree}></aside>
        </Access>
        <main className={style.table}>
          <ProTable {...tableConfig} />
        </main>
      </div>
      <UploadModal {...uploadConfig} />
    </Modal>
  );
};

interface StudentModalProps {
  type: 'class' | 'student';
  actionRef: React.MutableRefObject<StudentRefType | null>;
}
export type StudentRefType = {
  setOpen: (open: boolean) => void;
};
export default StudentModal;
StudentModal.displayName = 'StudentModal';
