const config = {
  safeMode: true,
  safeMethod: 'POST',
  unSafeMethods: ['head', 'put', 'delete', 'patch'],
  requestType: 'json',
  requestSleepTime: 500,
  successCode: 'livemanage.0000.0000',
  successCodeUniform: 'cloud_sc.0000.0000',
  successCodeUpform: 'success',
  defaultPageSize: 10,
  defaultCover:'/learn/workbench/img/v2/live_default_cover.png',
  vedioType:['flv','m3u8']
}
export default config
