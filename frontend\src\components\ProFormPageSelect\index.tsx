import { omit } from '@/utils/utils';
import {
  ProFormSelect,
  ProFormSelectProps,
  RequestOptionsType,
} from '@ant-design/pro-components';
import React, { useEffect, useMemo } from 'react';
import { useImmer } from 'use-immer';
import style from './index.less';
type Params = {
  current: number;
  pageSize: number;
  keyWords?: string;
};

/**
 * @description 简单易用的分页select
 */
const ProFormPageSelect: React.FC<ProFormPageSelectProps> = React.memo(
  props => {
    const [oldData, setOldData] = useImmer<
      Record<number, RequestOptionsType[]>
    >({});
    const [params, setParams] = useImmer<Params>({
      current: 1,
      pageSize: props?.pageSize ?? 40,
      keyWords: '',
    });
    const [total, setTotal] = React.useState<number | undefined>();
    const [haveMore, setHaveMore] = React.useState<boolean>(true);
    const [loading, setLoading] = useImmer({
      value: false,
      isFirst: true,
    });
    const result = useMemo(() => {
      const result = [...Object.values(oldData).flat(Infinity)] as {
        label: string;
        value: string;
      }[];
      // 去重
      const obj = result.reduce((prev, cur) => {
        prev[cur.value] = cur;
        return prev;
      }, {} as Record<string, { label: string; value: string }>);
      return Object.values(obj);
    }, [oldData]);
    useEffect(() => {
      request(params);
    }, [params]);
    const request = async ({ keyWords, current, pageSize }: Params) => {
      try {
        if (loading.isFirst) {
          setLoading({
            value: true,
            isFirst: false,
          });
        }
        const { total, data } = await props?.request({
          keyWords,
          current,
          pageSize,
        });
        const result = [...Object.values(oldData).flat(Infinity), ...data];
        setTotal(total);
        if (
          (pageSize !== 1 && data.length === 0) ||
          (result.length >= total && total !== 0)
        ) {
          setHaveMore(false);
        }
        setOldData(draft => {
          draft[current] = data;
        });
        setLoading({
          value: false,
          isFirst: false,
        });
        return result;
      } catch (error) {
        console.error(error);
        setLoading({
          value: false,
          isFirst: false,
        });
        return [];
      }
    };
    return (
      <ProFormSelect
        {...omit(props, ['request'])}
        params={params}
        options={result}
        fieldProps={{
          onBlur: e => {
            document.body.classList.remove(style.hidden);
            document.documentElement.classList.remove(style.scroll);
          },
          onDropdownVisibleChange(open) {
            if (!open) {
              document.body.classList.remove(style.hidden);
              document.documentElement.classList.remove(style.scroll);
            }
          },
          onPopupScroll: haveMore
            ? e => {
                e.stopPropagation();
                e.currentTarget.onmouseover = () => {
                  document.documentElement.classList.add(style.scroll);
                  document.body.classList.add(style.hidden);
                };
                e.currentTarget.onmouseout = () => {
                  document.body.classList.remove(style.hidden);
                  document.documentElement.classList.remove(style.scroll);
                };
                if (!haveMore) return;
                const { target } = e;
                e.nativeEvent.stopImmediatePropagation();
                const { scrollTop, scrollHeight, clientHeight } = target as any;
                if (scrollTop + clientHeight >= scrollHeight - 30) {
                  // 判断是否超过总数
                  if (total && total <= Object.keys(oldData).length) {
                    return;
                  }
                  setParams(draft => {
                    draft.current += 1;
                  });
                }
              }
            : undefined,
          onSearch(value) {
            if (params.keyWords !== value) {
              setLoading({
                value: false,
                isFirst: true,
              });
              setParams({
                current: 1,
                pageSize: props?.pageSize ?? 40,
                keyWords: value,
              });
              console.log('清空');
              setOldData({});
              props?.fieldProps?.onSearch?.(value);
            }
          },
          loading: loading.value,
          getPopupContainer: triggerNode => {
            return triggerNode.parentElement;
          },
          ...props?.fieldProps,
        }}
      />
    );
  },
);

type ProFormPageSelectProps = Omit<ProFormSelectProps, 'request'> & {
  request: (params: {
    keyWords?: string;
    current: number;
    pageSize: number;
  }) => Promise<{
    data: RequestOptionsType[];
    total: number;
  }>;
  pageSize?: number;
};
export default ProFormPageSelect;
ProFormPageSelect.displayName = 'ProFormPageSelect';
