.activity_create_container {
  .activity_header {
    margin: -24px -24px 24px -24px;
    .header_title {
      font-size: 18px;
      font-weight: 500;
    }
  }
}
.activity_main_container {
  .live_cover_container {
    display: flex;
    align-items: flex-end;
  }
  .live_upload {
    position: relative;
    margin-right: 20px;
    .avatar_img {
      width: 160px;
      height: 90px;
      border: 1px solid #ccc;
    }
    .upload_tip {
      position: absolute;
      top: 0;
      left: 0;
      bottom: 0;
      right: 0;
      display: flex;
      justify-content: center;
      align-items: center;
      transition: all 0.5s;
      cursor: pointer;
      color: #ffffff;
      background: rgba(0, 0, 0, 0.6);
    }
    .hover_show {
      opacity: 0;
      &:hover {
        opacity: 1;
      }
    }
  }
  .module_container{
    .ant-form{
      .flexItem{
        .ant-form-item-control{
          .ant-form-item-control-input-content{
            display: flex;
            flex-direction: row;
            align-items: center;
            .ant-select{
              margin-left: 15px;
              width: 300px;
            }
            .ant-input{
              margin-left: 15px;
              width: 300px;
            }
          }
          .ant-form-item-explain{
            margin-left: 59px;
          }
        }
      }
    }
  }
  .button_wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
  }

}

.display-now {
  .ant-picker-now {
    display: none !important;
  }
}
.saveModal{
  .ant-modal-body{
    display: flex;
    align-items: center;
    justify-content: center;
    .ant-select{
      width: 250px;
    }
  }
}
