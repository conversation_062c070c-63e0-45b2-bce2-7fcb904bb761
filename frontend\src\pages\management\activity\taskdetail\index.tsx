import React, { useEffect, useState } from 'react';
import { Drawer, message, Table } from 'antd';
import activityManagementService from '@/service/activityManagementService';
import moment from "moment";
interface TaskDrawerProps {
  visible: boolean;
  onClose: () => void;
  taskDrawerData: any;
}


const TaskDrawer: React.FC<TaskDrawerProps> = ({ visible, onClose, taskDrawerData }) => {
  const [data, setData] = useState([]);
  //重做任务   
  const restartTask = (record: any) =>{
    activityManagementService.reStartTask({
        task_id: record.task_id,
        status: record.status
    }).then((res: any) => {
        if(res.error_msg == 'Success'){
            message.success('重做任务成功');
        }else{
            message.error('重做任务失败');
        }
    }).catch((err: any) => {
        message.error('重做任务失败');
    })
  }

  const columns = [
    { title: '任务名称', dataIndex: 'task_name', key: 'task_name' },
    { title: '状态', dataIndex: 'status', key: 'status' },
    { title: '开始时间', dataIndex: 'create_time', key: 'create_time',render:(text:any)=>{    
        if(!text){
          return '--'
      }else{
          return moment(text * 1000).format('YYYY-MM-DD HH:mm:ss');
      }
    } },
    { title: '操作', dataIndex: 'task_id', key: 'task_id', render: (text:any,record:any) => <a onClick={()=>restartTask(record)}>重做</a> },
  ];
  

  useEffect(() => {
    if (visible && taskDrawerData) {        
        activityManagementService.getTaskDetail(taskDrawerData._id) .then((res: any) => {
            if(res.error_msg == 'Success'){
                setData(res?.extend_message || []);
            }
        });
    } else if (!visible) {
      setData([]);
    }
  }, [visible, taskDrawerData]);

  return (
    <Drawer
      title="录播任务"
      placement="right"
      width={800}
      onClose={onClose}
      open={visible}
      destroyOnClose
    >
      <Table columns={columns} dataSource={data} key="_id" pagination={false} />
    </Drawer>
  );
};

export default TaskDrawer;
