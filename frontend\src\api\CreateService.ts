/*
 * @Author: 冉志诚
 * @Date: 2023-08-23 14:14:40
 * @LastEditTime: 2023-09-20 14:50:49
 * @FilePath: \frontend\src\api\CreateService.ts
 * @Description:
 */
import request from './request';
import { IResponse, RmanResponse, UpFormResponse } from '.';
import Request from '@/constant/request';
export namespace CreateService {
  export const apply = (data: any) => {
    return request<IResponse<null>>('/v1/general-schedule/apply/', {
      method: 'POST',
      data,
      prefix: Request.PREFIX.LIVE_MANAGE,
    });
  };
  export const save = (data: any) => {
    return request<IResponse<null>>('/v1/general-schedule/draft/', {
      method: 'POST',
      data,
      prefix: Request.PREFIX.LIVE_MANAGE,
    });
  };
  export const cancel = (ids: React.Key[]) => {
    return request<IResponse<null>>('/v1/general-schedule/apply/cancel/', {
      method: 'POST',
      data: {
        ids,
      },
      prefix: Request.PREFIX.LIVE_MANAGE,
    });
  };
  export const patch = (data: any) => {
    return request<IResponse<null>>('/v1/general-schedule/man/patch', {
      method: 'POST',
      data,
      prefix: Request.PREFIX.LIVE_MANAGE,
    });
  };
  export const getDetail = (params: { _id: string }) => {
    return request<IResponse<ApplyDetail>>('/v1/general-schedule/man/detail/', {
      method: 'get',
      params,
      prefix: Request.PREFIX.LIVE_MANAGE,
    });
  };
  export const getAreaLive = (params: {
    start_time: number;
    end_time: number;
  }) => {
    return request<IResponse<AreaLiveTreeNode[]>>(
      '/v1/general-schedule/area/live/',
      {
        method: 'get',
        params,
        prefix: Request.PREFIX.LIVE_MANAGE,
      },
    );
  };
  export type AreaLiveTreeNode = {
    id: number;
    type: string;
    code: string;
    name: string;
    super_area: null;
    total: number;
    children: AreaLiveTreeNode[];
    area_fullname: string;
    is_occupy?: boolean;
  };
  export interface ApplyDetail {
    _id: string;
    origin_start_time: number;
    live_now: boolean;
    dynamic_start_time: number;
    schedule_status: number;
    schedule_type: number;
    origin_finish_time: number;
    live_address: LiveAddress[];
    live_address_type: number;
    online_numbers: number;
    add_time: number;
    update_time: number;
    voice_identify: boolean;
    voice_translation: boolean;
    voice_tts: boolean;
    live_name: string;
    live_introduce: string;
    series_lives?: {
      live_address: LiveAddress[];
      area_id: number;
      area_name: string;
      start_time: number;
      end_time: number;
      live_address_type: number;
    }[];
    live_cover: string;
    live_speaker: string;
    live_speaker_id: string;
    live_speaker_trait: null;
    room_id: string;
    area_name: string;
    campus: string;
    academic_building: string;
    classroom_number: string;
    view_numbers: number;
    look_back: boolean;
    look_back_day: number;
    save_resource: boolean;
    save_folder: string;
    save_folder_name: null;
    look_permission: string;
    look_back_status: null;
    create_user_code: string;
    look_back_expire_time: null;
    look_password: null;
    is_si: boolean;
    is_share: boolean;
    view_object: null;
    is_series: boolean;
    look_custom_permission: string;
    look_custom_roles: string;
    look_custom_course_classes: null;
    look_custom_students: null;
    apply_status: string;
    apply_time: number;
    apply_user_code: string;
    apply_user_name: string;
    audit_reason: null;
    audit_user_code: null;
    audit_user_name: null;
    audit_time: null;
    area_id: null;
  }

  export interface LiveAddress {
    stream_name: string;
    push_url: string;
    pull_url: string;
    _id: number;
    is_mute: boolean;
    stream_address: string;
    voice_identify: boolean;
    flow_name?: string;
  }
  export const auditPage = (params: {
    page: number;
    size: number;
    live_name?: string;
    order_by?: string;
  }) => {
    return request<IResponse<List<AuditPage>>>('/v1/general-schedule/audit/', {
      method: 'get',
      params,
      prefix: Request.PREFIX.LIVE_MANAGE,
    });
  };
  export const timeArrange = (params: {
    start_time: number;
    end_time: number;
  }) => {
    return request<IResponse<any[]>>(
      '/v1/general-schedule/user/time/arrange/',
      {
        method: 'get',
        params,
        prefix: Request.PREFIX.LIVE_MANAGE,
      },
    );
  };
  export type List<T> = {
    total: number;
    size: number;
    page: number;
    results: T[];
  };
  export type AuditPage = {
    _id: string;
    origin_start_time: number | null;
    live_now: boolean;
    dynamic_start_time: number | null;
    schedule_status: number;
    schedule_type: number;
    origin_finish_time: number | null;
    live_address: LiveAddress[] | null;
    live_address_type: number | null;
    online_numbers: number;
    add_time: number;
    update_time: number;
    voice_identify: boolean;
    voice_translation: boolean;
    voice_tts: boolean;
    live_name: string;
    live_introduce: string;
    live_cover: string;
    live_speaker: string;
    live_speaker_id: string;
    live_speaker_trait: null;
    room_id: string;
    area_name: string;
    campus: string;
    academic_building: string;
    classroom_number: string;
    view_numbers: number;
    look_back: boolean;
    look_back_day: number | null;
    save_resource: boolean;
    save_folder: null | string;
    save_folder_name: null;
    look_permission: string;
    look_back_status: number | null;
    create_user_code: string;
    look_back_expire_time: null;
    look_password: null | string;
    is_si: boolean;
    is_share: boolean;
    view_object: null;
    is_series: boolean;
    series_lives: any[];
    look_custom_permission: null | string;
    look_custom_roles: null | string;
    look_custom_course_classes: null;
    look_custom_students: null;
    apply_status: string;
    apply_time: number;
    apply_user_code: string;
    apply_user_name: string;
    audit_reason: null;
    audit_user_code: null;
    audit_user_name: null;
    audit_time: null;
    area_id: number | null;
  };
  export interface LiveAddress {
    stream_name: string;
    push_url: string;
    pull_url: string;
    _id: number;
    is_mute: boolean;
    stream_address: string;
    voice_identify: boolean;
  }
}
