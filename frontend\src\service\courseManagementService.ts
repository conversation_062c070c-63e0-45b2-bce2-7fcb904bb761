import { request } from 'umi'
import RequestConstant from "@/constant/request";
namespace courseManagementService {
    // 课程名，课程号，课序号
    export const getCourse = () => {
        return request(`/v1/search/offline/course`, {
            method: 'POST',
            prefix: RequestConstant.module.rman,
        })
    }

    export const getTeachers = (data: any) => {
        //获取教师列表
        return request(`/v1/role/search/user/bind`, {
            method: 'POST',
            prefix: RequestConstant.module.unifiedplatform,
            body: data,
        })
    }
    export const getPagingData = (data: string) => {
        //获取分页元数据
        return request(`/v1/base/data/database/source/data?${data}`, {
            method: 'GET',
            prefix: RequestConstant.module.unifiedplatform,
        })
    }
    export const getCurrentWeekly = () => {
        //获取当前学期周次
        return request(`/v1/base/data/database/get/current/weeks`, {
            method: 'GET',
            prefix: RequestConstant.module.unifiedplatform,
        })
    }
    export const getfields = () => {
        //获取元数据
        return request(`/v1/base/data/database/get/show/base/data`, {
            method: 'GET',
            prefix: RequestConstant.module.unifiedplatform,
        })
    }
    export const getSection = () => {
        //获取节次
        return request(`/v1/base/data/database/select/default/course/time`, {
            method: 'GET',
            prefix: RequestConstant.module.unifiedplatform,
        })
    }
    export const getCourseScheduleList = (data: string) => {
        //获取课程直播计划列表
        // return request(`/v1/course-schedule/man/?${data}`, {
        return request(`/schedule/course_loop/?${data}`, {
            method: 'GET',
            prefix: RequestConstant.module.ipingestman,
            params:{is_live:true}
        })
    }
    export const getCurrSearchList = (data: string) => {
        //获取课程直播计划列表
        return request(`/schedule/course_loop/?${data}`, {
            method: 'GET',
            prefix: RequestConstant.module.ipingestman,
        })
    }
    export const addCourseSchedule = (data: any) => {
        return request(`/v1/course-schedule/man/`, {
            method: 'POST',
            body: data,
            prefix: RequestConstant.module.default,
        })
    }
    // 获取教室
    export const searchtree = () => {
        return request('/area_tree/', {
            method: 'GET',
            prefix: RequestConstant.module.ipingestman,
        })
    }
    // 删除
    export const deleteSchedule = (data: CourseType.Idelete) => {
        return request('/v1/course-schedule/man/', {
            method: 'DELETE',
            data,
            prefix: RequestConstant.module.default,
        })
    }
    // 语音识别
    export const identifySchedule = (data: CourseType.IVideoTranslation) => {
        return request('/v1/course-schedule/man/identify/', {
            method: 'PATCH',
            data,
            prefix: RequestConstant.module.default,
        })
    }
    // 课程计划批量启用/关闭语音翻译转写
    export const translationSchedule = (data: CourseType.IVideoTranslation) => {
        return request('/v1/course-schedule/man/translation/', {
            method: 'PATCH',
            data,
            prefix: RequestConstant.module.default,
        })
    }



    // 课程直播任务监控列表
    export const getScheduleTasks = (data: string) => {
        return request(`/v1/schedule-tasks/man/course/?${data}`, {
            method: 'GET',
            prefix: RequestConstant.module.default,
        })
    }
    // 删除
    export const deleteScheduleTasks = (data: CourseType.Idelete) => {
        return request('/v1/schedule-tasks/man/course/', {
            method: 'DELETE',
            data,
            prefix: RequestConstant.module.default,
        })
    }
    // 课程直播任务停止
    export const stopScheduleTasks = (data: CourseType.IstopCourse) => {
        return request('/v1/schedule-tasks/man/course/stop/', {
            method: 'PATCH',
            data,
            prefix: RequestConstant.module.default,
        })
    }

    // 根据教室id获取直播流地址
    export const getareUrls = (data: string) => {
        return request(`/area_urls?area=${data}`, {
            method: 'GET',
            prefix: RequestConstant.module.ipingestman,
        })
    }
}

export default courseManagementService