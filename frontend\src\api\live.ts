/*
 * @Author: 冉志诚
 * @Date: 2023-08-23 14:22:05
 * @LastEditTime: 2023-08-23 14:22:13
 * @FilePath: \frontend\src\api\live.ts
 * @Description:
 */
import { request } from 'umi';
import { IResponse, RmanResponse, UpFormResponse } from '.';
import Request from '@/constant/request';

export namespace LiveService {
  export const uploadCover = (img: string) => {
    return request<IResponse<{ url: string }>>(
      '/v1/general-schedule/man/cover/',
      {
        method: 'POST',
        data: {
          image_base64: img,
        },
        prefix: Request.PREFIX.LIVE_MANAGE,
      },
    );
  };

  /**
   * 删除活动直播计划
   */
  export const deleteLiveBulk = (_ids: React.Key[]) => {
    return request<IResponse<string>>('/v1/general-schedule/man/delete', {
      method: 'post',
      data: {
        _ids,
      },
      prefix: Request.PREFIX.LIVE_MANAGE,
    });
  };
  export const getPagingData = (params: {
    school: string;
    pageIndex: number;
    pageSize: number;
    keyword?: string;
    sourceType: 0;
  }) => {
    //获取分页元数据
    return request<UpFormResponse<PagingDataRes>>(
      `/v1/base/data/database/source/data`,
      {
        method: 'GET',
        params,
        prefix: Request.PREFIX.UP_FORM,
      },
    );
  };
  export interface PagingDataRes {
    pageIndex: number;
    pageTotal: number;
    pageSize: number;
    recordTotal: number;
    results: {
      fieldCode: string;
      fieldValue: string;
      extendedValue: string;
    }[];
  }
  export const getTree = (level: number = 2) => {
    return request<RmanResponse<TreeNode[]>>(`/v1/folder/all/tree`, {
      method: 'GET',
      prefix: Request.PREFIX.RMAN,
      params: {
        level,
      },
    });
  };
  /**
   * 查询区域-树结构
   */
  export const fetchAreaTree = () => {
    return request<IResponse<AreaTreeNode[]>>('/area_tree/', {
      prefix: Request.PREFIX.IPINGESTMAN,
      method: 'GET',
    });
  };
  export enum MyLiveTypeName {
    CREATE = 'my_create',
    SPEAKER = 'my_speaker',
    COLLECT = 'my_collect',
    SHARE = 'my_share',
  }
  export type LiveParams = {
    type_name?: MyLiveTypeName;
    schedule_status?: number;
    live_name?: string;
    page: number;
    size: number;
    order_by?: string;
    apply_status?: string;
    is_share?: boolean;
    course_name?: string;
  };

  export const getMyLive = (params: LiveParams) => {
    return request<IResponse<List<MyLiveData[]>>>(
      '/v1/general-schedule/my/live/',
      {
        prefix: Request.PREFIX.LIVE_MANAGE,
        method: 'GET',
        params,
      },
    );
  };
  export const getAllActivityLive = (params: LiveParams) => {
    return request<IResponse<List<MyLiveData[]>>>(
      '/v1/general-schedule/workbench/man/',
      {
        prefix: Request.PREFIX.LIVE_MANAGE,
        method: 'GET',
        params,
      },
    );
  };
  export const getAllCourseLive = (params: LiveParams) => {
    return request<IResponse<List<AllLiveData[]>>>(
      '/v1/schedule-tasks/workbench/man/course/',
      {
        prefix: Request.PREFIX.LIVE_MANAGE,
        method: 'GET',
        params,
      },
    );
  };
  export type AllLiveData = {
    _id: string;
    schedule_id: string;
    room_id: string;
    start_time: number;
    finish_time: number;
    task_status: number;
    task_status_msg: string;
    online_number: number;
    step_weekly: number;
    fail_reason: null;
    course_metadata: string;
    voice_identify: boolean;
    voice_translation: boolean;
    voice_tts: boolean;
    view_numbers: number;
  };
  export interface List<T> {
    total: number;
    results: T;
    page: number;
    size: number;
  }

  export interface MyLiveData {
    _id: string;
    origin_start_time: number;
    live_now: boolean;
    dynamic_start_time: number;
    schedule_status: number;
    schedule_type: number;
    origin_finish_time: number;
    live_address: LiveAddress[] | null;
    live_address_type: number | null;
    online_numbers: number;
    add_time: number;
    update_time: number;
    voice_identify: boolean;
    voice_translation: boolean;
    voice_tts: boolean;
    live_name: string;
    live_introduce: string;
    live_cover: string;
    live_speaker: string;
    live_speaker_id: string;
    live_speaker_trait: null;
    room_id: string;
    area_name: string;
    campus: string;
    academic_building: string;
    classroom_number: string;
    view_numbers: number;
    look_back: boolean;
    look_back_day: number | null;
    save_resource: boolean;
    save_folder: null | string;
    save_folder_name: null;
    look_permission: string;
    look_back_status: number | null;
    create_user_code: string;
    look_back_expire_time: number | null;
    look_password: null;
    is_si: boolean;
    is_share: boolean;
    view_object: null | string;
    is_series: boolean;
    look_custom_permission: null;
    look_custom_roles: null;
    look_custom_course_classes: null;
    look_custom_students: null;
    apply_status: string;
    apply_time: null;
    apply_user_code: null;
    apply_user_name: null;
    audit_reason: null;
    audit_user_code: null;
    audit_user_name: null;
    audit_time: null;
    area_id: null;
  }

  export interface LiveAddress {
    _id: string;
    stream_address: string;
    push_url: string;
    is_mute: boolean;
    stream_name: string;
    pull_url?: string;
  }

  export const fetchFlowAddessByArea = (id: number) => {
    // return request<Request.IResponse<ActivityType.IFlowClassroom>>('/area_urls', {
    return request<IResponse<FlowAddress>>('/area_voice_urls', {
      prefix: Request.PREFIX.IPINGESTMAN,
      method: 'GET',
      params: {
        area: id,
      },
    });
  };
  export type SHOW_STREAM_NAME_MAP_KEY = keyof typeof STREAM_NAME_MAP;
  export const STREAM_NAME_MAP = {
    screenAddr: '屏幕画面',
    teacher_full_url_flv: '教师全景',
    teacher_singl_url_flv: '教师单目',
    teacher_trace_url_flv: '教师近景',
    guide_url_flv: '导切画面',
    student_full_url_flv: '学生全景',
    student_singl_url_flv: '学生单目',
    student_trace_url_flv: '学生近景',
  };
  /**
   * 查询流地址列表
   */
  export const fetchFlows = function (
    page: number,
    size: number,
    keyword: string,
  ) {
    return request<
      IResponse<{
        results: FlowType[];
        page: number;
        size: number;
        total: number;
      }>
    >(`/v1/stream/man/`, {
      params: {
        str: new Date().getTime(),
        stream_name: keyword,
        page,
        size,
      },
      prefix: Request.PREFIX.LIVE_MANAGE,
    });
  };
  export type FlowType = {
    _id: string;
    stream_name: string;
    stream_address: string;
    push_url: string;
    pull_url: string;
    stream_status: boolean;
    is_used: boolean;
    create_time: number;
    is_mute: boolean;
  };

  export type FlowAddress = Partial<{
    screenAddr: string;
    ai_url: string;
    guide_url: string;
    teacher_full_url: string;
    teacher_singl_url: string;
    teacher_trace_url: string;
    student_full_url: string;
    student_trace_url: string;
    student_singl_url: string;
    teacher_full_has_voice: boolean;
    teacher_trace_has_voice: boolean;
    teacher_singl_has_voice: boolean;
    student_full_has_voice: boolean;
    student_trace_has_voice: boolean;
    student_singl_has_voice: boolean;
    name: string;
    ai_url_flv: string;
    guide_url_flv: string;
    teacher_full_url_flv: string;
    teacher_trace_url_flv: string;
    teacher_singl_url_flv: string;
    student_full_url_flv: string;
    student_trace_url_flv: string;
    student_singl_url_flv: string;
  }>;
  export type AreaTreeNode = {
    id: number;
    type: string;
    code: string;
    name: string;
    super_area: null;
    total: number;
    children: AreaTreeNode[];
    area_fullname: string;
    is_occupy?: boolean;
  };
  export interface TreeNode {
    id: string;
    code: null;
    name: string;
    value: string;
    path: string;
    parentId: null;
    layer: number;
    childCount?: number;
    children?: TreeNode[];
  }

  export const onloadTreeLeaf = (params: {
    folderPath: string;
    isOwner?: boolean;
    isChildCount?: boolean;
  }) => {
    return request<RmanResponse<LeafNode[]>>(`/v1/folder/children`, {
      method: 'GET',
      prefix: Request.PREFIX.RMAN,
      params: {
        isOwner: true,
        isChildCount: true,
        ...params,
      },
    });
  };
  /**获取当前学期的周次列表 */
  export const semesterWeek = () => {
    return request<IResponse<SemesterWeekType>>(
      `/v1/general-schedule/semester/week/`,
      {
        method: 'GET',
        prefix: Request.PREFIX.LIVE_MANAGE,
      },
    );
  };

  export type SemesterWeekType = {
    this_week: number;
    weeks: number[];
  };
  /** 获取某教师在某学期某一周的直播情况 */
  export const userLive = (week: number) => {
    return request<IResponse<UserLiveResponseType[]>>(
      `/v1/general-schedule/user/live/`,
      {
        method: 'GET',
        prefix: Request.PREFIX.LIVE_MANAGE,
        params: {
          week,
        },
      },
    );
  };
  export const audit = (data: AuditBody) => {
    return request<IResponse<UserLiveResponseType[]>>(
      `/v1/general-schedule/audit/`,
      {
        method: 'post',
        prefix: Request.PREFIX.LIVE_MANAGE,
        data,
      },
    );
  };
  export const myCourseLive = (params: {
    page: number;
    size: number;
    course_name?: string;
  }) => {
    return request<IResponse<List<CourseLiveData[]>>>(
      `/schedule/my_course_live/`,
      {
        method: 'get',
        prefix: Request.PREFIX.IPINGESTMAN,
        params,
      },
    );
  };
  export type CourseLiveData = {
    id: number;
    area: string;
    area_id: number;
    course_section: string;
    course_section_start: number;
    course_section_end: number;
    course_id: string;
    course_no: string;
    course: string;
    teacher: string;
    college: string;
    major: null;
    course_name: string;
    teacher_name: string;
    college_name: string;
    major_name: null;
    times: string;
    week: string;
    create_time: number;
    start_time: null;
    end_time: null;
    start_hour: number;
    start_minute: number;
    end_hour: number;
    end_minute: number;
    _id: null;
    update_success: boolean;
    class_weekly: string;
    semester: string;
    update_time: number;
    is_customize: boolean;
    is_voice_identify: boolean;
    is_voice_translation: boolean;
    is_voice_tts: boolean;
  };

  export const shareCancel = (params: { live_id: string }) => {
    return request<IResponse<null>>(`/v1/general-schedule/live/share/cancel/`, {
      method: 'get',
      prefix: Request.PREFIX.LIVE_MANAGE,
      params,
    });
  };
  export type AuditBody = {
    id: string;
    is_pass: boolean;
    audit_reason?: string;
  };
  export interface UserLiveResponseType {
    date: string;
    lives: UserLiveType[];
    week_of_day: number;
  }

  export interface UserLiveType {
    area_id: number;
    area_name: string;
    arrange_type: string;
    end_time: string;
    live_name: string;
    live_no: number;
    start_time: string;
    // 索引位置
    index?: number;
  }
  export interface LeafNode {
    showName: string;
    name: string;
    path: string;
    contentId: string;
    parentPath: string;
    childCount: number;
  }
  /**
   * 活动直播任务开始
   *
   */
  export const updateLiveToStart = (_id: string) => {
    return request<IResponse<null>>('/v1/general-schedule/man/start/patch', {
      method: 'post',
      data: {
        _id,
      },
      prefix: Request.PREFIX.LIVE_MANAGE,
    });
  };

  /**
   * 活动直播任务停止
   *
   */
  export const updateLiveToEnd = (_id: string) => {
    return request<IResponse<null>>('/v1/general-schedule/man/stop/patch', {
      data: {
        _id,
      },
      method: 'post',

      prefix: Request.PREFIX.LIVE_MANAGE,
    });
  };
  export enum ArrangeType {
    活动直播 = 'activity_live',
    课程直播 = 'course_live',
    课表安排 = 'curriculum',
  }
}
