import { RequestConfig } from 'umi';
import { RequestOptionsInit } from 'umi-request';
import { sleep } from '@/utils';
import config from '@/utils/config';
import RequestConstant from '@/constant/request';
import { globalService } from './service/globalService';
import React from 'react';
import './reset.less';
import {
  StyleProvider,
  legacyLogicalPropertiesTransformer,
} from '@ant-design/cssinjs';

const requestInterceptors = (url: string, options: RequestOptionsInit) => {
  if (!options.prefix) {
    // 兼容红温的老代码
    if (!url.startsWith('/api')) {
      url = RequestConstant.module.default + url;
    }
  }
  /**
   * 处理极端网络环境，添加后缀
   */
  if (config.safeMode && options.method) {
    let method = options.method?.toLowerCase();
    if (config.unSafeMethods.includes(method)) {
      options.method = config.safeMethod;
      url = url.endsWith('/') ? `${url}${method}` : `${url}/${method}`;
    }
  }
  // options.requestType = config.requestType as any

  return {
    url,
    options: {
      ...options,
      headers: {
        ...options?.headers,
        'Content-type': 'application/json',
      },
    },
  };
};

const handleResponse = () => {
  // TODO 处理请求返回结果,未登录等
};

const responseInterceptors = async (response: Response, options: any) => {
  // 需要等待响应
  if (options.isSleep) {
    await sleep(
      isNaN(options.isSeelp) ? config.requestSleepTime : options.isSeelp,
    );
  }
  handleResponse();
  const res: any = await response.clone().json();
  //无权限
  if (res.error_code === '401' || res.errorCode === '401') {
    window.location.replace('/unifiedplatform/#/not_authority');
  }
  return response;
};

function generateTheme(themeColor: string) {
  // 切换主题颜色（antd）
  (window as any).less
    .modifyVars({
      '@primary-color': themeColor,
    })
    .then(() => {
      console.log(`${themeColor} 主题切换成功`);
    })
    .catch(() => console.error(`${themeColor} 主题切换失败`));
}

export async function getInitialState() {
  // 获取并设置主题色
  let res = await globalService.fetchSetting();
  if (res.errorCode === config.successCodeUpform) {
    generateTheme(res.extendMessage.themeColor);
    return res.extendMessage;
  }
  return null;
}

export const request: RequestConfig = {
  timeout: 12000,
  errorConfig: {
    adaptor: (resData) => {
      return {
        ...resData,
        success: resData.ok,
        errorMessage: resData.error?.title,
      };
    },
  },
  middlewares: [],
  requestInterceptors: [requestInterceptors],
  responseInterceptors: [responseInterceptors],
};

// 注入styleProvider 提供低版本浏览器支持
export function rootContainer(container: any) {
  return React.createElement(
    StyleProvider,
    {
      hashPriority: 'high',
      transformers: [legacyLogicalPropertiesTransformer],
    },
    container,
  );
}
