/**
 * @description 取当前页面的根路径,打开新页面
 * @param rootPathname
 * @param pathname
 */

export function openNewPage(
  rootPathname: string,
  pathname: string,
  query?: Record<string, any>,
) {
  const url = `${location.origin}${rootPathname}/#${pathname}${
    query ? objToQueryString(query) : ''
  }`;
  window.open(url);
}

/**************************脚本文件异步加载***********************************/
export const getScriptDomFromUrl = (url: string) => {
  let dom;
  if (/.+\.js$/.test(url)) {
    dom = document.createElement('SCRIPT');
    dom.setAttribute('type', 'text/javascript');
    dom.setAttribute('src', url);
  } // css
  else {
    dom = document.createElement('link');
    dom.href = url;
    dom.type = 'text/css';
    dom.rel = 'stylesheet';
  }
  return dom;
};
const asyncLoadedScripts: any = {};
const asyncLoadedScriptsCallbackQueue: any = {};
/**
 * 异步加载script
 * @param url
 * @param callback
 */
export const asyncLoadScript = (
  url: string,
  callback?: (() => void) | undefined,
): Promise<void> => {
  return new Promise(resolve => {
    if (asyncLoadedScripts[url] !== undefined) {
      // 已加载script标签
      if (callback && typeof callback === 'function') {
        if (asyncLoadedScripts[url] === 0) {
          // 未执行首个script标签的回调
          if (!asyncLoadedScriptsCallbackQueue[url]) {
            asyncLoadedScriptsCallbackQueue[url] = [];
          }
          asyncLoadedScriptsCallbackQueue[url].push(callback);
        } else {
          callback.apply(window, []);
        }
      }
      resolve();
      return;
    }
    asyncLoadedScripts[url] = 0;
    const scriptDom: any = getScriptDomFromUrl(url);
    if (scriptDom.readyState) {
      scriptDom.onreadystatechange = () => {
        if (
          scriptDom.readyState === 'loaded' ||
          scriptDom.readyState === 'complete'
        ) {
          scriptDom.onreadystatechange = null;
          asyncLoadedScripts[url] = 1;
          resolve();
          if (callback && typeof callback === 'function') {
            callback.apply(window, []);
          }
          if (asyncLoadedScriptsCallbackQueue[url]) {
            for (
              let i = 0, j = asyncLoadedScriptsCallbackQueue[url].length;
              i < j;
              i++
            ) {
              asyncLoadedScriptsCallbackQueue[url][i].apply(window, []);
            }
            asyncLoadedScriptsCallbackQueue[url] = undefined;
          }
        }
      };
    } else {
      scriptDom.onload = () => {
        asyncLoadedScripts[url] = 1;
        resolve();
        if (callback && typeof callback === 'function') {
          callback.apply(window, []);
        }
        if (asyncLoadedScriptsCallbackQueue[url]) {
          for (
            let i = 0, j = asyncLoadedScriptsCallbackQueue[url].length;
            i < j;
            i++
          ) {
            asyncLoadedScriptsCallbackQueue[url][i].apply(window, []);
          }
          asyncLoadedScriptsCallbackQueue[url] = undefined;
        }
      };
    }
    document.getElementsByTagName('head')[0].appendChild(scriptDom);
  });
};
/**
 * @description: 对象转query string
 * @param o Object
 * @param  needMask 是否需要query问号
 */
export const objToQueryString = (o: Record<string, any>, needMask = true) => {
  return `${needMask ? '?' : ''}${Object.entries(o)
    .reduce(
      (searchParams, [name, value]) => (
        searchParams.append(name, String(value)), searchParams
      ),
      new URLSearchParams(),
    )
    .toString()}`;
};

/** @description: 生成样式 */
export function ClassNames(...rest: string[]) {
  return rest.join(' ');
}

/** @description: 生成柔和的随机颜色 */
export function randomColor() {
  return `hsl(${Math.random() * 360}, 100%, 75%)`;
}

/** @description 数字转汉字周 */
export function numToWeek(num: number) {
  return ['一', '二', '三', '四', '五', '六', '日'][num - 1];
}
/**@description 蒋两个数字化为没有公约数的形式并返回 */
export function getNoCommonDivisor(num1: number, num2: number) {
  let [a, b] = [num1, num2];
  while (b !== 0) {
    [a, b] = [b, a % b];
  }
  return [num1 / a, num2 / a];
}

/** @description 获取文件后缀*/
export function urlSuffix(url: string) {
  return url.split('.').pop()?.toLowerCase() ?? '';
}

/**
 * @param keys 取key
 * @description 取对象中的某些key
 */
export function pick<T extends Record<string, any>, K extends keyof T>(
  obj: T,
  keys: K[],
) {
  return Object.fromEntries(
    Object.entries(obj).filter(([key]) => keys.includes(key as K)),
  ) as Pick<T, K>;
}

/**
 * @description 对象字段去undefined null
 */
export const removeEmpty = (obj: any) => {
  Object.keys(obj).forEach((key) => {
    if (obj[key] === undefined || obj[key] === null) {
      delete obj[key];
    }
  });
  return obj;
};

/**
 * @description: 监听条件是否满足,满足执行回调
 * @param  condition 条件
 * @param  callback 回调
 * @param  time 间隔时间
 */
export async function ensure(
  condition: () => boolean,
  callback?: Function,
  time = 400,
) {
  if (condition()) {
    callback?.();
  } else {
    await sleep(time);
    ensure(condition, callback, time);
  }
}

/**
 * @param time 延迟时间
 * @description 睡一会~
 */
export const sleep = (time: number) => {
  return new Promise((res, rej) => {
    setTimeout(() => {
      res(true);
    }, time);
  });
};

/**
 *  @description file转base64
 *  @param file 文件
 */
export function fileToBase64(
  file?: File,
): Promise<string | ArrayBuffer | null> {
  return new Promise((resolve, reject) => {
    if (!file) return resolve(null);
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => {
      resolve(reader.result);
    };
    reader.onerror = (error) => {
      reject(error);
    };
  });
}

/**
 * @param fn
 * @param delay
 * @description 防抖
 */
export const debounce = (fn: Function, delay: number) => {
  let timer: any = null;
  return (...rest: any) => {
    clearTimeout(timer);
    timer = setTimeout(() => {
      fn(...rest);
    }, delay);
  };
};

/**
 * @param keys 排除的key
 * @description 排除对象中的某些key
 */
export function omit<T extends Record<string, any>, K extends keyof T>(
  obj: T,
  keys: K[],
) {
  return Object.fromEntries(
    Object.entries(obj).filter(([key]) => !keys.includes(key as K)),
  ) as Omit<T, K>;
}

/**
 * @description: 数组转对象
 */
export function arrayToObject<T extends any>(array: T[]) {
  return Object.fromEntries(array.map((item) => [item, item]));
}

export function deepCopy(obj: any) {
  return JSON.parse(JSON.stringify(obj));
}

/**
 * @description 将数组元素从index开始所有元素向后移动x位
 * @example moveArrayElement([1,2,3,4,5],1,2) => [1,empty,empty,2,3,4,5]
 */
export function moveArrayElement<T>(arr: T[], index: number, x: number) {
  const element = arr.splice(index, arr.length - index);
  // arr.splice(index+x, 0, ...element);
  for (let i = 0; i < element.length; i++) {
    arr[i + index + x] = element[i];
  }
  return arr;
}

// 当splice超出数组长度添加不了元素的问题.
export function spliceAdd<T>(arr: T[], index: number, ...rest: T[]) {
  if (index === arr.length - 1) {
    arr.push(...rest);
  } else {
    arr.splice(index, 0, ...rest);
  }
  return arr;
}

export const isDevelopment = process.env.NODE_ENV === 'development';

/** 清除数组中的空值 */
export function clearAryEmpty<T extends any>(arr: T[]): NonNullable<T>[] {
  return arr.filter((v) => !isNul(v)) as NonNullable<T>[];
}

/** 判断是否为空值 */
export function isNul<T extends any>(v: T) {
  return v === null || v === undefined;
}
/** 从桶返回的url一般不带头,拼接 */
export function getImgUrlFromBucket(url?: string) {
  if (!url) {
    return url;
  }
  const head = isDevelopment ? 'http://**************' : location.origin;
  return head + url;
}

/**
 *@description 加载远端js-sdk
 * @param url 远端js脚本地址
 * @param id 创建script标签的id
 */
export async function remoteJsToScript(
  url: string,
  id: string = 'fetch-script',
) {
  const old = document.getElementById(id);
  // 如果已经加载过了，就不再加载
  if (old?.getAttribute('data-url') === url) {
    return;
  }
  // 保持id唯一性
  if (old) {
    document.body.removeChild(old);
  }
  const script = document.createElement('script');
  script.id = id;
  script.setAttribute('data-url', url);
  await new Promise((resolve) => {
    document.head.appendChild(script);
    script.src = url;
    script.onload = resolve;
  });
}
