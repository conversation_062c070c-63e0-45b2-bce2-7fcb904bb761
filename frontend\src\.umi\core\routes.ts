// @ts-nocheck
import React from 'react';
import { ApplyPluginsType, dynamic } from 'D:/公司项目/云上川大/livemange/frontend/node_modules/@umijs/runtime';
import * as umiExports from './umiExports';
import { plugin } from './plugin';
import LoadingComponent from '@/Loading';

export function getRoutes() {
  const routes = [
  {
    "path": "/mg",
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'layouts__mgLayout' */'@/layouts/mgLayout'), loading: LoadingComponent}),
    "routes": [
      {
        "path": "/mg/course",
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__management__course' */'@/pages/management/course'), loading: LoadingComponent}),
        "exact": true
      },
      {
        "path": "/mg/course/task",
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__management__course__taskList' */'@/pages/management/course/taskList'), loading: LoadingComponent}),
        "exact": true
      },
      {
        "path": "/mg/activity",
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__management__activity' */'@/pages/management/activity'), loading: LoadingComponent}),
        "exact": true
      },
      {
        "path": "/mg/activity/flow",
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__management__activity__flow' */'@/pages/management/activity/flow'), loading: LoadingComponent}),
        "exact": true
      },
      {
        "path": "/mg/activity/createlive",
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__management__activity__createLive' */'@/pages/management/activity/createLive'), loading: LoadingComponent}),
        "exact": true
      },
      {
        "path": "/mg/live",
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__management__live' */'@/pages/management/live'), loading: LoadingComponent}),
        "exact": true
      }
    ]
  },
  {
    "path": "/live",
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__live__Layout' */'@/pages/live/Layout'), loading: LoadingComponent}),
    "routes": [
      {
        "path": "/live/my",
        "title": "我的直播",
        "routes": [
          {
            "path": "/live/my/apply",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__live__My__Apply' */'@/pages/live/My/Apply'), loading: LoadingComponent}),
            "title": "我申请的",
            "exact": true
          },
          {
            "path": "/live/my/speaker",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__live__My__Speaker' */'@/pages/live/My/Speaker'), loading: LoadingComponent}),
            "title": "我主讲的",
            "exact": true
          },
          {
            "path": "/live/my/collect",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__live__My__Collect' */'@/pages/live/My/Collect'), loading: LoadingComponent}),
            "title": "我收藏的",
            "exact": true
          },
          {
            "path": "/live/my/share",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__live__My__Share' */'@/pages/live/My/Share'), loading: LoadingComponent}),
            "title": "我分享的",
            "exact": true
          }
        ]
      },
      {
        "path": "/live/share",
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__live__Share' */'@/pages/live/Share'), loading: LoadingComponent}),
        "title": "共享直播",
        "exact": true
      },
      {
        "path": "/live/all",
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__live__All' */'@/pages/live/All'), loading: LoadingComponent}),
        "title": "全部直播",
        "exact": true
      },
      {
        "path": "/live/create",
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__live__Create' */'@/pages/live/Create'), loading: LoadingComponent}),
        "exact": true
      },
      {
        "path": "/live/control",
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__live__Control' */'@/pages/live/Control'), loading: LoadingComponent}),
        "exact": true
      }
    ]
  },
  {
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__404' */'@/pages/404'), loading: LoadingComponent}),
    "title": "404",
    "exact": true
  }
];

  // allow user to extend routes
  plugin.applyPlugins({
    key: 'patchRoutes',
    type: ApplyPluginsType.event,
    args: { routes },
  });

  return routes;
}
