/*
 * @Author: spring breeze
 * @Date: 2023-07-07 16:22:30
 * @LastEditTime: 2023-07-07 16:22:30
 * @FilePath: \frontend\src\Loading.tsx
 * @Description:
 */

import style from '@/assets/loading.less';
import { LoadingOutlined } from '@ant-design/icons';
import { Spin } from 'antd';
import React from 'react';

const Loading: React.FC<LoadingProps> = () => {
  return (
    <div className={style.loading}>
      <Spin
        className={style.spin}
        indicator={<LoadingOutlined style={{ fontSize: 48 }} spin />}
        tip="加载中"
        size="large"
      ></Spin>
    </div>
  );
};

interface LoadingProps {}
export default Loading;
Loading.displayName = 'Loading';
