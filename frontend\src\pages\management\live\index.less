.gap {
  gap: 20px;
  display: flex;
}
.item {
  background-color: #fff;
  border-radius: 10px;
  transition: all 0.3s ease-in;
  padding: 20px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  &:hover {
    box-shadow: 0 10px 20px rgba(168, 182, 191, 0.6);
    transform: translateY(-1px);
  }
  > .title {
    font-size: 20px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #181818;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 5px;
    > img {
      width: 28px;
      height: 28px;
    }
  }
  .line {
    border-bottom: 1px solid #f1f1f1;
  }
}
.center {
  display: flex;
  justify-content: center;
  align-items: center;
}
.test {
  border: 1px solid black;
}
.monitoring {
  height: 100%;
  height: 100%;
  flex: 80%;
  .gap();
  flex-direction: column;
  header {
    cursor: pointer;
    user-select: none;
    display: flex;
    align-items: center;
    font-size: 24px;
    h1 {
      margin-bottom: 0 !important;
      font-size: 24px;
    }
  }
  > * {
    width: 100%;
    // 不会被撑开
    // flex-grow: 0 !important;
  }
  .info {
    // flex: 1 0 70%;
    flex: 1;
    max-height: 70%;

    .gap();

    .media {
      flex: 32.5%;
      .gap();
      height: 100%;
      flex-direction: column;
      .item();
      display: flex;
      flex-direction: column;
      gap: 10px;
      .infoLine {
        font-size: 16px;
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        > * {
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
        }
      }
    }
    .video {
      height: 100%;

      flex: 67.5%;
      z-index: 0;
      .item();
      .videoContent {
        border-radius: 10px;
      }
    }
  }
  .chart {
    flex: 3 0 30%;
    max-height: 30%;
    .item();
    display: flex;
    flex-direction: column;
    padding-bottom: 0;
    width: 100%;
    .chartContent {
      flex: 1;
      width: 100%;
    }
  }
}
