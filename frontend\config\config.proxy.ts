export const PROXY_URL = 'https://**************';
// export const PROXY_URL = 'http://**************';

const proxy = {
  '/api': {
    // target: 'http://ecloud.scu.sobeylingyun.com',
    target: PROXY_URL,
    // target: 'http://localhost:8080',
    changeOrigin: true,
    secure: false,
    pathRewrite: { '^/api': '/' },
  },
  // 代理配置
  '/rman': {
    target: PROXY_URL,
    changeOrigin: true,
    secure: false, // 支持https代理
  },
  '/livemanage/v1': {
    // target: 'http://*************:8020',
    target: PROXY_URL,
    changeOrigin: true,
    secure: false, // 支持https代理
  },
  '/unifiedplatform': {
    target: PROXY_URL,
    changeOrigin: true,
    secure: false, // 支持https代理
    // pathRewrite: { '^/unifiedplatform': '' },
  },
  '/ipingestman': {
    // target: 'http://*************:8000',
    target: PROXY_URL,
    changeOrigin: true,
    secure: false, // 支持https代理
  },
  '/somediaserver': {
    // target: 'http://*************:8000',
    target: PROXY_URL,
    changeOrigin: true,
    secure: false, // 支持https代理
  },
  /**
   * 课表服务
   */
  '/curr': {
    // target: 'http://*************:8001',
    target: PROXY_URL,
    changeOrigin: true,
    secure: false, // 支持https代理
  },
  '/learn': {
    target: PROXY_URL,
    changeOrigin: true,
    secure: false, // 支持https代理
  },
  '/unifiedlogin': {
    target: PROXY_URL,
    changeOrigin: true,
    secure: false, // 支持https代理
  },
  '/bucket-k': {
    target: PROXY_URL,
    changeOrigin: true,
    secure: false, // 支持https代理
  },
};
export default proxy;
