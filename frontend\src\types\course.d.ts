declare namespace CourseType {
    interface Idelete {
        _ids: string[]
    }
    interface IVideoTranslation {
        action: string,//on||off
        _ids: string[]
    }
    interface IstopCourse {
        _id: string
    }
    interface IScheduleData {
        add_time: number
        area: number
        area_name: string
        course_id: string
        course_metadata: boolean
        course_name: string
        course_no: string
        course_weekly: string
        course_what: string
        dynamic_start_time: number
        each_live_duration: number
        is_loop: boolean
        live_now: boolean
        origin_finish_time: number
        origin_start_time: number
        sc_curr_id: string
        schedule_status: number
        schedule_type: number
        sync_time: number
        task_interval: number
        teacher_id: string
        teacher_name: string
        update_time: number
        voice_identify?: boolean
        voice_translation?: boolean
        is_voice_identify?: boolean
        is_voice_translation?: boolean
        is_voice_tts?: boolean
        week: number
        _id: string
    }
    interface ICourseMetadata {
        _id: string,
        area: number,
        area_name: string,
        college: string,
        college_name: string,
        course: string,
        course_id: string,
        course_name: string,
        course_no: string,
        course_section: string,
        create_time: number,
        end_hour: number,
        end_minute: number,
        end_time: number,
        end_week: number,
        is_delete: boolean,
        major: string,
        major_name: string,
        site_info: number,
        start_hour: number,
        start_minute: number,
        start_time: number,
        start_week: number,
        teacher: string,
        teacher_name: string,
        times: string,
        update_time: number,
        week: number
    }
}
