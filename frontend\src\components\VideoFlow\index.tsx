import { FC, useEffect, useState } from 'react';
import { Form, Button, Select, Checkbox, Input, Row, Col } from 'antd';
import { MinusCircleOutlined, PlusCircleOutlined } from '@ant-design/icons';
import './index.less';

interface IVideoFlowProps {
  maxLength: number;
  defaultLength: number;
  onAddressesChange: (
    addressed: (ActivityType.IFlowOrigin | undefined)[],
  ) => void;
  customFlows: ActivityType.IFlow[]; // 获取的直播流列表
  customList: (ActivityType.IFlowOrigin | undefined)[]; // 自己设置的活动直播列表

  onUpdateCustomFlows: (keyword: string, paging: boolean) => void;
}

const VideoFlow: FC<IVideoFlowProps> = ({
  maxLength,
  customFlows,
  onAddressesChange,
  customList,
  onUpdateCustomFlows
}) => {
  const handleSelectChange = (id: string, index: number) => {
    const addresses:any = [...customList];
    addresses[index] = customFlows
      .filter((item) => id === item._id)
      .map((item) => ({
        _id: item._id,
        stream_address: item.stream_address,
        push_url: item.push_url,
        pull_url: item.pull_url,
        stream_name: item.stream_name,
        is_mute: item.is_mute,
      }))[0];
    onAddressesChange(addresses);
  };
  const handleCheckChange = (e: any, index: number) => {
    const addresses = [...customList];
    if (addresses && addresses[index]) {
      addresses[index].is_select = e.target.checked;
      onAddressesChange(addresses);
    }
  };
  const handleInputChange = (value: string, index: number) => {
    const addresses = [...customList];
    if (addresses && addresses[index]) {
      addresses[index]!.stream_name = value;
      onAddressesChange(addresses);
    }
  };
  const handleAddItem = () => {
    onAddressesChange(customList.concat([undefined]));
  };

  const handleDeleteItem = (index: number) => {
    const addresses = [...customList];
    addresses.splice(index, 1);
    onAddressesChange(addresses);
  };

  const flowScroll = (e: any) => {
    e.persist();
    const { target } = e;
    if (target.scrollTop + target.offsetHeight === target.scrollHeight) {
      //下一页
      onUpdateCustomFlows('', true)
    }
  }

  useEffect(() => {
    onAddressesChange(customList);
  }, [customList]);
  return (
    <div className="activity_flowlist_container">
      <Form name="address">
        {customList.map((item, index) => (
          <div key={index} className="activity_flowItem_container">
            <div key={index} className="activity_flowitem_wrapper">
              <Checkbox style={{ height: '32px', lineHeight: '32px', marginRight: 5 }} checked={item?.is_select} onChange={(e: any) => { handleCheckChange(e, index) }} />
              <Form.Item
                label={`视频流-${index + 1}`}
                name={'flow'}
                labelCol={{ span: 3 }}
                rules={[{ required: !index, message: '请输入视频流地址' }]}
                className="activity_flow_form_item"
                help={
                  index === 0
                    ? '注：直播将默认播放“视频流-1”的声音，直播中可切换至任意视频流的声音'
                    : undefined
                }
              >
                <Input
                  disabled={!item || !item._id}
                  placeholder="流名称"
                  value={item?.stream_name}
                  onChange={(e) => handleInputChange(e.target.value, index)}
                  style={{ width: '18%', marginRight: '2%' }}
                ></Input>
                <Select
                  showSearch
                  placeholder="请选择视频流"
                  onSearch={(value: string) => onUpdateCustomFlows(value, false)}
                  onFocus={() => onUpdateCustomFlows('', false)}
                  onChange={(value: string) => handleSelectChange(value, index)}
                  onPopupScroll={(e: any) => flowScroll(e)}
                  value={item?._id}
                  style={{ width: '80%' }}
                  filterOption={false}
                >
                  {customFlows?.map((item: ActivityType.IFlow) => (
                    <Select.Option key={item._id} value={item._id}>
                      {item.stream_name}
                    </Select.Option>
                  ))}
                </Select>
                {item &&
                  <div className="activity_flowitem_info">
                    <div>{`直播流地址${item?.is_mute ? '(有声)' : '(无声)'}：${item?.stream_address}`}</div>
                    <div>{`推流地址为${item?.is_mute ? '(有声)' : '(无声)'}：${item?.push_url}`}</div>
                    <div>{`拉流地址为${item?.is_mute ? '(有声)' : '(无声)'}：${item?.pull_url}`}</div>
                  </div>
                }
              </Form.Item>
              {customList.length > 1 && (
                <MinusCircleOutlined
                  className="activity_icon activity_subtraction"
                  onClick={() => handleDeleteItem(index)}
                />
              )}
              {customList.length <= 1 && (
                <PlusCircleOutlined
                  className="activity_icon"
                  onClick={handleAddItem}
                />
              )}
            </div>
            {/* {item &&
              <div className="activity_flowitem_info">
                <div>{`直播流地址${item?.is_mute ? '(有声)' : '(无声)'}：${item?.stream_address}`}</div>
                <div>{`推流地址为${item?.is_mute ? '(有声)' : '(无声)'}：${item?.push_url}`}</div>
              </div>
            } */}
          </div>
        ))}
      </Form>
      {customList.length < maxLength && (
        <Row>
          <Col span={3}></Col>
          <Button
            ghost
            type="primary"
            onClick={handleAddItem}
            style={{ width: '100%' }}
            icon={<PlusCircleOutlined />}
          >
            添加直播流
          </Button>
        </Row>
      )}
    </div>
  );
};

export default VideoFlow;
