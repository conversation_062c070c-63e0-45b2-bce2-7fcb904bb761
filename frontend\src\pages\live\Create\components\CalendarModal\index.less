.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 50px;
  background: linear-gradient(86deg, #549cff 0%, #719eff 100%);
  border-radius: 10px;
  color: white;
  margin-bottom: 10px;
  padding: 0 10px;
  span {
    color: white;
  }
  .create {
    display: flex;
    flex-direction: column;
    font-size: 12px;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    span {
      font-size: 24px;
    }
  }
  .title {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 24px;
    user-select: none;
    p {
      font-size: 18px;
    }
    div[role='button'] {
      cursor: pointer;
      user-select: none;
    }
  }
}

.table {
  width: 100%;
  @border: 1px solid #ededed;
  .item {
    border-radius: 4px;
    border: @border;
    display: flex;
    flex-direction: column;
    flex: 1;
    justify-content: space-around;
    align-items: center;
  }
  tr {
    margin-bottom: 6px;
    display: flex;
    width: 100%;
    gap: 6px;
  }
  thead {
    @height: 60px;
    height: @height;
    tr {
      height: @height;

      th {
        // 边框
        .item();
        background: rgba(84, 156, 255, 0.1);
        span:first-child {
          font-size: 16px;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 500;
          color: #559cff;
          line-height: 22px;
        }
        span:last-child {
          color: #9d9d9d;
          line-height: 22px;
        }
        &:hover {
          background: rgba(84, 156, 255, 0.2);
        }
      }
    }
  }
  tbody {
    tr {
      td {
        .item();
        height: 100px;
        background-color: white;
        overflow: hidden;
        display: flex;
        flex-direction: column;
        gap: 3px;
        justify-content: center;
        padding: 0 10px;
        &.repeat {
          position: relative;
          border: #549cff 1px solid;
          color: #549cff;
          &::after {
            content: '重复';
            position: absolute;
            right: 0;
            top: 0;
            background: #ff6a6a;
            border-radius: 0px 4px 0px 4px;
            color: white;
            font-size: 12px;
            padding: 2px 5px;
          }
        }

        span {
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          width: 100%;
          text-align: center;
          font-size: 12px;
        }
        &.alive:hover {
          background-color: rgb(245, 245, 245);
          border: #549cff 1px solid;
          color: #549cff;
        }
        &.select {
          background-color: rgb(245, 245, 245);
          &:hover {
            background-color: rgb(245, 245, 245);
          }
        }
      }
    }
  }
}
