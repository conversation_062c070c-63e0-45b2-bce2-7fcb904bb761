/*
 * @Author: spring breeze
 * @Date: 2023-07-06 10:24:49
 * @LastEditTime: 2023-07-06 10:24:56
 * @FilePath: \frontend\src\pages\live\components\LegendMode\index.tsx
 * @Description:
 */
import React from 'react';
import Card from '../Card';
import style from './index.less';
import { Empty, Pagination, Spin, TablePaginationConfig } from 'antd';
import { LiveService } from '@/api/live';
import Access from '@/components/Access';

const LegendMode: <T = LiveService.MyLiveData>(
  props: LegendModeProps<T>,
) => React.ReactElement = ({
  dataSource,
  loading,
  pagination,
  cardButtonsRender,
  cardRender,
}) => {
  return (
    <div className={style.legend}>
      <Spin spinning={loading}>
        <div className={style.content}>
          {dataSource?.map((item, index) => {
            //! 不同页面card不同
            if (cardRender) {
              return cardRender(item, index);
            }
            const val = item as LiveService.MyLiveData;
            return (
              <Card
                item={val}
                buttonsRender={
                  cardButtonsRender
                    ? item => {
                        return cardButtonsRender?.(val as any);
                      }
                    : undefined
                }
                key={val._id}
              />
            );
          })}
          {/* 计算dataSource的个数除3,然后补齐 */}
          {(dataSource?.length ?? 0) % 3 !== 0 &&
            new Array(3 - (dataSource?.length % 3))
              .fill(0)
              .map((item, index) => {
                return <div key={index} className={style.emptyCard}></div>;
              })}
        </div>
        <Access accessible={dataSource?.length === 0}>
          <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
        </Access>
        <footer>
          <Pagination {...pagination} size="small" />
        </footer>
      </Spin>
    </div>
  );
};

interface LegendModeProps<T> {
  dataSource: T[];
  loading?: boolean;
  pagination?: false | TablePaginationConfig;
  cardButtonsRender?: (item?: T) => React.ReactNode[];
  cardRender?: (item: T, index: number) => React.ReactNode;
}
export default LegendMode;

export interface CardProps<T> {
  buttonsRender?: (item?: T) => React.ReactNode[];
  item?: T;
}

/** 表格模式 */
export enum TABLE_MODE {
  /** 列表模式 */
  LIST = 'list',
  /** 图标模式 */
  CARD = 'card',
}
