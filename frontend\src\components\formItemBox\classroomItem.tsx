import React, { FC, useState, useEffect } from 'react'
import { Form, TreeSelect, Cascader } from 'antd'
import courseManagementService from '@/service/courseManagementService'
// import { IreturnTree } from '../../types/treeTypes'
interface IBasicItemProps {
  required: boolean
  form: any
}
const ClassroomItem: FC<IBasicItemProps> = (props) => {
  const { required, form } = props
  const [selectValue, setSelectValue] = useState<string>('')
  const [treeData, setTreeData] = useState<any>([])
  useEffect(() => {
    gettree()
  }, [])
  const onChange = (value: any) => {
    // setSelectValue(value)
    if (value[value.length - 1].split(',')[1] !== 'undefined') {
      form.setFieldsValue({ classroom: value })
    } else {
      form.setFieldsValue({ classroom: [] })
    }
  }
  const gettree = () => {
    courseManagementService.searchtree().then((res) => {
      if (res.error_msg === 'Success' && res.extend_message.length) {
        setTreeData(forTree(res.extend_message))
      }
    })
  }
  const forTree = (tree: any): any => {
    return tree.map((item: any) => {
      return {
        // key: item.id.toString() + ',' + item.area_fullname,
        // title: item.name,
        // type: item.type,
        // disabled: item.type !== '教室',
        // area_fullname: item.area_fullname,
        // children: item.children ? forTree(item.children) : [],
        value: item.id.toString() + ',' + item.area_fullname,
        label: item.name,
        children: item.children ? forTree(item.children) : null,
      }
    })
  }
  return (
    <Form.Item
      label="教室"
      name="classroom"
      rules={[{ required: required, message: '请选择教室' }]}
    >
      {/* <TreeSelect
        allowClear
        style={{ width: 250 }}
        value={selectValue}
        dropdownStyle={{ height: 300, overflow: 'auto' }}
        treeData={treeData}
        placeholder="请选择教室"
        onChange={onChange}
      /> */}
      <Cascader
        options={treeData}
        placeholder="请选择教室"
        onChange={onChange}
      />
    </Form.Item>
  )
}

export default ClassroomItem
