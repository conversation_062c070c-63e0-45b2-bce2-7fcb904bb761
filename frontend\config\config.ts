import { defineConfig } from 'umi';
import routes from './config.routes';
import proxy from './config.proxy';

export default defineConfig({
  nodeModulesTransform: {
    // node_modules 编译模式
    type: 'none',
    exclude: [],
  },
  publicPath: '/livemanage/',
  history: {
    // 路由模式
    type: 'hash',
  },
  routes: routes, // 路由文件
  locale: {
    // 国际化配置
    default: 'zh-CN',
    antd: true,
    title: true,
  },
  antd: {},
  // mfsu: {},
  dva: {
    // dva配置
    immer: true,
    hmr: false,
  },
  proxy: proxy,
  hash: true, // 打包文件生成hash
  targets: {
    ie: 10,
  },
  theme: {
    // 主题颜色
    // '@primary-color': '#208bfa',
    '@white-color': '#FFF',
  },
  dynamicImport: {
    loading: '@/Loading',
  },
  lessLoader: {
    javascriptEnabled: true,

    lessOptions: {
      // This line
      javascriptEnabled: true,
    },
  },
});
