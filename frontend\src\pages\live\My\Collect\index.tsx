/*
 * @Author: 冉志诚
 * @Date: 2023-07-10 10:44:26
 * @LastEditTime: 2023-08-14 10:56:39
 * @FilePath: \frontend\src\pages\live\My\Share\index.tsx
 * @Description:
 */

import React, { useEffect, useMemo, useRef, useState } from 'react';
import style from './index.less';
import {
  Button,
  Dropdown,
  Modal,
  Popconfirm,
  Popover,
  Radio,
  Select,
  Space,
  Tabs,
  TabsProps,
  Tooltip,
  message,
} from 'antd';
// 导入tab样式
import 'antd/lib/tabs/style/index.css';
import { Tab } from 'rc-tabs/lib/interface';
import {
  ActionType,
  ProColumns,
  ProTable,
  ProTableProps,
} from '@ant-design/pro-components';
import { useImmer } from 'use-immer';
import { useQuery, useQueryPagination } from '@/hooks';
import IconFont from '@/components/iconFont/iconFont';
import { ArrowDownOutlined, ArrowUpOutlined } from '@ant-design/icons';
import LegendMode, { TABLE_MODE } from '../../components/LegendMode';
import { history } from 'umi';
import { LiveService } from '@/api/live';
import moment from 'moment';
import Access from '@/components/Access';
import { isDevelopment, openNewPage } from '@/utils/utils';
import test from './test.json';
import './reset.less';
const items: Tab[] = [
  {
    key: '1',
    label: '活动直播',
  },
];
type Item = Partial<LiveService.MyLiveData>;
type Params = {
  current?: number;
  pageSize?: number;
  order_by?: string;
} & Item;
type ConfigTypes = {
  /** 列表或图例模式 */
  mode: TABLE_MODE;
};

const MyShare: React.FC<AppProps> = () => {
  const tabsConfig: TabsProps = {
    items,
  };
  const columns: ProColumns<Item>[] = [
    {
      title: '直播名称',
      fieldProps: {
        placeholder: '请输入直播名称',
      },
      formItemProps: {
        label: null,
      },
      dataIndex: 'live_name',
      width: 150,
      ellipsis: true,
    },

    {
      title: '直播状态',
      fieldProps: {
        placeholder: '请输入直播状态',
        showSearch: true,
      },
      formItemProps: {
        label: null,
      },
      dataIndex: 'schedule_status',

      valueEnum: {
        1: {
          text: '未开始',
          status: 'Processing',
        },
        2: {
          text: '直播中',
          status: 'Success',
        },
        3: {
          text: '已结束',
          status: 'Error',
        },
        4: {
          text: '已关闭',
          status: 'Error',
        },
        5: {
          text: '异常',
          status: 'Error',
        },
      },
    },
    {
      title: '听众对象',
      search: false,
      dataIndex: 'look_permission',
      valueEnum: {
        public: '社会公开',
        school: '校内公开',
        password: '需要密码加入',
        school_custom: '校内自定义范围',
      },
    },

    {
      title: '开始时间',
      search: false,
      dataIndex: 'origin_start_time',
      renderText(text, record, index, action) {
        if (!text) {
          return;
        }
        return moment(text * 1000).format('YYYY-MM-DD HH:mm:ss');
      },
    },
    {
      title: '结束时间',
      search: false,
      dataIndex: 'origin_finish_time',
      renderText(text, record, index, action) {
        if (!text) {
          return;
        }
        return moment(text * 1000).format('YYYY-MM-DD HH:mm:ss');
      },
    },
    {
      title: '操作',
      valueType: 'option',
      width: 150,
      fixed: 'right',
      render: (_, record, index, action) => {
        const {
          _id,
          look_back,
          look_back_status,
          schedule_status,
          apply_status,
          is_series,
        } = record;

        const deleteBtnDisabled = schedule_status === 2;
        const disabledView = schedule_status !== 2 && schedule_status !== 3;
        return (
          <div key="operation" className={style.operation}>
            <Button
              type="link"
              disabled={disabledView}
              onClick={() => {
                window.open(`${location.origin}/learn/live/activity/${_id}`);
              }}
            >
              查看直播
            </Button>
            <Button
              type="link"
              onClick={async () => {
                setLoading(true);

                setLoading(false);
              }}
            >
              取消收藏
            </Button>
          </div>
        );
      },
    },
  ];
  const [config, setConfig] = useImmer<ConfigTypes>({
    mode: TABLE_MODE.LIST,
  });
  const [params, setParams] = useImmer<Params>({
    order_by: '-add_time',
  });
  const actionRef = useRef<ActionType>();
  const { pagination } = useQueryPagination({
    pageSize: 9,
  });

  const Condition = () => {
    const { mode } = config;
    return (
      <div className={style.condition}>
        <Select
          className={style.item}
          key="createTime"
          value={params.order_by}
          onChange={(v) => {
            setParams((d) => {
              d.order_by = v;
            });
          }}
          options={[
            {
              label: (
                <>
                  开始时间
                  <ArrowUpOutlined />
                </>
              ),
              value: 'start_time',
            },
            {
              label: (
                <>
                  开始时间
                  <ArrowDownOutlined />
                </>
              ),
              value: '-start_time',
            },
            {
              label: (
                <>
                  创建时间
                  <ArrowUpOutlined />
                </>
              ),
              value: 'add_time',
            },
            {
              label: (
                <>
                  创建时间
                  <ArrowDownOutlined />
                </>
              ),
              value: '-add_time',
            },
          ]}
        ></Select>

        <div
          className={mode === TABLE_MODE.CARD ? style.active : ''}
          onClick={() => {
            setConfig((d) => {
              d.mode = TABLE_MODE.CARD;
            });
          }}
        >
          <Tooltip title="图例模式">
            <IconFont type="iconhebingxingzhuangfuzhi2" />
          </Tooltip>
        </div>
        <div
          className={mode === TABLE_MODE.LIST ? style.active : ''}
          onClick={() => {
            setConfig((d) => {
              d.mode = TABLE_MODE.LIST;
            });
          }}
        >
          <Tooltip title="列表模式">
            <IconFont type="iconliebiao" />
          </Tooltip>
        </div>
      </div>
    );
  };

  const [loading, setLoading] = useState(false);
  const tableConfig: ProTableProps<Item, Params> = {
    rowKey: '_id',
    toolBarRender: false,
    search: {
      optionRender(searchConfig, props, dom) {
        return [
          <div
            style={{
              display: 'flex',
              gap: 10,
            }}
            key="defaultDom"
          >
            {dom}
          </div>,
          <Condition key="condition" />,
        ];
      },
      span: 4,
      showHiddenNum: true,
      labelWidth: 'auto',
      className: 'in-pro-search',
      searchText: '搜索',
    },
    columns,
    pagination,
    loading,
    params,
    actionRef,
    onLoadingChange(loading) {
      setLoading(loading as boolean);
    },
    tableRender(props, defaultDom) {
      const { dataSource, loading } = (props as any).action ?? {};
      const { pagination } = props;
      return config.mode === TABLE_MODE.LIST ? (
        defaultDom
      ) : (
        <div>
          <LegendMode
            cardButtonsRender={(item) => {
              if (!item) {
                return [<></>];
              }
              const { schedule_status } = item;

              const showReplay = schedule_status === 3;

              return [
                // <Access accessible={showReplay}>
                <Button key="replay" type="link" onClick={() => {}}>
                  取消分享
                </Button>,
              ];
            }}
            pagination={pagination}
            dataSource={dataSource}
            loading={loading}
          />
        </div>
      );
    },
    async request(params, sort, filter) {
      try {
        console.log('发生请求', params);
        const {
          current,
          pageSize,
          schedule_status,
          live_name,
          order_by,
          apply_status,
        } = params;
        const {
          extend_message: { results, total },
        } = await LiveService.getMyLive({
          type_name: LiveService.MyLiveTypeName.COLLECT,
          page: current!,
          size: pageSize!,
          live_name,
          schedule_status,
          order_by,
          apply_status,
        });
        return {
          data: test,
          // success 请返回 true，
          // 不然 table 会停止解析数据，即使有数据
          success: true,
          // 不传会使用 data 的长度，如果是分页一定要传
          total,
        };
      } catch (error) {
        return {
          data: [],
          success: false,
        };
      }
    },
  };
  return (
    <div className={style.apply}>
      <Tabs {...tabsConfig}></Tabs>
      <ProTable {...tableConfig} />
    </div>
  );
};

interface AppProps {}
export default MyShare;
MyShare.displayName = 'MyApply';
