.left {
  height: 100%;
  width: 180px;
  flex: none !important;
  box-sizing: border-box;
  padding: 12px 0 37px;
  font-size: 16px;
  border-right: 1px solid #f0eeee;
  position: fixed;
  left: 70px;
  top: 52px;
  transition: all 0.3s ease;
  @media screen and (max-width: 769px) {
    overflow: hidden;
    width: 0px;
    min-width: 0px;
    margin: 0;
    padding: 0;
    padding-left: 76px;
    border-right: none;
    color: white;
  }
}

.tree {
  width: 100%;
  height: 100%;
}
