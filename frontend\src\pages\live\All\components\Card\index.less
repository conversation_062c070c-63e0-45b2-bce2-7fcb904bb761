.text-center (@height) {
  line-height: @height;
  height: @height;
  text-align: center;
}
.full {
  width: 100%;
  height: 100%;
}

.card {
  height: 130px;
  box-shadow: 0px 2px 8px 2px rgba(129, 129, 129, 0.06);
  border-radius: 6px;
  border: 1px solid #f1f1f1;
  transition: 0.3s ease all;
  display: flex;
  padding: 10px;
  gap: 14px;
  &:hover {
    box-shadow: 0px 2px 8px 2px rgba(129, 129, 129, 0.16);
    transform: translateY(-1px);
  }
  > * {
    position: relative;
  }

  .left {
    flex: 41%;
    .img {
      position: relative;
      .full();
      img {
        .full();
      }
      .look_back_rendering {
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 25%;
        text-align: center;
        display: flex;
        justify-content: center;
        align-items: center;
        background: rgba(0, 0, 0, 0.3);
        color: white;
        font-size: 12px;
      }
      .status {
        position: absolute;
        right: 8px;
        top: 8px;
        font-size: 12px;
        color: white;
        background: #7582b4;
        border-radius: 2px;
        width: 31.6%;
        min-width: 36px;
        .text-center(24px);
        &.doing {
          background: #ff5a5a;
        }
        &.done {
          background: rgba(0, 0, 0, 0.3);
        }
        &.not {
          background: rgba(255, 110, 38, 0.6);
        }
      }
    }
    .label {
      position: absolute;
      left: -10px;
      top: -10px;
      z-index: 2;
      background: #549cff;
      border-radius: 6px 2px 2px 2px;
      color: white;
      user-select: none;
      font-size: 12px;
      min-width: 54px;
      padding: 0 6px;
      .text-center(24px);
    }
  }
  .right {
    flex: 59%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    .title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      .btns {
        display: flex;
        gap: 4px;
        .btn {
          color: var(--primary-color);
          padding: 0;
          font-size: 12px;
          &:hover {
            font-weight: 500;
          }
          &:disabled {
            color: #999999;
          }
        }
        > * {
          .btn();
        }
      }
      h3 {
      }
    }
    .common {
      font-size: 12px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #9d9d9d;
      line-height: 17px;
    }
    .address,
    .time {
      .common();
    }
    .bottom {
      display: flex;
      font-size: 12px;
      justify-content: space-between;
      .bottomLeft {
        display: flex;
        gap: 8px;
      }
      .bottomRight {
        display: flex;
        gap: 3px;
        align-items: center;
        white-space: nowrap;
      }
    }
    .content {
      margin-top: -10%;
    }
  }
}

.reason {
  h2 {
    font-weight: 500;
    color: #525252;
    font-size: 14px;
    height: 20px;
  }
  span {
    font-weight: 400;
    color: #9d9d9d;
    font-weight: 12px;
  }
}
.hidden {
  visibility: hidden;
}
