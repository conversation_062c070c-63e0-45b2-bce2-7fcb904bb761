import React, { useEffect, useMemo, useState } from 'react';
import './index.less';
import {
  Popconfirm,
  Space,
  Select,
  Input,
  Button,
  Table,
  message,
  Modal,
  Form,
  DatePicker,
  Tag,
} from 'antd';
import ActivityConstant from '@/constant/activity';
import activityManagementService from '@/service/activityManagementService';
import config from '@/utils/config';
import { QuestionCircleFilled } from '@ant-design/icons';
import { formatDate } from '@/utils';
import { Link, history } from 'umi';
import moment from 'moment';
import { IconFont } from '@/components';
import QRCode from 'qrcode.react';
import copy from 'copy-to-clipboard';
import Clipboard from 'clipboard';
import html2canvas from 'html2canvas';
import b64toBlob from 'b64-to-blob';
import TaskDrawer from './taskdetail';
const Option = Select.Option;
// const QRCode = require('qrcode.react');
const ActivityList = () => {
  const [searchInfo, setSearchInfo] = useState<ActivityType.ILiveSearchParam>({
    page: 1,
    size: config.defaultPageSize,
    schedule_status: ActivityConstant.liveStatus.all,
  });
  const [deleteIds, setDeleteIds] = useState<React.Key[]>([]);
  const [selectedRows, setSelectedRows] = useState<ActivityType.ILive[]>([]);
  const [LiveList, setLiveList] = useState<ActivityType.ILive[]>([]);
  const [liveName, setLiveName] = useState<string>('');
  const [total, setTotal] = useState(0);
  const [orderBy, setOrderBy] = useState('add_time');
  const [sort, setSort] = useState('0');
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [shareModalVisible, setShareModalVisible] = useState<boolean>(false);
  const [shareObj, setShareObj] = useState<any>({
    mode: '',
    url: '',
    record: {},
  });
  const [form] = Form.useForm();
  const ORDER_OPTIONS = [
    {
      label: '创建时间',
      value: 'add_time',
    },
    {
      label: '开始时间',
      value: 'start_time',
    },
  ];
  const SORT_OPTIONS = [
    {
      label: '升序',
      value: '1',
    },
    {
      label: '降序',
      value: '0',
    },
  ];

  const [taskDrawerVisible, setTaskDrawerVisible] = useState(false);
  const [taskDrawerData, setTaskDrawerData] = useState<any>(null);

  useEffect(() => {
    handleGetLiveList();
  }, [searchInfo, sort, orderBy]);

  const handleInputChange = (e: any) => {
    setLiveName(e.target.value);
  };

  const handleGetLiveList = async () => {
    const res = await activityManagementService.fetchLiveList({
      ...searchInfo,
      live_name: liveName,
      order_by: sort === '1' ? orderBy : '-' + orderBy,
    });
    if (res.error_code === config.successCode) {
      setTotal(res?.extend_message?.total ?? 0);
      setLiveList(res.extend_message?.results);
    }
  };

  const handleStopLive = async (_id: string) => {
    const res = await activityManagementService.updateLiveToEnd(_id);
    if (res.error_code === config.successCode) {
      message.success('操作成功');
      handleGetLiveList();
    }
  };

  const handleStartLive = async (_id: string) => {
    const res = await activityManagementService.updateLiveToStart(_id);
    if (res.error_code === config.successCode) {
      message.success('操作成功');
      handleGetLiveList();
    } else {
      message.error(res.extend_message);
    }
  };
  const handleReStartLive = async (data: any) => {
    const liveInfo: ActivityType.ILiveOrigin = {
      _id: data._id,
      origin_start_time: Number(data.origin_start_time?.format('X')),
      origin_finish_time: Number(data.origin_finish_time?.format('X')) || '',
    };
    const res = await activityManagementService.updateLive(liveInfo);
    if (res.error_code === config.successCode) {
      message.success('操作成功');
      handleGetLiveList();
      setIsModalVisible(false);
    } else {
      message.error(res.extend_message);
      setIsModalVisible(false);
    }
  };

  const handleBulkDelete = async () => {
    const res = await activityManagementService.deleteLiveBulk(deleteIds);
    if (res.error_code === config.successCode) {
      message.success('删除成功');
      setDeleteIds([]);
      setSearchInfo({ ...searchInfo, page: 1 });
    } else {
      message.error(res.extend_message);
    }
  };

  const handleDeleteLive = async (_id: string) => {
    Modal.confirm({
      title: '提示',
      content: '确定删除该直播吗？',
      okText: '确定',
      icon: <QuestionCircleFilled style={{ color: 'var(--primary-color)' }} />,
      cancelText: '取消',
      onOk: async () => {
        const res = await activityManagementService.deleteLiveBulk([_id]);
        if (res.error_code === config.successCode) {
          message.success('删除成功');
          setSearchInfo({ ...searchInfo, page: 1 });
        } else {
          message.error(res.extend_message);
        }
      },
    });
  };

  const rowSelection = {
    onChange: (
      selectedRowKeys: React.Key[],
      newSelectedRows: ActivityType.ILive[],
    ) => {
      setDeleteIds(selectedRowKeys);
      setSelectedRows(newSelectedRows);
    },
  };
  const shareLive = (mode: string, item: any) => {
    setShareModalVisible(true);
    console.log(item);
    setShareObj({
      mode,
      url: `${location.origin}/learn/live/activity/${item._id}`,
      record: item,
    });
  };
  const columns = [
    {
      title: '直播名称',
      dataIndex: 'live_name',
      align: 'left',
    },
    {
      title: '主讲人',
      dataIndex: 'live_speaker',
      align: 'left',
    },
    {
      title: '开始时间',
      dataIndex: 'origin_start_time',
      align: 'left',
      render: (time: number) => {
        return time && moment.unix(time).format('YYYY-MM-DD HH:mm');
      },
    },
    {
      title: '结束时间',
      dataIndex: 'origin_finish_time',
      align: 'left',
      render: (time: number) => {
        return time && moment.unix(time).format('YYYY-MM-DD HH:mm');
      },
    },
    {
      title: '创建时间',
      dataIndex: 'add_time',
      align: 'left',
      render: (time: number) => {
        return time && moment.unix(time).format('YYYY-MM-DD HH:mm:ss');
      },
    },
    {
      title: '直播流类型',
      align: 'left',
      dataIndex: 'live_address_type',
      width: 200,
      render: (live_address_type: ActivityConstant.flowRadioOption) => {
        switch (live_address_type) {
          case ActivityConstant.flowRadioOption.classroomFlow:
            return <Tag color="magenta">教室相机直播流</Tag>;
          case ActivityConstant.flowRadioOption.customFlow:
            return <Tag color="blue">自定义直播流</Tag>;
        }
      },
    },
    {
      title: '直播流',
      align: 'left',
      dataIndex: 'live_address',
      width: 200,
      render: (addresses: ActivityType.IFlow[], record: ActivityType.ILive) => {
        switch (record.live_address_type) {
          case ActivityConstant.flowRadioOption.classroomFlow:
            return record.area_name;
          case ActivityConstant.flowRadioOption.customFlow:
            return addresses?.map((item) => item.stream_name)?.join('，');
        }
      },
    },
    {
      title: '状态',
      align: 'left',
      dataIndex: 'schedule_status',
      render: (status: number, record: any) => {
        switch (status) {
          case ActivityConstant.liveStatus.wait:
            return <div>未开始</div>;
          case ActivityConstant.liveStatus.on:
            return <div>直播中</div>;
          case ActivityConstant.liveStatus.end:
            return <div>已结束</div>;
          case ActivityConstant.liveStatus.close:
            return <div>已关闭</div>;
            //look_back_status 0异常 1 回看生成中 2直播回看 3回看已结束
            // return record.look_back ? (
            //   record.look_back_status ? (
            //     record.look_back_status === 1 ? (
            //       <div>回看生成中</div>
            //     ) : record.look_back_status === 2 ? (
            //       <a href={`/learn/live/activity/${record._id}`}>直播回看</a>
            //     ) : (
            //       <div>回看已结束</div>
            //     )
            //   ) : (
            //     <div>异常</div>
            //   )
            // ) : status === ActivityConstant.liveStatus.end ? (
            //   <div>已结束</div>
            // ) : (
            //   <div>已关闭</div>
            // );
          case ActivityConstant.liveStatus.error:
            return <div>异常</div>;
        }
      },
    },
    {
      title: '直播分享',
      align: 'left',
      dataIndex: '_id',
      render: (_id: string, record: ActivityType.ILive) => {
        return (
          <div className="shareLive">
            <IconFont
              type="iconerweima1"
              onClick={() => shareLive('qr', record)}
              title="二维码分享"
            />
            <IconFont
              type="iconchaolianjie01"
              onClick={() => shareLive('link', record)}
              title="链接分享"
            />
          </div>
        );
      },
    },
    {
      title: '操作',
      align: 'left',
      dataIndex: '_id',
      render: (_id: string, record: any) => {
        const disabledData = () => {
          if (record.schedule_status === ActivityConstant.liveStatus.on) {
            return false;
          } else if (
            record.schedule_status === ActivityConstant.liveStatus.end &&
            record.look_back &&
            record.look_back_status === 2
          ) {
            return false;
          }

          return true;
        };
        return (
          <div>
            <Button
              type="link"
              disabled={
                !(record.schedule_status === ActivityConstant.liveStatus.on)
              }
            >
              <a href={`/learn/live/activity/${_id}`} target="_blank">
                回看
              </a>
            </Button>
            <Button
              type="link"
              disabled={
                !(record.schedule_status === ActivityConstant.liveStatus.on)
              }
            >
              <a href={`/learn/live/activity/${_id}`} target="_blank">
                预览
              </a>
            </Button>
            <Button
              type="link"
              // disabled={!(record.schedule_status === ActivityConstant.liveStatus.wait)}
            >
              <Link to={`/mg/activity/createlive?id=${_id}`}>设置</Link>
            </Button>
            <Button
              type="link"
              onClick={() => {
                const address = record.live_address?.map((item: any) => {
                  return {
                    name: item?.stream_name,
                    url: item?.stream_address,
                    rtmp: item?.pull_url,
                  };
                });
                if (!address || address?.length === 0) {
                  message.error('直播流不存在，无法查看直播数据！');
                  return;
                }
                const state = {
                  startTime: record.origin_start_time,
                  endTime: record.origin_finish_time,
                  address: JSON.stringify(address),
                  name: record.live_name,
                } as any;
                history.push({
                  pathname: '/mg/live',
                  query: state,
                });
              }}
              disabled={disabledData()}
            >
              直播数据
            </Button>
            <Button
              type="link"
              onClick={() => handleDeleteLive(_id)}
              disabled={
                record.schedule_status === ActivityConstant.liveStatus.on
              }
            >
              删除
            </Button>
            {record.schedule_status === ActivityConstant.liveStatus.on && (
              <Button type="link" onClick={() => handleStopLive(_id)}>
                停止直播
              </Button>
            )}
            {(record.schedule_status === ActivityConstant.liveStatus.wait ||
              record.schedule_status === ActivityConstant.liveStatus.end) && (
              <Button type="link" onClick={() => handleStartLive(_id)}>
                立即开始
              </Button>
            )}
            {record.schedule_status === ActivityConstant.liveStatus.end && (
              <Button
                type="link"
                onClick={() => {
                  form.setFieldsValue({
                    _id: record?._id,
                  });
                  setIsModalVisible(true);
                }}
              >
                重新开始
              </Button>
            )}
            <Button type="link" onClick={() =>{
              setTaskDrawerVisible(true);
              setTaskDrawerData(record);
            }}>
                查看录播任务
            </Button>
          </div>
        );
      },
    },
  ];
  const DPR = () => {
    if (window.devicePixelRatio && window.devicePixelRatio > 1) {
      return window.devicePixelRatio;
    } else {
      return 1;
    }
  };
  const drawCanvas = async () => {
    //html转图片
    const dom: any = document.getElementById('poster');
    const box = window.getComputedStyle(dom);
    //dom 节点计算后宽高
    const width = parseInt(box.width, 10);
    const height = parseInt(box.height, 10);
    // 获取像素比-防止模糊
    const scaleBy = DPR();
    // 创建自定义 canvas 元素
    const canvas = document.createElement('canvas');
    // 设定 canvas 元素属性宽高为 DOM 节点宽高 * 像素比
    canvas.width = width * scaleBy;
    canvas.height = height * scaleBy;
    // 设定 canvas css宽高为 DOM 节点宽高
    canvas.style.width = `${width}px`;
    canvas.style.height = `${height}px`;
    // 获取画笔
    const context: any = canvas.getContext('2d');
    // 将所有绘制内容放大像素比倍
    context.scale(scaleBy, scaleBy);
    // 将自定义 canvas 作为配置项传入，开始绘制
    return await html2canvas(dom, { canvas }).then((canvas) => {
      const img = canvas.toDataURL('image/png');
      return navigator.clipboard
        ? img.replace(/data:image\/png;base64,/, '')
        : img;
    });
  };
  const copy_ = async () => {
    if (shareObj.mode === 'link') {
      copy(shareObj.url);
      message.success('复制成功');
    } else if (shareObj.mode === 'qr') {
      const str: any = await drawCanvas();
      if (!navigator.clipboard) {
        //在不安全网络时采用下载图片模式
        const a = document.createElement('a');
        a.href = str;
        // a.download 后面的内容为自定义图片的名称
        a.download = shareObj.record?.live_name + '_直播分享';
        a.click();
      } else {
        const file_ = b64toBlob(str, 'image/png');
        const clipboardItemInput = new window.ClipboardItem({
          'image/png': file_,
        });
        window.navigator.clipboard.write([clipboardItemInput]);
        message.success('复制成功');
      }
    }
  };
  return (
    <div className="activity_live">
      <div className="activity_head_wrapper">
        <div className="activity_search">
          <Space>
            <Select
              defaultValue={searchInfo.schedule_status}
              style={{ width: 120 }}
              onChange={(state: number) => {
                setSearchInfo({
                  ...searchInfo,
                  schedule_status: state,
                });
              }}
            >
              <Option value={ActivityConstant.liveStatus.all}>全部状态</Option>
              <Option value={ActivityConstant.liveStatus.wait}>未开始</Option>
              <Option value={ActivityConstant.liveStatus.on}>直播中</Option>
              <Option value={ActivityConstant.liveStatus.end}>已结束</Option>
              {/* <Option value={ActivityConstant.liveStatus.close}>已关闭</Option>
              <Option value={ActivityConstant.liveStatus.error}>异常</Option> */}
            </Select>
            <Input
              placeholder="请输入直播名称"
              value={liveName}
              allowClear
              onChange={handleInputChange}
              onKeyDown={(e) => {
                if (e.keyCode === 13) {
                  setSearchInfo({ ...searchInfo });
                }
              }}
            />
            <Button
              type="primary"
              onClick={() => setSearchInfo({ ...searchInfo, page: 1 })}
            >
              搜索
            </Button>
            <div className="cd_filter_wrapper">
              <Select
                className="order_select"
                options={ORDER_OPTIONS}
                value={orderBy}
                onChange={(orderBy) => {
                  setOrderBy(orderBy);
                }}
                suffixIcon={<IconFont type="iconshengjiang" />}
              />
              <Select
                className="sort_select"
                options={SORT_OPTIONS}
                suffixIcon={<IconFont type="iconshengjiang" />}
                value={sort}
                onChange={(sort) => {
                  setSort(sort);
                }}
              />
            </div>
          </Space>
        </div>
        <div className="activity_opt">
          <Space>
            <Popconfirm
              title="确认删除？"
              disabled={deleteIds.length === 0}
              onConfirm={() => {
                handleBulkDelete();
              }}
            >
              <Button
                type="primary"
                disabled={
                  selectedRows.some((item: any) => {
                    return (
                      item.schedule_status === ActivityConstant.liveStatus.on
                    );
                  }) || deleteIds.length === 0
                }
              >
                批量删除
              </Button>
            </Popconfirm>
            <Link to="/mg/activity/createlive">
              <Button type="primary">创建直播</Button>
            </Link>
          </Space>
        </div>
      </div>
      <div className="activity_main_wrapper">
        <Table
          scroll={{ y: 'calc(100vh - 235px)' }}
          rowSelection={{ type: 'checkbox', ...rowSelection }}
          columns={columns as any}
          rowKey="_id"
          dataSource={LiveList}
          pagination={{
            total: total,
            showQuickJumper: true,
            showSizeChanger: true,
            pageSize: searchInfo.size,
            current: searchInfo.page,
            onChange: (page, pageSize) => {
              setSearchInfo({
                ...searchInfo,
                page: page,
                size: pageSize ?? 0,
              });
            },
          }}
        />
      </div>
      <Modal
        title={'重新开始'}
        visible={isModalVisible}
        onOk={() => form.submit()}
        onCancel={() => setIsModalVisible(false)}
      >
        <Form
          form={form}
          onFinish={(values) => {
            handleReStartLive(values);
          }}
          labelCol={{ span: 5 }}
        >
          <Form.Item label="id" name="_id" noStyle hidden></Form.Item>
          <Form.Item
            label="开始时间"
            name={'origin_start_time'}
            rules={[{ required: true, message: '请选择直播开始日期' }]}
          >
            <DatePicker
              showTime
              dropdownClassName="display-now"
              format="YYYY-MM-DD HH:mm"
              disabledDate={(currentDate: any) => {
                const afterTag = form.getFieldValue('origin_finish_time')
                  ? currentDate > form.getFieldValue('origin_finish_time')
                  : false;
                // const beforeTag = currentDate < moment()
                const beforeTag =
                  Number(currentDate.format('X')) <
                  Number(moment().format('X'));
                return afterTag || beforeTag;
                // return currentDate
              }}
            />
          </Form.Item>
          <Form.Item label="结束时间">
            <Form.Item name={'origin_finish_time'} noStyle>
              <DatePicker
                showTime
                format="YYYY-MM-DD HH:mm"
                dropdownClassName="display-now"
                disabledDate={(currentDate: any) => {
                  const afterTag = form.getFieldValue('origin_start_time')
                    ? currentDate < form.getFieldValue('origin_start_time')
                    : false;
                  const beforeTag = currentDate < moment();
                  return afterTag || beforeTag;
                }}
              />
            </Form.Item>
          </Form.Item>
          <div className="activity_note_info">
            注：若不填写结束时间，则只能够手动结束直播
          </div>
        </Form>
      </Modal>
      <Modal
        title={shareObj.mode === 'link' ? '链接分享' : '二维码分享'}
        visible={shareModalVisible}
        destroyOnClose
        width={440}
        onCancel={() => setShareModalVisible(false)}
        footer={[
          <Button onClick={copy_}>
            {shareObj.mode === 'link'
              ? '复制链接'
              : navigator.clipboard
              ? '复制海报'
              : '下载海报'}
          </Button>,
        ]}
        className="shareLiveModal"
      >
        {shareObj.mode === 'link' ? (
          <div>{shareObj.url}</div>
        ) : (
          <div className="poster" id="poster">
            <div className="head">邀请您观看直播</div>
            <div className="body">
              <div className="imgDiv">
                <img src={shareObj.record.live_cover} />
              </div>
              <span
                title={shareObj.record.live_name}
                className={`name${
                  shareObj.record.live_name?.length > 20 ? ' rows' : ''
                }`}
              >
                {shareObj.record.live_name?.substring(0, 20)}
              </span>
              <span
                title={shareObj.record.live_name}
                className={`name${
                  shareObj.record.live_name?.length > 20 ? ' rows' : 'none'
                }`}
              >
                {shareObj.record.live_name?.substring(20)}
              </span>
              <span className="time">
                {moment
                  .unix(shareObj.record.origin_start_time)
                  .format('YYYY-MM-DD HH:mm')}
              </span>
              <QRCode value={shareObj.url} size={150} />
              <span>扫码观看直播</span>
            </div>
          </div>
        )}
      </Modal>
      <TaskDrawer visible={taskDrawerVisible} taskDrawerData={taskDrawerData} onClose={() => {
        setTaskDrawerVisible(false);
        handleGetLiveList();
      }} />
    </div>
  );
};
export default ActivityList;
