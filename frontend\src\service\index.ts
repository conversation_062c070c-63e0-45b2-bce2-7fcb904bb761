export interface IResponse<T = object | string | [] | null> {
  error_code: string;
  error_msg: string;
  extend_message: T;
}
export interface UpFormResponse<T = object | string | [] | null> {
  errorCode: string;
  errorMsg: string;
  extendMessage: T;
}
export interface UpFormListResponse<T extends any>
  extends UpFormResponse<{
    results: T;
    pageIndex: number;
    pageSize: number;
    pageTotal: number;
    recordTotal: number;
  }> {}

export interface RmanResponse<T = object | string | [] | null> {
  error: any;
  data: T;
  success: boolean;
}
export interface SupervisionResponse<T = object | string | [] | null> {
  message: string;
  stackMessage: string;
  statusCode: number;
  hostId: string;
  data: T;
}
