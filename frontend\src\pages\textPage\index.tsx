import React, { FC, useState, useEffect } from 'react';
import { Form, Input, Button, Checkbox, Space } from 'antd';
import './index.less';
import testService from '@/service/test';
import { Link } from 'umi';
import Header from '@/components/header/header';

const TextPage: FC = () => {
  const login = async () => {
    let res = await testService.login({
      loginName: 'zhiliao',
      password: 'SobeyPbu2020',
    });
    // console.log(res)
  };
  return (
    <div className="text-container">
      <Header showNav />
      <Space>
        <Button onClick={login}>登录</Button>
        <Button>
          <Link to="/mg/activity">
            <p>活动直播列表</p>
          </Link>
        </Button>
        <Button>
          <Link to="/mg/activity/flow">
            <p>活动直播流</p>
          </Link>
        </Button>
        <Button>
          <Link to="/mg/activity/createlive">
            <p>创建活动直播</p>
          </Link>
        </Button>
      </Space>
    </div>
  );
};

export default TextPage;
