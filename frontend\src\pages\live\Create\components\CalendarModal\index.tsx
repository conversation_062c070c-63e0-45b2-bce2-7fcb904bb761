/*
 * @Author: 冉志诚
 * @Date: 2023-08-02 10:22:52
 * @LastEditTime: 2023-08-02 10:22:52
 * @FilePath: \frontend\src\pages\live\Create\components\CalendarModal\index.tsx
 * @Description:
 */

import {
  Button,
  Empty,
  Form,
  Modal,
  ModalProps,
  Spin,
  Tooltip,
  message,
} from 'antd';
import React, { useEffect, useImperativeHandle, useMemo, useRef } from 'react';
import { useImmer } from 'use-immer';
import style from './index.less';
import {
  CalendarOutlined,
  CaretLeftOutlined,
  CaretRightOutlined,
} from '@ant-design/icons';
import { LiveService } from '@/api/live';
import moment from 'moment';
import {
  ClassNames,
  deepCopy,
  moveArrayElement,
  spliceAdd,
} from '@/utils/utils';
import TimeModal, { TimeRefType } from '../TimeModal';
import Access from '@/components/Access';

const CalendarModal: React.FC<CalendarModalProps> = ({
  actionRef: propActionRef,
  onSuccess,
  callback,
}) => {
  const [open, setOpen] = useImmer(false);
  const [row, setRow] = useImmer(10);
  const [liveData, setLiveData] = useImmer<LiveService.UserLiveResponseType[]>(
    [],
  );

  const timeActionRef = useRef<TimeRefType>(null);
  const [semesterWeeks, setSemesterWeeks] = useImmer<
    LiveService.SemesterWeekType | undefined
  >(undefined);
  /** 计算出可直接使用的二维数组 时间复杂度O(n^2) */
  const tableData = useMemo(() => {
    // 将liveData转换为二维数组
    let lives: LiveService.UserLiveType[][] = liveData.map((item) => {
      return item.lives.sort((a, b) => {
        return hourToTimestamp(a.start_time) - hourToTimestamp(b.start_time);
      });
    });

    const tableData: LiveService.UserLiveType[][] = [];
    // 将live 行列交换
    lives.forEach((item, j) => {
      item.forEach((live, i) => {
        if (!tableData[i]) {
          tableData[i] = [];
        }
        tableData[i][j] = live;
      });
    });
    lives = tableData;

    sortData(lives);

    // 找出二维数组中最长的项,作为行数
    const row = lives.reduce((pre, cur) => {
      return Math.max(pre, cur.length);
    }, 0);
    // 补齐列 7列
    lives.forEach((item) => {
      // 找出所有空值,填充
      for (let i = 0; i < 7; i++) {
        if (!item[i]) {
          //@ts-ignore
          item[i] = null;
        }
      }
    });
    // 通过row 将live不足的项补齐
    lives.forEach((item) => {
      if (item.length < row) {
        item.push(...new Array(row - item.length).fill(null));
      }
    });

    return lives;
  }, [row, liveData]);

  const [loading, setLoading] = useImmer(false);
  const [weekStartTime, setWeekStartTime] = useImmer(
    moment().startOf('week').valueOf(),
  );
  useImperativeHandle(
    propActionRef,
    () => {
      return {
        setOpen,
      };
    },
    [timeActionRef.current],
  );

  const modalConfig: ModalProps = {
    open,
    title: '系列分期直播创建',
    width: 1350,
    onCancel() {
      setOpen(false);
    },
    footer: null,
    style: {
      padding: 0,
      minWidth: 1350,
      paddingBottom: 20,
      top: 20,
    },

    className: 'calendar-modal',
    destroyOnClose: false,
  };

  // useEffect(() => {
  //   setRow(computedLine());
  // }, [lives]);
  useEffect(() => {
    getWeekData();
  }, []);
  useEffect(() => {
    if (!semesterWeeks?.this_week) {
      return;
    }
    getUserLiveData();
  }, [semesterWeeks?.this_week]);
  async function getWeekData() {
    setLoading(true);
    try {
      const { extend_message } = await LiveService.semesterWeek();

      setSemesterWeeks(extend_message);
    } catch (error) {
      message.error('获取学期周失败');
    }
    setLoading(false);
  }
  async function getUserLiveData() {
    if (!semesterWeeks?.this_week) {
      return;
    }
    setLoading(true);
    try {
      const { extend_message } = await LiveService.userLive(
        semesterWeeks?.this_week,
      );
      setLiveData(extend_message);
    } catch (error) {
      message.error('获取用户直播数据失败');
    }
    setLoading(false);
  }

  return (
    <Modal {...modalConfig}>
      <Spin spinning={loading}>
        <header className={style.header}>
          {/* <Button
          type="link"
          onClick={() => {
            setOpen(false);
          }}
        >
          返回
        </Button> */}
          <div></div>
          <div className={style.title}>
            <div
              role="button"
              onClick={() => {
                // 判断是否越过边界
                if (semesterWeeks?.this_week === semesterWeeks?.weeks?.[0]) {
                  return message.error('已经是第一周了');
                }
                setWeekStartTime(
                  moment(weekStartTime).subtract(1, 'week').valueOf(),
                );
                setSemesterWeeks((arg) => {
                  if (arg?.this_week) {
                    arg.this_week -= 1;
                  }
                });
              }}
            >
              <CaretLeftOutlined />
            </div>
            <p>第{semesterWeeks?.this_week}教学周</p>
            <div
              role="button"
              onClick={() => {
                // 判断是否越过边界
                if (semesterWeeks?.this_week === semesterWeeks?.weeks?.at(-1)) {
                  return message.error('已经是最后一周了');
                }
                setWeekStartTime(
                  moment(weekStartTime).add(1, 'week').valueOf(),
                );
                setSemesterWeeks((arg) => {
                  if (arg?.this_week) {
                    arg.this_week += 1;
                  }
                });
              }}
            >
              <CaretRightOutlined />
            </div>
          </div>
          <div
            role="button"
            className={style.create}
            onClick={() => {
              timeActionRef.current?.setTitle('第三期:直播名称');
              timeActionRef.current?.setOpen(true);
            }}
          >
            <CalendarOutlined />
            <p>系列分期</p>
          </div>
        </header>
        <main className={style.main}>
          <table className={style.table}>
            <thead>
              <tr>
                {['周一', '周二', '周三', '周四', '周五', '周六', '周日'].map(
                  (item, index) => {
                    return (
                      <th key={item}>
                        <span>
                          {moment(weekStartTime)
                            .add(index, 'day')
                            .format('YYYY-MM-DD')}
                        </span>
                        <span>{item}</span>
                      </th>
                    );
                  },
                )}
              </tr>
            </thead>
            <tbody>
              <Access accessible={tableData.length > 0}>
                {tableData.map((item, index) => {
                  return (
                    <tr key={index}>
                      {item.map((item, index) => {
                        if (item) {
                          const { live_name, start_time, end_time, area_name } =
                            item;
                          return (
                            <td
                              key={index}
                              // style.repeat
                              className={ClassNames(style.alive)}
                            >
                              <Tooltip title={live_name}>
                                <span>{live_name}</span>
                              </Tooltip>
                              <Tooltip
                                title={`${start_time}- ${end_time}`}
                                placement="left"
                              >
                                <span>
                                  {start_time}-{end_time}
                                </span>
                              </Tooltip>
                              <Tooltip title={area_name} placement="bottom">
                                <span>{area_name}</span>
                              </Tooltip>
                            </td>
                          );
                        }
                        return <td key={index}></td>;
                      })}
                    </tr>
                  );
                })}
              </Access>
              <Access accessible={tableData.length === 0}>
                <Empty
                  image={Empty.PRESENTED_IMAGE_SIMPLE}
                  description="暂无数据"
                />
              </Access>
            </tbody>
          </table>
        </main>
        <TimeModal actionRef={timeActionRef} onSuccess={onSuccess} />
      </Spin>
    </Modal>
  );
};

export type CalendarRefType = {
  setOpen: (open: boolean) => void;
};

interface CalendarModalProps {
  actionRef: React.MutableRefObject<CalendarRefType | null>;
  onSuccess?: (value: any) => void;
  callback?: (v: any) => void;
}
export default CalendarModal;
CalendarModal.displayName = 'CalendarModal';

/**
 * @description 排序以生成表格 时间复杂度O(n^2)
 */
function sortData(lives: LiveService.UserLiveType[][]) {
  // 比较行的数据,找出最小的start_time
  for (let i = 0; i < lives.length; i++) {
    const minItem = getMinItem(lives[i]);
    // minItem 不存在证明当前行已经没有数据了
    if (!minItem) {
      break;
    }
    for (let k = 0; k < 7; k++) {
      if (minItem === lives[i][k]) {
        continue;
      }
      if (!lives?.[i]?.[k]) {
        continue;
      }

      // 开始时间相同且结束时间在10分钟内则不偏移
      if (
        !(
          lives[i][k].start_time === minItem.start_time &&
          hourToTimestamp(lives[i][k].end_time) -
            hourToTimestamp(minItem.end_time) <=
            1000 * 60 * 10
        )
      ) {
        // 下一行越界
        if (!lives?.[i + 1]) {
          const newLine = Array(7).fill(null);
          newLine[k] = deepCopy(lives[i][k]);
          lives.push(newLine);
        } else {
          if (lives[i + 1][k]) {
            // 下一行的数据和当前行的数据比较
            moveDown(lives, i + 1, k);
            lives[i + 1][k] = deepCopy(lives[i][k]);
          } else {
            lives[i + 1][k] = deepCopy(lives[i][k]);
          }
        }
        //@ts-ignore  删除原来的元素
        lives[i][k] = null;
      }
    }
  }
}

/** 将后端返回的时间转换以便比较 */
function hourToTimestamp(time: string) {
  if (time.length > 5) {
    return moment(time, 'YYYY-MM-DD HH:mm:ss').valueOf();
  }
  return moment(time, 'HH:mm').valueOf();
}

/** 获取最小项  */
function getMinItem(lives: LiveService.UserLiveType[]) {
  return lives.reduce((pre: LiveService.UserLiveType | null, cur) => {
    if (!pre) {
      return cur ?? null;
    }
    if (!cur) {
      return pre;
    }
    if (hourToTimestamp(pre.start_time) > hourToTimestamp(cur.start_time)) {
      return cur;
    }
    return pre;
  }, null);
}

// 二维数组从指定行开始 一列下移一行
function moveDown(
  lives: LiveService.UserLiveType[][],
  row: number,
  column: number,
) {
  const len = lives.length;
  for (let i = len; i >= row; i--) {
    if (!lives[i]) {
      lives[i] = [];
    }
    lives[i][column] = lives[i - 1][column];
  }
}
