/*
 * @Author: 冉志诚
 * @Date: 2023-08-16 10:15:48
 * @LastEditTime: 2023-08-16 10:17:17
 * @FilePath: \frontend\src\pages\live\My\Speaker\Activity\index.tsx
 * @Description:
 */


import React, { useEffect, useMemo, useRef, useState } from 'react';
import style from './index.less';
import {
  Button,
  Dropdown,
  Modal,
  Popconfirm,
  Popover,
  Radio,
  Select,
  Space,
  Tabs,
  TabsProps,
  Tooltip,
  message,
} from 'antd';
// 导入tab样式
import 'antd/lib/tabs/style/index.css';
import { Tab } from 'rc-tabs/lib/interface';
import {
  ActionType,
  ProColumns,
  ProTable,
  ProTableProps,
} from '@ant-design/pro-components';
import { useImmer } from 'use-immer';
import { useQuery, useQueryPagination } from '@/hooks';
import IconFont from '@/components/iconFont/iconFont';
import { ArrowDownOutlined, ArrowUpOutlined } from '@ant-design/icons';
import LegendMode, { TABLE_MODE } from '../../../components/LegendMode';
import { history } from 'umi';
import { LiveService } from '@/api/live';
import moment from 'moment';
import Access from '@/components/Access';
import { isDevelopment, openNewPage } from '@/utils/utils';
import './reset.less';

type Item = Partial<LiveService.MyLiveData>;
type Params = {
  current?: number;
  pageSize?: number;
  order_by?: string;
} & Item;
type ConfigTypes = {
  /** 列表或图例模式 */
  mode: TABLE_MODE;
};

const MySpeaker: React.FC<AppProps> = () => {
  const { pagination, query, setQuery } = useQueryPagination<{
    activeKey: string;
  }>({
    pageSize: 9,
  });

  const [config, setConfig] = useImmer<ConfigTypes>({
    mode: TABLE_MODE.LIST,
  });
  const activeKey = useMemo(() => query.activeKey, [query]);
  const columns: ProColumns<Item>[] = [
    {
      title: '直播名称',
      fieldProps: {
        placeholder: '请输入直播名称',
      },
      formItemProps: {
        label: null,
      },
      dataIndex: 'live_name',
      width: 150,
      ellipsis: true,
    },

    {
      title: '直播状态',
      fieldProps: {
        placeholder: '请输入直播状态',
        showSearch: true,
      },
      formItemProps: {
        label: null,
      },
      dataIndex: 'schedule_status',

      valueEnum: {
        1: {
          text: '未开始',
          status: 'Processing',
        },
        2: {
          text: '直播中',
          status: 'Success',
        },
        3: {
          text: '已结束',
          status: 'Error',
        },
        4: {
          text: '已关闭',
          status: 'Error',
        },
        5: {
          text: '异常',
          status: 'Error',
        },
      },
    },
    {
      title: '主讲人',
      search: false,
      dataIndex: 'live_speaker',
    },
    {
      title: '听众对象',
      search: false,
      dataIndex: 'look_permission',
      valueEnum: {
        public: '社会公开',
        school: '校内公开',
        password: '需要密码加入',
        school_custom: '校内自定义范围',
      },
    },
    {
      title: '创建时间',
      search: false,
      dataIndex: 'add_time',
      renderText(text, record, index, action) {
        return moment(text * 1000).format('YYYY-MM-DD HH:mm:ss');
      },
    },
    {
      title: '开始时间',
      search: false,
      dataIndex: 'origin_start_time',
      renderText(text, record, index, action) {
        if (!text) {
          return;
        }
        return moment(text * 1000).format('YYYY-MM-DD HH:mm:ss');
      },
    },
    {
      title: '结束时间',
      search: false,
      dataIndex: 'origin_finish_time',
      renderText(text, record, index, action) {
        if (!text) {
          return;
        }
        return moment(text * 1000).format('YYYY-MM-DD HH:mm:ss');
      },
    },
    {
      title: '操作',
      valueType: 'option',
      width: 150,
      fixed: 'right',
      render: (_, record, index, action) => {
        const { _id, schedule_status } = record;
        const disabledView = schedule_status !== 2 && schedule_status !== 3;
        const isPlayback = schedule_status === 3;

        const deleteBtnDisabled = schedule_status === 2;
        return (
          <div key="operation" className={style.operation}>
            <Button
              type="link"
              disabled={disabledView}
              onClick={() => {
                history.push({
                  pathname: '/live/control',
                  query: {
                    id: _id!,
                    isPlayback: isPlayback ? '1' : null,
                  },
                });
              }}
            >
              {isPlayback ? '回放' : '查看'}直播
            </Button>
            <Popconfirm
              title="确定删除吗？"
              disabled={deleteBtnDisabled}
              onConfirm={async () => {
                try {
                  const {
                    error_code,
                    extend_message,
                  } = await LiveService.deleteLiveBulk([_id!]);
                  if (error_code === 'livemanage.0000.0000') {
                    message.success('删除成功');
                    action?.reload();
                  } else {
                    throw new Error(extend_message);
                  }
                } catch (error) {
                  message.error((error as any).message);
                }
              }}
            >
              <Button
                style={
                  deleteBtnDisabled
                    ? {
                        padding: 0,
                      }
                    : {
                        color: 'red',
                      }
                }
                disabled={deleteBtnDisabled}
                type="link"
              >
                删除直播
              </Button>
            </Popconfirm>
          </div>
        );
      },
    },
  ];

  const [params, setParams] = useImmer<Params>({
    order_by: '-add_time',
  });
  const actionRef = useRef<ActionType>();

  const Condition = () => {
    const { mode } = config;
    return (
      <div className={style.condition}>
        <Select
          className={style.item}
          key="createTime"
          value={params.order_by}
          onChange={v => {
            setParams(d => {
              d.order_by = v;
            });
          }}
          options={[
            {
              label: (
                <>
                  开始时间
                  <ArrowUpOutlined />
                </>
              ),
              value: 'start_time',
            },
            {
              label: (
                <>
                  开始时间
                  <ArrowDownOutlined />
                </>
              ),
              value: '-start_time',
            },
            {
              label: (
                <>
                  创建时间
                  <ArrowUpOutlined />
                </>
              ),
              value: 'add_time',
            },
            {
              label: (
                <>
                  创建时间
                  <ArrowDownOutlined />
                </>
              ),
              value: '-add_time',
            },
          ]}
        ></Select>

        <div
          className={mode === TABLE_MODE.CARD ? style.active : ''}
          onClick={() => {
            setConfig(d => {
              d.mode = TABLE_MODE.CARD;
            });
          }}
        >
          <Tooltip title="图例模式">
            <IconFont type="iconhebingxingzhuangfuzhi2" />
          </Tooltip>
        </div>
        <div
          className={mode === TABLE_MODE.LIST ? style.active : ''}
          onClick={() => {
            setConfig(d => {
              d.mode = TABLE_MODE.LIST;
            });
          }}
        >
          <Tooltip title="列表模式">
            <IconFont type="iconliebiao" />
          </Tooltip>
        </div>
      </div>
    );
  };

  const [loading, setLoading] = useState(false);
  const tableConfig: ProTableProps<Item, Params> = {
    rowKey: '_id',
    toolBarRender: false,
    search: {
      optionRender(searchConfig, props, dom) {
        return [
          <div
            style={{
              display: 'flex',
              gap: 10,
            }}
            key="defaultDom"
          >
            {dom}
          </div>,
          <Condition key="condition" />,
        ];
      },
      span: 4,
      showHiddenNum: true,
      labelWidth: 'auto',
      className: 'in-pro-search',
      searchText: '搜索',
    },
    columns,
    pagination,
    loading,
    params,
    actionRef,
    onLoadingChange(loading) {
      setLoading(loading as boolean);
    },
    tableRender(props, defaultDom) {
      const { dataSource, loading } = (props as any).action ?? {};
      const { pagination } = props;
      return config.mode === TABLE_MODE.LIST ? (
        defaultDom
      ) : (
        <div>
          <LegendMode
            cardButtonsRender={item => {
              if (!item) {
                return [<></>];
              }
              const { _id, schedule_status } = item;
              const disabledView =
                schedule_status !== 2 && schedule_status !== 3;
              const isPlayback = schedule_status === 3;

              return [
                <Button
                  disabled={disabledView}
                  key="replay"
                  type="link"
                  onClick={() => {
                    history.push({
                      pathname: '/live/control',
                      query: {
                        id: _id!,
                        isPlayback: isPlayback ? '1' : null,
                      },
                    });
                  }}
                >
                  回放
                </Button>,
              ];
            }}
            pagination={pagination}
            dataSource={dataSource}
            loading={loading}
          />
        </div>
      );
    },
    async request(params, sort, filter) {
      try {
        console.log('发生请求', params);
        const {
          current,
          pageSize,
          schedule_status,
          live_name,
          order_by,
          apply_status,
        } = params;
        const body = {
          type_name: LiveService.MyLiveTypeName.SPEAKER,
          page: current!,
          size: pageSize!,
          live_name,
          schedule_status,
          order_by,
          apply_status,
        };
        const {
          extend_message: { results, total },
        } = await LiveService.getMyLive(body);

        return {
          data: results,
          // success 请返回 true，
          // 不然 table 会停止解析数据，即使有数据
          success: true,
          // 不传会使用 data 的长度，如果是分页一定要传
          total,
        };
      } catch (error) {
        return {
          data: [],
          success: false,
        };
      }
    },
  };
  return <ProTable {...tableConfig} />;
};

interface AppProps {}
export default MySpeaker;
MySpeaker.displayName = 'MyApply';
