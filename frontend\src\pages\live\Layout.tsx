/*
 * @Author: spring breeze
 * @Date: 2023-07-03 15:43:39
 * @LastEditTime: 2023-07-03 15:43:39
 * @FilePath: \frontend\src\pages\live\Layout.tsx
 * @Description:
 */

import LeftMenu from '@/components/LeftMenu';
import Header, { CUSTOMER_NPU } from '@/components/header/header';
import React, { useEffect, useMemo } from 'react';
import { useHistory, useLocation, useSelector } from 'umi';
import style from './Layout.less';
import Access from '@/components/Access';
import Left from './components/Left';
import { IGlobal } from '@/models/global';
import { useIsAdmin } from '@/hooks';
import './reset.less';

const LiveLayout: React.FC<LiveProps> = ({ children }) => {
  const location = useLocation();
  const { parameterConfig } = useSelector<{ global: any }, any>(
    (state) => state.global,
  );
  const isBlackPath = useMemo(() => {
    const { pathname } = location;
    return pathname.startsWith('/live/management');
  }, [location]);

  const hideHeader = useMemo(
    () => (location as any).query.hideHeader === '1' || isBlackPath,
    [location, isBlackPath],
  );
  const hideLeft = useMemo(
    () => (location as any).query.hideLeft === '1' || isBlackPath,
    [location, isBlackPath],
  );
  const hideMenu = useMemo(
    () =>
      parameterConfig?.target_customer === CUSTOMER_NPU ||
      (location as any).query.hideMenu === '1' ||
      isBlackPath,
    [location, isBlackPath, parameterConfig?.target_customer],
  );

  const active = useMemo(() => location.pathname ?? '', [location]);
  const { userInfo } = useSelector<any, IGlobal>((state) => state.global);

  useEffect(() => {
    let theme_config = JSON.parse(
      (localStorage as any).getItem('theme_config'),
    );
    if (theme_config) {
      if (
        document.title != `直播-${theme_config.browserTabAbbreviation}` &&
        theme_config.isShow == 1
      ) {
        document.title = '直播' + '-' + theme_config.browserTabAbbreviation;
      }
    }
  });
  useEffect(() => {}, []);
  return (
    <div>
      <Access accessible={!hideHeader}>
        <div className={style.header}>
          <Header showNav={true} navActive={active} />
        </div>
      </Access>
      <main className={style.main}>
        <Access accessible={!hideMenu}>
          <div className={style.left}>
            <LeftMenu userInfo={userInfo} />
          </div>
          <div style={{ paddingLeft: 70 }}></div>
        </Access>
        <div
          className={style.content}
          id="content"
          style={{
            padding: hideMenu ? 0 : undefined,
          }}
        >
          <Access accessible={!hideLeft}>
            <Left />
          </Access>

          <div
            className={style.child}
            id="child"
            style={{
              marginLeft: hideLeft ? 0 : undefined,
              paddingTop: hideHeader ? 0 : undefined,
            }}
          >
            {children}
          </div>
        </div>
      </main>
    </div>
  );
};

interface LiveProps {
  children: React.ReactNode;
}
export default LiveLayout;
LiveLayout.displayName = 'LiveLayout';
