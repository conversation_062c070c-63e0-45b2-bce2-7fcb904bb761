declare namespace ActivityType {
  //--------------流地址-----------------------
  interface IFlowOrigin {
    _id: string
    stream_address: string;
    play_url: string;
    push_url: string;
    pull_url: string;
    is_mute?: any;
    is_select?: boolean;
    stream_name?:string
  }
  interface IFlowForm extends IFlowOrigin {
    stream_name: string;
  }
  interface IFlow extends IFlowForm{
    is_used: boolean; // true-占用，false-未占用
    stream_status: boolean; // true-启用；false-禁用
  }

  interface IFlowClassroom {
    id: number;
    ai_url: string;
    ai_url_flv: string;
    name: string;
    guide_url?: string;
    guide_url_flv: string;
    student_full_url:string;
    student_full_url_flv:string;
    student_full_has_voice:any;
    student_singl_url:string;
    student_singl_url_flv:string;
    student_singl_has_voice:any;
    student_trace_url:string;
    student_trace_url_flv:string;
    student_trace_has_voice:any;
    teacher_full_url:string;
    teacher_full_url_flv:string;    
    teacher_full_has_voice:any;
    teacher_singl_url:string;
    teacher_singl_url_flv:string;
    teacher_singl_has_voice:any;
    teacher_trace_url:string;
    teacher_trace_url_flv:string;
    teacher_trace_has_voice:any;
  }

  // ---------------直播任务--------------------
  interface ILiveOrigin {
    // 基本信息
    _id?: string,
    live_name?: string,
    origin_start_time?: number,
    origin_finish_time?: number | '',
    live_cover?: string,
    live_speaker_id?: string,
    live_speaker?: string,
    live_introduce?: string,
    // 直播源(流地址)
    live_address?: (IFlowOrigin | undefined)[],
    // 智能分析相关
    voice_identify?: boolean,
    voice_translation?: boolean,
    live_address_type?: number,
    area_name?: string
  }

  interface ILive extends ILiveOrigin {
    live_now: boolean,  //true--立即开启直播；false--不开启
    dynamic_start_time: number,
    add_time: number,
    update_time: number,
    online_numbers: number,
    room_id: string,
    schedule_status: number,  // 活动直播状态；
    schedule_type: number,  // 直播类型；2--活动直播
  }

  interface IPagerParam {
    page: number,
    size: number
  }

  interface ILiveSearchParam extends IPagerParam {
    schedule_status: number,
  }

  type ILiveSearch = ILiveSearchParam & {
    live_name: string,
    order_by:string
  }

  interface IBasicItem {
    alias: string,
    fieldName: string,
    controlData: string
  }

  interface ITeacherMap {
    [x: string]: string
  }
}
