<!--
 * @Author: 李晋
 * @Date: 2021-10-21 18:20:18
 * @Email: <EMAIL>
 * @LastEditTime: 2023-09-20 16:53:57
 * @Description: file information
 * @Company: Sobey
-->
<!doctype html>
<html lang="zh">

<head>
  <meta charset="UTF-8">
  <meta name="viewport"
    content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
  <meta http-equiv="X-UA-Compatible" content="ie=edge">
  <title>网站管理系统</title>
</head>

<body>
  <link rel="stylesheet/less" type="text/css"
    href="<%= context.config.publicPath %>theme.less" />
  <script>
    window.less = {
      async: true,
      env: 'production'
    };

  </script>
  <div id="root"></div>
  <!-- 同步加载,异步出现主题闪烁的bug -->
  <script type="text/javascript"  src="/learn/workbench/CommonMenu.min.js"></script>
  <script type="text/javascript"
    src="<%= context.config.publicPath %>libs/less.min.js"></script>
</body>

</html>