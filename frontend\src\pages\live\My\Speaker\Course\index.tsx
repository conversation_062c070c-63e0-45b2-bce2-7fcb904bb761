/*
 * @Author: 冉志诚
 * @Date: 2023-08-16 10:17:30
 * @LastEditTime: 2023-08-16 10:47:12
 * @FilePath: \frontend\src\pages\live\My\Speaker\Course\index.tsx
 * @Description:
 */

import React, { useEffect, useMemo, useRef, useState } from 'react';
import style from './index.less';
import {
  Button,
  Dropdown,
  Modal,
  Popconfirm,
  Popover,
  Radio,
  Select,
  Space,
  Tabs,
  TabsProps,
  Tooltip,
  message,
} from 'antd';
// 导入tab样式
import 'antd/lib/tabs/style/index.css';
import { Tab } from 'rc-tabs/lib/interface';
import {
  ActionType,
  ProColumns,
  ProTable,
  ProTableProps,
} from '@ant-design/pro-components';
import { useImmer } from 'use-immer';
import { useQuery, useQueryPagination } from '@/hooks';
import IconFont from '@/components/iconFont/iconFont';
import { ArrowDownOutlined, ArrowUpOutlined } from '@ant-design/icons';
import LegendMode, { TABLE_MODE } from '../../../components/LegendMode';
import { history } from 'umi';
import { LiveService } from '@/api/live';
import moment from 'moment';
import Access from '@/components/Access';
import { isDevelopment, openNewPage } from '@/utils/utils';
import './reset.less';
import Request from '@/constant/request';

type Item = Partial<LiveService.CourseLiveData>;
type Params = {
  current?: number;
  pageSize?: number;
  order_by?: string;
} & Item;
type ConfigTypes = {
  /** 列表或图例模式 */
  mode: TABLE_MODE;
};

const MySpeaker: React.FC<AppProps> = () => {
  const { pagination } = useQueryPagination<{
    activeKey: string;
  }>({
    pageSize: 9,
  });

  const [config, setConfig] = useImmer<ConfigTypes>({
    mode: TABLE_MODE.LIST,
  });
  const columns: ProColumns<Item>[] = [
    {
      title: '直播名称',
      fieldProps: {
        placeholder: '请输入直播名称',
      },
      formItemProps: {
        label: null,
      },
      dataIndex: 'course_name',
      width: 150,
      ellipsis: true,
    },

    {
      title: '主讲人',
      search: false,
      dataIndex: 'teacher_name',
    },

    {
      title: '创建时间',
      search: false,
      dataIndex: 'create_time',
      renderText(text, record, index, action) {
        return moment(text * 1000).format('YYYY-MM-DD HH:mm:ss');
      },
    },

    {
      title: '操作',
      valueType: 'option',
      width: 150,
      fixed: 'right',
      render: (_, record, index, action) => {
        const { course } = record;

        return (
          <div key="operation" className={style.operation}>
            {/* 杰哥后面出接口 */}
            {/* <Button
              type="link"
              onClick={() => {
                history.push({
                  pathname: '/live/control',
                  query: {
                    id: course!,
                    isPlayback: '1',
                    type: Request.LIVE_TYPE.COURSE,
                  },
                });
              }}
            >
              查看直播
            </Button> */}
          </div>
        );
      },
    },
  ];

  const [params, setParams] = useImmer<Params>({
    order_by: '-add_time',
  });
  const actionRef = useRef<ActionType>();

  const Condition = () => {
    const { mode } = config;
    return (
      <div className={style.condition}>
        {/* <Select
          className={style.item}
          key="createTime"
          value={params.order_by}
          onChange={v => {
            setParams(d => {
              d.order_by = v;
            });
          }}
          options={[
            {
              label: (
                <>
                  开始时间
                  <ArrowUpOutlined />
                </>
              ),
              value: 'start_time',
            },
            {
              label: (
                <>
                  开始时间
                  <ArrowDownOutlined />
                </>
              ),
              value: '-start_time',
            },
            {
              label: (
                <>
                  创建时间
                  <ArrowUpOutlined />
                </>
              ),
              value: 'add_time',
            },
            {
              label: (
                <>
                  创建时间
                  <ArrowDownOutlined />
                </>
              ),
              value: '-add_time',
            },
          ]}
        ></Select> */}

        <div
          className={mode === TABLE_MODE.CARD ? style.active : ''}
          onClick={() => {
            setConfig((d) => {
              d.mode = TABLE_MODE.CARD;
            });
          }}
        >
          <Tooltip title="图例模式">
            <IconFont type="iconhebingxingzhuangfuzhi2" />
          </Tooltip>
        </div>
        <div
          className={mode === TABLE_MODE.LIST ? style.active : ''}
          onClick={() => {
            setConfig((d) => {
              d.mode = TABLE_MODE.LIST;
            });
          }}
        >
          <Tooltip title="列表模式">
            <IconFont type="iconliebiao" />
          </Tooltip>
        </div>
      </div>
    );
  };

  const [loading, setLoading] = useState(false);
  const tableConfig: ProTableProps<Item, Params> = {
    rowKey: 'id',
    toolBarRender: false,
    search: {
      optionRender(searchConfig, props, dom) {
        return [
          <div
            style={{
              display: 'flex',
              gap: 10,
            }}
            key="defaultDom"
          >
            {dom}
          </div>,
          <Condition key="condition" />,
        ];
      },
      span: 4,
      showHiddenNum: true,
      labelWidth: 'auto',
      className: 'in-pro-search',
      searchText: '搜索',
    },
    columns,
    pagination,
    loading,
    params,
    actionRef,
    onLoadingChange(loading) {
      setLoading(loading as boolean);
    },
    tableRender(props, defaultDom) {
      const { dataSource, loading } = (props as any).action ?? {};
      const { pagination } = props;
      return config.mode === TABLE_MODE.LIST ? (
        defaultDom
      ) : (
        <div>
          <LegendMode
            cardButtonsRender={(item) => {
              if (!item) {
                return [<></>];
              }
              const { _id, schedule_status } = item;
              const disabledView =
                schedule_status !== 2 && schedule_status !== 3;
              const isPlayback = schedule_status === 3;

              return [
                <Button
                  disabled={disabledView}
                  key="replay"
                  type="link"
                  onClick={() => {
                    history.push({
                      pathname: '/live/control',
                      query: {
                        id: _id!,
                        isPlayback: isPlayback ? '1' : null,
                      },
                    });
                  }}
                >
                  回放
                </Button>,
              ];
            }}
            pagination={pagination}
            dataSource={dataSource}
            loading={loading}
          />
        </div>
      );
    },
    async request(params, sort, filter) {
      try {
        console.log('发生请求', params);
        const { current, pageSize, order_by } = params;
        const body = {
          type_name: LiveService.MyLiveTypeName.SPEAKER,
          page: current!,
          size: pageSize!,
          order_by,
        };
        const {
          extend_message: { results, total },
        } = await LiveService.myCourseLive(body);

        return {
          data: results,
          // success 请返回 true，
          // 不然 table 会停止解析数据，即使有数据
          success: true,
          // 不传会使用 data 的长度，如果是分页一定要传
          total,
        };
      } catch (error) {
        return {
          data: [],
          success: false,
        };
      }
    },
  };
  return <ProTable {...tableConfig} />;
};

interface AppProps {}
export default MySpeaker;
MySpeaker.displayName = 'MyApply';
