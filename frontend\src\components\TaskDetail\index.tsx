import React, { useState, useEffect } from 'react';
import { Modal, Progress, Button, Spin, message, Table, Form, Input, Select, TreeSelect } from 'antd';
// import './index.less'
import courseManagementService from '@/service/courseManagementService'
const { Option } = Select
interface CompoentProps {
    modalVisible: boolean;
    modalClose: () => void;
    operationData: any
}
const TaskDetail: React.FC<CompoentProps> = (props) => {
    const { modalVisible, modalClose, operationData } = props
    const [dataSource, setDataSource] = useState<any>([])
    // const dataSource = [
    //     {
    //         key: '1',
    //         name: '胡彦斌',
    //         age: 32,
    //         address: '西湖区湖底公园1号',
    //     },
    //     {
    //         key: '2',
    //         name: '胡彦祖',
    //         age: 42,
    //         address: '西湖区湖底公园1号',
    //     },
    // ];

    const columns = [
        {
            title: '通道名称',
            dataIndex: 'name',
            key: 'name',
        },
        {
            title: '流地址',
            dataIndex: 'address',
            key: 'address',
        },
        // {
        //     title: '流状态',
        //     dataIndex: 'address',
        //     key: 'address',
        // },
        // {
        //     title: '提示信息',
        //     dataIndex: '1',
        //     key: '1',
        // },
    ];
    useEffect(() => {
        if (modalVisible) {
            courseManagementService.getareUrls(
                // '89'
                JSON.parse(operationData.course_metadata)?.area
            ).then((res: any) => {
                if (res && res.error_msg === 'Success') {
                    // console.log(res)
                    let data = [{
                        key: 1,
                        name: '教师摄像机',
                        address: res.extend_message.teacher_url,
                    },
                    {
                        key: 2,
                        name: '录屏',
                        address: res.extend_message.ai_url,
                    }]
                    setDataSource(data)
                } else {
                    message.error(res.extend_message)
                }
            })
        }
    }, [modalVisible])
    return (
        <div className='task-detail'>
            <Modal
                destroyOnClose
                title='详情'
                visible={modalVisible}
                closable={false}
                footer={[
                    // <Button key="submit" type="primary" onClick={modalOk}>确定</Button>,
                    <Button key="back" onClick={modalClose}>关闭</Button>,
                ]}
                width={800}>
                <Table
                    dataSource={dataSource}
                    columns={columns}
                    pagination={false}
                />
            </Modal>
        </div>
    );
};

export default TaskDetail;
