namespace RequestConstant {
  export enum module {
    default = '/livemanage',
    rman = '/rman',
    cvod = '/cvod',
    unifiedplatform = '/unifiedplatform',
    curr = '/curr', // 课表服务
    ipingestman = '/ipingestman',
    SOMEDIASERVER = '/somediaserver',
  }

  export enum coreType {
    default = 'DEFAULT',
    sleep = 'SLEEP',
  }
  /**
   * @description 接口前缀
   */
  export enum PREFIX {
    RMAN = '/rman',
    UP_FORM = '/unifiedplatform',
    LIVE_MANAGE = '/livemanage',
    SUPERVISION = '/supervision',
    IPINGESTMAN = '/ipingestman',
    LEARN = '/learn',
  }
  export enum CODE {
    SUCCESS_LIVE_MANAGE = 'livemanage.0000.0000',
  }
  export enum LIVE_TYPE {
    ACTIVITY = '1',
    COURSE = '2',
  }
}
export default RequestConstant;
