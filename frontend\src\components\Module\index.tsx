import './index.less';
interface IModuleProps {
  title?: string;
  width?: string;
  horizonAlign: 'left' | 'right' | 'center';
  style?: Object;
}
const horizonAlignCSS = {
  left: 'flex-start',
  right: 'flex-end',
  center: 'center',
};
const Module: React.FC<IModuleProps> = (props) => {
  const {
    title,
    width = '100%',
    children,
    horizonAlign = 'left',
    style,
  } = props;
  return (
    <div
      className="module_container"
      style={title ? { ...style, boxShadow: '#0002 2px 2px 8px' } : style}
    >
      {title && (
        <header>
          <h3>{title}</h3>
        </header>
      )}
      <main style={{ justifyContent: horizonAlignCSS[horizonAlign] }}>
        <div className="module_content" style={{ width: width }}>
          {children}
        </div>
      </main>
    </div>
  );
};

export default Module;
