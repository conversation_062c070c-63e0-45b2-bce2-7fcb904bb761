.activity_live {
  // padding: 20px;
  .activity_head_wrapper {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
  }
  .cd_filter_wrapper .order_select,.cd_filter_wrapper .sort_select{
    margin-left: 20px;
  }
  .activity_main_wrapper{
    .ant-table-wrapper{
      .ant-table-tbody{
        .shareLive{
          >span.anticon{
            cursor: pointer;
            &:not(:last-child){
              margin-right: 10px;
            }
          }
        }
      }
    }
  }
}
.activity_note_info{
  display: inline;
  color: #ccc;
  padding-left: 30px;
}
.shareLiveModal{
  .ant-modal-body{
    padding: 19px 20px 20px 20px;
    .poster{
      background: #508CFF;
      height: 590px;
      padding-top: 20px;
      background-repeat: no-repeat;
      background-position: 0% 100%;
      background-image: url(/livemanage/image/sharebackground.png);
      .head{
        text-align: center;
        background: #508CFF;
        font-size: 16px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #FFFFFF;
        line-height: 22px;
      }
      .body{
        padding-top: 20px;
        width: 400px;
        height: 560px;
        display: flex;
        flex-direction: column;
        align-items:center;
        .imgDiv{
          width: 215px;
          height: 130px;
          background: #F1F1F1;
          border-radius: 12px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-top: 30px;
          >img{
            width: 198px;
            height: 110px;
            object-fit: contain;
          }
        }
        .name{
          font-size: 16px;
          font-weight: 600;
          margin-top: 15px;
          width: 303px;
          text-align: center;
        }
        .rows{
          margin-top:0 !important;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space:nowrap;
        }
        .none{
          display: none;
        }
        .time{
          margin-top: 10px;
          font-size: 14px;
          font-weight: 600;
        }
        >canvas{
          margin-top: 55px;
        }
        >span:last-child{
          margin-top: 14px;
          font-size: 14px;
          font-weight: 600;
        }
      }
    }
  }
  .ant-modal-footer{
    .ant-btn{
      width: 100%;
    }
  }
}