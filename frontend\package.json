{"private": true, "scripts": {"start": "node theme && cross-env NODE_OPTIONS=--openssl-legacy-provider umi dev", "build": "cross-env NODE_OPTIONS=--openssl-legacy-provider umi build", "postinstall": "umi generate tmp", "prettier": "prettier --write '**/*.{js,jsx,tsx,ts,less,md,json}'", "test": "umi-test", "test:coverage": "umi-test --coverage"}, "gitHooks": {"pre-commit": "lint-staged"}, "lint-staged": {"*.{js,jsx,less,md,json}": ["prettier --write"], "*.ts?(x)": ["prettier --parser=typescript --write"]}, "dependencies": {"@ant-design/cssinjs": "1.5.6", "@ant-design/icons": "^4.8.0", "@ant-design/pro-components": "2.4.2", "@ant-design/pro-layout": "^6.5.0", "@umijs/preset-react": "1.x", "antd": "^4.24.2", "antd-img-crop": "^3.16.0", "antd-theme-generator": "^1.2.10", "b64-to-blob": "^1.2.19", "clipboard": "^2.0.11", "copy-to-clipboard": "^3.3.2", "echarts": "^5.4.2", "html2canvas": "^1.4.1", "immer": "^10.0.2", "jquery": "^3.7.1", "lodash": "^4.17.21", "qrcode.react": "^3.1.0", "react": "^18.2.0", "react-dom": "^18.2.0", "umi": "^3.5.17", "use-immer": "^0.9.0", "xgplayer": "^3.0.4", "xgplayer-flv": "^3.0.4", "xgplayer-hls.js": "^3.0.4", "xgplayer-mp4": "^3.0.4"}, "devDependencies": {"@umijs/test": "^3.3.7", "cross-env": "^7.0.3", "lint-staged": "^10.0.7", "prettier": "^2.2.0", "typescript": "^4.1.2", "yorkie": "^2.0.0"}, "resolutions": {"antd": "^4.24.2", "@ant-design/cssinjs": "1.5.6", "react": "^18.2.0", "react-dom": "^18.2.0"}}