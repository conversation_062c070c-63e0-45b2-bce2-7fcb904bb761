/*
 * @Author: 冉志诚
 * @Date: 2023-07-10 10:42:18
 * @LastEditTime: 2023-08-14 13:48:06
 * @FilePath: \frontend\src\pages\live\My\Speaker\index.tsx
 * @Description:
 */

import React, { useEffect, useMemo, useRef, useState } from 'react';
import style from './index.less';
import {
  Button,
  Dropdown,
  Modal,
  Popconfirm,
  Popover,
  Radio,
  Select,
  Space,
  Tabs,
  TabsProps,
  Tooltip,
  message,
} from 'antd';
// 导入tab样式
import 'antd/lib/tabs/style/index.css';
import { Tab } from 'rc-tabs/lib/interface';

import { useIsAdmin, useQuery, useQueryPagination } from '@/hooks';
import { LiveService } from '@/api/live';
import Access from '@/components/Access';
import './reset.less';
import Activity from './Activity';
import Course from './Course';
import { useHistory } from 'umi';

enum LiveItem {
  活动直播 = '1',
  课程直播 = '2',
}

const items: Tab[] = [
  {
    key: LiveItem.活动直播,
    label: '活动直播',
  },
  {
    key: LiveItem.课程直播,
    label: '课程直播',
  },
];

type Item = Partial<LiveService.MyLiveData>;
type Params = {
  current?: number;
  pageSize?: number;
} & Item;

const MySpeaker: React.FC<AppProps> = () => {
  const { query, setQuery } = useQueryPagination<{
    activeKey: string;
  }>({
    pageSize: 9,
  });
  const history = useHistory();
  const tabsConfig: TabsProps = {
    items,
    activeKey: query.activeKey ?? LiveItem.活动直播,
    defaultActiveKey: LiveItem.活动直播,
    onChange(activeKey) {
      setQuery({
        ...query,
        page: 1,
        activeKey,
      });
    },
  };
  const isAdmin = useIsAdmin();
  useEffect(() => {
    if (!isAdmin) {
      history.replace({
        pathname: '/live/my/apply',
      });
    }
  }, [isAdmin]);

  const activeKey = useMemo(
    () => query.activeKey ?? LiveItem.活动直播,

    [query],
  );

  return (
    <div className={style.apply}>
      <Tabs {...tabsConfig}></Tabs>
      <Access accessible={activeKey === LiveItem.活动直播}>
        <Activity />
      </Access>
      <Access accessible={activeKey === LiveItem.课程直播}>
        <Course />
      </Access>
    </div>
  );
};

interface AppProps {}
export default MySpeaker;
MySpeaker.displayName = 'MyApply';
