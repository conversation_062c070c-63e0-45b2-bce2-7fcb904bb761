/*
 * @Author: 李晋
 * @Date: 2021-10-21 16:55:01
 * @Email: <EMAIL>
 * @LastEditTime: 2023-09-25 11:48:18
 * @Description: file information
 * @Company: Sobey
 */

import RequestConstant from '@/constant/request';
import { request } from 'umi';

const host = window.location.hostname;
export namespace globalService {
  /**
   * 获取网站配置
   */
  export const fetchSetting = () =>
    request<Request.IUnifiedResponse<GlobalType.LoginConfig>>('/v1/setting/no/authorization', {
      params: {
        host,
      },
      prefix: RequestConstant.module.unifiedplatform,
    });
}
