import React, { useEffect, useState } from 'react';
import style from './index.less';
import Header from '../components/Header';
import { useQuery } from '@/hooks';
import { Button, Result, Spin, message } from 'antd';
import './reset.less';
import Access from '@/components/Access';
import { PlayCircleOutlined, StopOutlined } from '@ant-design/icons';
import { CreateService } from '@/api/CreateService';
import { LiveService } from '@/api/live';
import Request from '@/constant/request';
import { useImmer } from 'use-immer';

const Control: React.FC<ControlProps> = () => {
  // 引用计数
  const [loading, setLoading] = useImmer(1);

  const [isErr, setIsErr] = useState(false);
  const [schedule_status, setSchedule_status] = useState<number>();
  const { query } = useQuery<{
    id: string;
    isPlayback: string;
    type: Request.LIVE_TYPE;
  }>();

  const [btnLoading, setBtnLoading] = useState(false);
  useEffect(() => {
    getData();
  }, []);

  async function getData() {
    setLoading((arg) => {
      return arg + 1;
    });
    try {
      const { extend_message } = await CreateService.getDetail({
        _id: query.id,
      });
      setSchedule_status(extend_message.schedule_status);
    } catch (error) {}
    setLoading((arg) => {
      return arg - 1;
    });
  }
  return (
    <div className={style.control} id="live-control">
      <Header title="查看直播"></Header>
      <Access accessible={!isErr}>
        <Spin spinning={loading !== 0} tip="直播加载中...">
          <div className={style.frame}>
            <iframe
              onLoad={() => {
                setLoading((arg) => {
                  return arg - 1;
                });
              }}
              onError={() => {
                setIsErr(true);
                setLoading((arg) => {
                  return arg - 1;
                });
              }}
              src={`${window.location.origin}/learn/live/${
                query?.type?.toString() === Request.LIVE_TYPE.COURSE
                  ? 'course'
                  : 'activity'
              }/${query.id}?admin=true`}
            ></iframe>
            <Access accessible={Number(query.isPlayback) !== 1}>
              <footer>
                <Button
                  type="primary"
                  loading={btnLoading}
                  disabled={!schedule_status || schedule_status === 2}
                  icon={<PlayCircleOutlined />}
                  onClick={async () => {
                    setLoading((arg) => {
                      return arg + 1;
                    });
                    try {
                      const { error_code } =
                        await LiveService.updateLiveToStart(query.id);
                      if (error_code === Request.CODE.SUCCESS_LIVE_MANAGE) {
                        await getData();
                        message.success('开播成功');
                      }
                    } catch (error) {
                      message.error('开播失败');
                    }
                    setLoading((arg) => {
                      return arg - 1;
                    });
                  }}
                >
                  开播
                </Button>
                <Button
                  loading={btnLoading}
                  disabled={!schedule_status || schedule_status !== 2}
                  onClick={async () => {
                    setLoading((arg) => {
                      return arg + 1;
                    });
                    try {
                      const { error_code } = await LiveService.updateLiveToEnd(
                        query.id,
                      );
                      if (error_code === Request.CODE.SUCCESS_LIVE_MANAGE) {
                        await getData();
                        message.success('停止直播成功');
                      }
                    } catch (error) {
                      message.error('停止直播失败');
                    }
                    setLoading((arg) => {
                      return arg - 1;
                    });
                  }}
                  icon={<StopOutlined />}
                >
                  停止
                </Button>
              </footer>
            </Access>
          </div>
        </Spin>
      </Access>
      <Access accessible={isErr}>
        <Result
          status="error"
          title="直播加载错误"
          subTitle="请点击重试"
          extra={[
            <Button
              type="primary"
              key="console"
              onClick={() => {
                setIsErr(false);
                setLoading((arg) => {
                  arg++;
                });
              }}
            >
              重试
            </Button>,
          ]}
        />
      </Access>
    </div>
  );
};

interface ControlProps {}
export default Control;
Control.displayName = 'Control';
