/*
 * @Author: spring breeze
 * @Date: 2023-07-03 15:57:00
 * @LastEditTime: 2023-07-03 15:57:06
 * @FilePath: \frontend\src\pages\live\My\Apply\index.tsx
 * @Description:
 */
import React, { useEffect, useMemo, useRef, useState } from 'react';
import style from './index.less';
import {
  Button,
  Modal,
  Popconfirm,
  Popover,
  Radio,
  Select,
  Space,
  Tabs,
  TabsProps,
  Tooltip,
  message,
} from 'antd';
// 导入tab样式
import 'antd/lib/tabs/style/index.css';
import { Tab } from 'rc-tabs/lib/interface';
import {
  ActionType,
  ProColumns,
  ProTable,
  ProTableProps,
} from '@ant-design/pro-components';
import { useImmer } from 'use-immer';
import { CreateService } from '@/api/CreateService';
import { useQueryPagination } from '@/hooks';
import moment from 'moment';
import DetailModal, { DetailRefType } from './components/DetailModal';

type Item = CreateService.AuditPage;
type Params = {
  current?: number;
  pageSize?: number;
} & Item;

const Apply: React.FC<AppProps> = () => {
  const columns: ProColumns<Item>[] = [
    // {
    //   title: '申请类型',
    //   fieldProps: {
    //    placeholder: '请输入申请类型',
    //  },
    //   formItemProps: {
    //     label: null,
    //   },
    //  },
    {
      title: '直播名称',
      fieldProps: {
        placeholder: '请输入直播名称',
      },
      formItemProps: {
        label: null,
      },
      dataIndex: 'live_name',
    },
    {
      title: '申请人',
      search: false,
      dataIndex: 'apply_user_name',
    },

    {
      title: '申请时间',
      search: false,
      dataIndex: 'apply_time',
      renderText(text, record, index, action) {
        return moment(text * 1000).format('YYYY-MM-DD HH:mm:ss');
      },
    },
    {
      title: '处理状态',
      search: false,
      dataIndex: 'apply_status',
      valueEnum: {
        in_review: {
          text: '审核中',
          status: 'Processing',
        },
        not_pass: {
          text: '未通过',
          status: 'Error',
        },
        pass: {
          text: '已通过',
          status: 'Success',
        },
        draft: {
          text: '草稿',
          status: 'Default',
        },
      },
    },
    {
      title: '操作',
      valueType: 'option',
      width: 50,
      fixed: 'right',
      render: (_, record) => {
        const deleteBtnDisabled = false;
        return (
          <div key="operation" className={style.operation}>
            <Button
              type="link"
              onClick={() => {
                console.log(detailModalRef);
                detailModalRef.current?.setState({
                  readonly: true,
                  id: record._id,
                  open: true,
                  type: record.is_series ? 'series' : 'rapid',
                  hideModalFooter: true,
                  hideFormFooter: true,
                });
              }}
            >
              详情
            </Button>
            <Button
              type="link"
              disabled={record.apply_status !== 'in_review'}
              onClick={() => {
                detailModalRef.current?.setState({
                  readonly: true,
                  id: record._id,
                  open: true,
                  type: record.is_series ? 'series' : 'rapid',
                  hideModalFooter: false,
                  hideFormFooter: true,
                });
              }}
            >
              审核
            </Button>
            <Button
              type="link"
              onClick={() => {
                detailModalRef.current?.setState({
                  readonly: false,
                  id: record._id,
                  open: true,
                  type: record.is_series ? 'series' : 'rapid',
                  hideModalFooter: true,
                  hideFormFooter: false,
                });
              }}
            >
              修改
            </Button>
          </div>
        );
      },
    },
  ];

  const actionRef = useRef<ActionType>();
  const { pagination } = useQueryPagination();

  const [loading, setLoading] = useState(false);
  const detailModalRef = useRef<DetailRefType>(null);
  const tableConfig: ProTableProps<Item, Params> = {
    toolBarRender: false,
    search: {
      span: 4,
      showHiddenNum: true,
      labelWidth: 'auto',
      className: 'in-search',
      searchText: '搜索',
    },
    columns,
    rowKey: '_id',
    pagination,
    // : {
    //   // 因为basic分页重刷所以不能使用query
    //   className: 'hook-pagination',
    //   defaultPageSize: 10,
    //   showTotal: (total) => `共${total}条`,
    // }
    loading,
    actionRef,
    onLoadingChange(loading) {
      setLoading(loading as boolean);
    },

    async request(params, sort, filter) {
      try {
        console.log('发生请求', params);
        const { pageSize, current, live_name } = params;
        const { extend_message } = await CreateService.auditPage({
          page: current!,
          size: pageSize!,
          live_name,
          order_by: '-apply_time',
        });
        const { results, total } = extend_message;
        return {
          data: results,
          // success 请返回 true，
          // 不然 table 会停止解析数据，即使有数据
          success: true,
          // 不传会使用 data 的长度，如果是分页一定要传
          total,
        };
      } catch (error) {
        return {
          data: [],
          success: false,
        };
      }
    },
  };
  return (
    <div className={style.apply}>
      <ProTable {...tableConfig} />
      <DetailModal
        actionRef={detailModalRef}
        onSuccess={() => {
          actionRef.current?.reload();
        }}
      />
    </div>
  );
};

interface AppProps {}
export default Apply;
Apply.displayName = 'MyApply';
