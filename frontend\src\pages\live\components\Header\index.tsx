import React from 'react';
import style from './index.less';
import { LeftOutlined } from '@ant-design/icons';
import { history } from 'umi';

const Header: React.FC<HeaderProps> = ({ title }) => {
  return (
    <header className={style.header}>
      <span
        role="button"
        onClick={() => {
          history.goBack();
        }}
      >
        <LeftOutlined className={style.icon} />

        {title}
      </span>
    </header>
  );
};

interface HeaderProps {
  title: string;
}
export default Header;
Header.displayName = 'Header';
