/*
 * @Author: spring breeze
 * @Date: 2023-06-21 10:47:30
 * @LastEditTime: 2023-06-21 14:46:02
 * @FilePath: \frontend\src\plugins\PlayerUIPlugin.ts
 * @Description: 西瓜播放器的ui插件
 */
// demoBasePlugin.js
import React from 'react';
import { Events, Plugin } from 'xgplayer';
const { POSITIONS } = Plugin;
import css from './index.less';

export class PlayerUIPlugin extends Plugin {
  /**
   * （必须声明）插件的名称，将作为插件实例的唯一key值
   * 该参数还最为播放器上该插件的配置透传key值，例如：
   * var p = new player({
   *   demoBasePlugin: {
   *     text: '这是插件demoBasePlugin的配置信息'
   *   }
   * })
   * 在插件afterCreate之后可以通过this.config.text获取到改配置参数
   **/
  static get pluginName() {
    return 'UIPlugin';
  }

  static get defaultConfig() {
    return {
      text: '这是插件demoBasePlugin的默认Text',
      // 挂载在controls的右侧，如果不指定则默认挂载在播放器根节点上
      // position: POSITIONS.ROOT_TOP,
    };
  }

  constructor(args: any) {
    super(args);
  }

  // afterPlayerInit() {
  //   // TODO 播放器调用start初始化播放源之后的逻辑
  //   console.log('播放器初始化之后的逻辑');
  // }

  afterCreate() {
    // 在afterCreate中可以加入DOM的事件监听
    this.on(
      PlayerUIPluginEvents.UI_TEXT_CHANGE,
      ({ text, className, show = true }: PlayerUIPluginEventsData) => {
        const html = show
          ? `
        <div class="${css.box} ${className}">
          <span>${text}</span>
        </div>
     `
          : '';
        this.setHtml(html);
      },
    );
    const { style = {} } = this.config ?? {};
    const configStyle: React.CSSProperties = {
      color: 'white',
      position: 'relative',
      padding: '3%',
      zIndex: 1,
      display: 'flex',
      flexDirection: 'row-reverse',
      ...style,
    };
    //@ts-ignore
    this.setStyle(configStyle);
  }

  destroy() {
    // 播放器销毁的时候一些逻辑
  }
  render() {
    return `<div class="ui-plugin"></div>`;
  }
}

export enum PlayerUIPluginEvents {
  UI_TEXT_CHANGE = 'ui_text_change',
}
export type PlayerUIPluginEventsData = Partial<{
  text?: string;
  icon?: string;
  className?: string;
  show?: boolean;
}>;

// color: white;
//     z-index: 1;
//     width: auto;
//     height: auto;
//     margin-top: 20px;
//     position: relative;
