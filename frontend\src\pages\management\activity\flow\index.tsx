import React, { useEffect, useState } from 'react';
import '../index.less';
import {
  Popconfirm,
  message,
  Select,
  Space,
  Input,
  Button,
  Table,
  Modal,
  Form,
  Radio,
} from 'antd';
import activityManagementService from '@/service/activityManagementService';
import config from '@/utils/config';
import ActivityConstant from '@/constant/activity';
import moment from 'moment';
import { IconFont } from '@/components';

type AlignType = 'left' | 'center' | 'right' | undefined;

interface IPager {
  page: number;
  size: number;
}

const Flow = () => {
  const [flowList, setFlowList] = useState<
    (ActivityType.IFlow & { key: string })[]
  >([]);
  const [deleteIds, setDeleteIds] = useState<React.Key[]>([]);
  const [flowName, setFlowName] = useState('');

  const [isModalVisible, setIsModalVisible] = useState(false);
  const [modelType, setModelType] = useState(
    ActivityConstant.flowModelType.add,
  );
  const [form] = Form.useForm();
  const [pager, setPager] = useState<IPager>({
    page: 1,
    size: config.defaultPageSize,
  });
  const [total, setTotal] = useState(0);
  const [liveType, setLiveType] = useState<string>('');
  const [orderBy, setOrderBy] = useState('create_time');
  const [sort, setSort] = useState('0');
  useEffect(() => {
    handleSearchByName();
  }, [pager, orderBy, sort]);

  useEffect(() => {
    console.log('liveType:' + liveType);
    if (liveType != '' && modelType === ActivityConstant.flowModelType.add) {
      handleGetAddr();
    }
  }, [liveType]);

  const ORDER_OPTIONS = [
    {
      label: '创建时间',
      value: 'create_time',
    },
  ];
  const SORT_OPTIONS = [
    {
      label: '升序',
      value: '1',
    },
    {
      label: '降序',
      value: '0',
    },
  ];

  const handleDelete = async (_id: string) => {
    const res = await activityManagementService.deleteFlowBulk([_id]);
    if (res.error_code === config.successCode) {
      message.success('删除成功');
      setDeleteIds([]);
      setPager({ ...pager, page: 1 });
    }
  };

  const handleBulkDelete = async () => {
    const res = await activityManagementService.deleteFlowBulk(deleteIds);
    if (res.error_code === config.successCode) {
      message.success('删除成功');
      setDeleteIds([]);
      setPager({ ...pager, page: 1 });
    }
  };

  const handleSearchByName = async () => {
    const res = await activityManagementService.fetchFlowsByName(
      flowName,
      pager.page,
      pager.size,
      sort === '1' ? orderBy : '-' + orderBy,
    );
    if (res.error_code === config.successCode) {
      setTotal(res?.extend_message?.total ?? 0);
      setFlowList(
        res?.extend_message?.results?.map((item: ActivityType.IFlow) => ({
          ...item,
          key: item._id,
        })),
      );
    }
  };

  const handleAddFlow = async (values: ActivityType.IFlowForm) => {
    console.log(values, 1111111111111);
    const res = await activityManagementService.addFlow(
      values.stream_name,
      values.stream_address,
      values.push_url,
      values.pull_url,
      values.is_mute,
    );
    if (res.error_code === config.successCode) {
      handleSearchByName();
      message.success('添加成功');
      setIsModalVisible(false);
    } else {
      message.error('添加失败');
    }
  };

  const handleGetAddr = async () => {
    if (liveType != '') {
      // 再判断 地址是不是当前所选格式
      // let stream_address = form.getFieldValue('stream_address');
      // if(stream_address && (stream_address.includes('.flv') || stream_address.includes('.m3u8'))){
      //   if(stream_address.includes('.flv')){
      //     stream_address = stream_address.replace(new RegExp(/.flv/g),'.m3u8')
      //   }else if(stream_address.includes('.m3u8')){
      //     stream_address = stream_address.replace(new RegExp(/.m3u8/g),".flv");
      //   }else{

      //   }
      //   form.setFieldsValue({
      //     stream_address: stream_address
      //   })
      // }else{
      const res = await activityManagementService.fetchFlowAddrAuto(liveType);
      if (res.error_code === config.successCodeUniform) {
        form.setFieldsValue({
          stream_address: res?.extend_message?.play_url,
          push_url: res?.extend_message?.push_url,
          pull_url: res?.extend_message?.pull_url,
        });
      }
      // }
    }
  };

  const handleToggleStatus = async (_id: string, stream_status: boolean) => {
    const res = await activityManagementService.updateFlowStatus(
      _id,
      stream_status,
    );
    if (res.error_code === config.successCode) {
      message.success(`${stream_status ? '启用' : '禁用'}成功`);
      handleSearchByName();
    }
  };

  const handleUpdateFlow = async (values: ActivityType.IFlowForm) => {
    const res = await activityManagementService.updateFlow(values);
    if (res.error_code === config.successCode) {
      message.success('修改成功');
      handleSearchByName();
      setIsModalVisible(false);
    }
  };

  const rowSelection = {
    onChange: (selectedRowKeys: React.Key[]) => {
      setDeleteIds(selectedRowKeys);
    },
  };
  const liveTypeChange = (item: any) => {
    console.log(item.target?.value);
    setLiveType(item.target?.value);
  };
  const columns = [
    {
      title: '名称',
      dataIndex: 'stream_name',
      key: 'stream_name',
      align: 'left',
      width: '12%',
      render: (value: string) => {
        return <span title={value}>{value}</span>;
      },
    },
    {
      title: '视频流地址',
      dataIndex: 'stream_address',
      key: 'stream_address',
      align: 'left',
      width: '25%',
      ellipsis: true,
      render: (value: string) => {
        return <span title={value}>{value}</span>;
      },
    },
    {
      title: '推流地址',
      dataIndex: 'push_url',
      key: 'push_url',
      align: 'left',
      width: '30%',
      ellipsis: true,
      render: (value: string) => {
        return <span title={value}>{value}</span>;
      },
    },
    {
      title: '状态',
      dataIndex: 'stream_status',
      key: 'stream_status',
      align: 'left',
      width: '8%',
      render: (status: boolean) => {
        if (status) {
          return <div>启用</div>;
        } else {
          return <div>禁用</div>;
        }
      },
    },
    {
      title: '是否有声音',
      dataIndex: 'is_mute',
      key: 'is_mute',
      align: 'center',
      width: '8%',
      render: (status: boolean) => {
        if (status) {
          return <div>是</div>;
        } else {
          return <div>否</div>;
        }
      },
    },
    {
      title: '创建时间',
      dataIndex: 'create_time',
      key: 'create_time',
      align: 'left',
      width: '12%',
      render: (time: number) => {
        return time && moment.unix(time).format('YYYY-MM-DD HH:mm:ss');
      },
    },
    {
      title: '操作',
      dataIndex: '_id',
      align: 'center',
      width: '10%',
      render: (_id: string, record: ActivityType.IFlow) => {
        return (
          <Space>
            <Button
              type="link"
              onClick={() => {
                handleDelete(_id);
              }}
            >
              删除
            </Button>
            <Button
              type="link"
              onClick={() => {
                setLiveType('');
                setLiveType(
                  record?.stream_address.split('.')[
                    record?.stream_address.split('.').length - 1
                  ],
                );
                form.setFieldsValue({
                  _id: record?._id,
                  stream_name: record?.stream_name,
                  stream_address: record?.stream_address,
                  push_url: record?.push_url,
                  pull_url: record?.pull_url,
                  is_mute: record?.is_mute,
                });
                setModelType(ActivityConstant.flowModelType.update);
                setIsModalVisible(true);
              }}
            >
              编辑
            </Button>
            <Button
              type="link"
              onClick={() => {
                handleToggleStatus(_id, !record?.stream_status);
              }}
            >
              {record?.stream_status ? '禁用' : '启用'}
            </Button>
          </Space>
        );
      },
    },
  ];

  return (
    <div className="activity_live">
      <div className="activity_head_wrapper">
        <div className="activity_search">
          <Space>
            <Input
              allowClear
              placeholder="请输入直播流名称"
              value={flowName}
              onChange={(e) => setFlowName(e.target.value)}
              onKeyDown={(e) => {
                if (e.keyCode === 13) {
                  setPager({ ...pager, page: 1 });
                }
              }}
            />
            <Button
              type="primary"
              onClick={() => setPager({ ...pager, page: 1 })}
            >
              搜索
            </Button>
            <div className="cd_filter_wrapper">
              <Select
                className="order_select"
                options={ORDER_OPTIONS}
                value={orderBy}
                onChange={(orderBy) => {
                  setOrderBy(orderBy);
                }}
                suffixIcon={<IconFont type="iconshengjiang" />}
              />
              <Select
                className="sort_select"
                options={SORT_OPTIONS}
                suffixIcon={<IconFont type="iconshengjiang" />}
                value={sort}
                onChange={(sort) => {
                  setSort(sort);
                }}
              />
            </div>
          </Space>
        </div>
        <div className="activity_opt">
          <Space>
            <Popconfirm
              title="确认删除？"
              disabled={deleteIds.length === 0}
              onConfirm={() => {
                handleBulkDelete();
              }}
            >
              <Button type="primary" disabled={deleteIds.length === 0}>
                批量删除
              </Button>
            </Popconfirm>
            <Button
              type="primary"
              onClick={() => {
                form.resetFields();
                setModelType(ActivityConstant.flowModelType.add);
                setLiveType(config.vedioType[0]);
                setIsModalVisible(true);
                handleGetAddr();
              }}
            >
              创建直播流
            </Button>
          </Space>
        </div>
      </div>
      <div className="activity_main_wrapper">
        <Table
          rowSelection={{ type: 'checkbox', ...rowSelection }}
          columns={columns as any}
          dataSource={flowList}
          pagination={{
            total: total,
            pageSize: pager.size,
            current: pager.page,
            showQuickJumper: true,
            showSizeChanger: true,
            onChange: (page, pageSize) => {
              setPager({
                page: page,
                size: pageSize ?? 0,
              });
            },
          }}
        />
      </div>
      <Modal
        title={
          modelType === ActivityConstant.flowModelType.add
            ? '创建直播流'
            : '修改直播流地址'
        }
        visible={isModalVisible}
        onOk={() => form.submit()}
        onCancel={() => setIsModalVisible(false)}
      >
        <Form
          form={form}
          onFinish={(values) => {
            if (modelType === ActivityConstant.flowModelType.add) {
              handleAddFlow(values);
            } else {
              handleUpdateFlow(values);
            }
          }}
          labelCol={{ span: 5 }}
          initialValues={{
            is_mute: false,
          }}
        >
          <Form.Item label="id" name="_id" noStyle hidden></Form.Item>
          <Form.Item
            label="直播流名称"
            name="stream_name"
            rules={[{ required: true, message: '输入直播流名称' }]}
          >
            <Input />
          </Form.Item>
          <Form.Item label="直播流格式">
            <Radio.Group
              onChange={liveTypeChange}
              key={liveType}
              defaultValue={liveType}
            >
              {config.vedioType.map((item: any, index: number) => {
                return (
                  <Radio key={index} value={item}>
                    {item}
                  </Radio>
                );
              })}
            </Radio.Group>
            {/* <Button
              type="primary"
              onClick={handleGetAddr}
              disabled={modelType !== ActivityConstant.flowModelType.add}
            >
              自动生成直播地址
            </Button> */}
          </Form.Item>
          <Form.Item
            label="视频流地址"
            name="stream_address"
            rules={[
              { required: true, message: '输入直播流地址' },
              {
                // pattern: new RegExp(/^(https?:\/\/).*\.(flv)$/),
                // pattern: new RegExp("^(https?:\/\/).*\.("+config.vedioType.join('|')+")$"),
                pattern: new RegExp('^(http(s)?://).*.(' + liveType + ')'),
                // message: '请输入flv格式地址'
                // message: `请输入${config.vedioType.join('/')}格式地址`
                message: `请输入${liveType}格式地址`,
              },
            ]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            label="推流地址"
            name="push_url"
            rules={[
              { required: true, message: '输入推流地址' },
              {
                message: `请输入正确格式地址`,
                // rtmp ,http 或 https开头 和liveType无关
                pattern: new RegExp('^(rtmp|http(s)?://).*'),
              },
            ]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            label="拉流地址"
            name="pull_url"
            rules={[
              { required: true, message: '输入拉流地址' },
              {
                message: `请输入正确格式地址`,
                // rtmp ,http 或 https开头 和liveType无关
                pattern: new RegExp('^(rtmp|http(s)?://).*'),
              },
            ]}
          >
            <Input />
          </Form.Item>
          <Form.Item label="是否有声音" name="is_mute">
            <Radio.Group>
              <Radio value={true}>是</Radio>
              <Radio value={false}>否</Radio>
            </Radio.Group>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default Flow;
